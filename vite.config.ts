import path from "path";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

export default defineConfig({
	plugins: [react(), tailwindcss()],
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "./src"),
		},
	},
	preview: {
		allowedHosts: ["clyp-web-dev-6d362c890ff9.herokuapp.com", "app.clyppay.io"],
	},
	server: {
		port: 4500,
	},
});
