{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/account-card.tsx", "./src/components/coming-soon.tsx", "./src/components/create-pin-success.tsx", "./src/components/create-pin.tsx", "./src/components/dropdown-selector.tsx", "./src/components/enhanced-select.tsx", "./src/components/enter-transaction-pin.tsx", "./src/components/generic-list.tsx", "./src/components/icons.tsx", "./src/components/navbar.tsx", "./src/components/protected-dashboard-route.tsx", "./src/components/send-crypto-drawer.tsx", "./src/components/send-crypto-form.tsx", "./src/components/sidebar.tsx", "./src/components/social-login.tsx", "./src/components/test-modal.tsx", "./src/components/theme-provider.tsx", "./src/components/theme-toggle.tsx", "./src/components/transaction-summary-card.tsx", "./src/components/with-auth-check.tsx", "./src/components/crypto-purchase/transaction-success.tsx", "./src/components/custom/button/index.tsx", "./src/components/custom/card/auth-card.tsx", "./src/components/custom/input/auth-input.tsx", "./src/components/custom/input/checkbox.tsx", "./src/components/custom/input/floating-label-input.tsx", "./src/components/custom/input/otp-input.tsx", "./src/components/custom/input/select-input.tsx", "./src/components/drawer-view/container.tsx", "./src/components/drawer-view/use-drawer.ts", "./src/components/loaders/account-balance-skeleton.tsx", "./src/components/loaders/full-transaction-skeleton.tsx", "./src/components/loaders/quick-actions-skeleton.tsx", "./src/components/loaders/recent-transactions-skeleton.tsx", "./src/components/modal-view/container.tsx", "./src/components/modal-view/use-modal.tsx", "./src/components/shadcn/input-otp.tsx", "./src/components/transaction/transaction-pin-manager.tsx", "./src/components/transaction/transaction-receipt.tsx", "./src/components/transaction/transaction-success.tsx", "./src/components/ui/button.tsx", "./src/components/ui/drawer-new.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/modal.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/select.tsx", "./src/components/ui/skeleton.tsx", "./src/config/api-routes.ts", "./src/config/axios.ts", "./src/config/enums.ts", "./src/config/env.ts", "./src/config/index.ts", "./src/config/routes.ts", "./src/config/constants/index.ts", "./src/config/constants/query-keys.ts", "./src/config/providers/idle-timer-provider.tsx", "./src/config/providers/index.tsx", "./src/config/providers/react-query-provider.tsx", "./src/data/index.ts", "./src/hooks/use-debounce.ts", "./src/hooks/use-media-query.ts", "./src/hooks/use-sidebar.ts", "./src/hooks/api/auth.ts", "./src/hooks/api/bills.ts", "./src/hooks/api/blog-post.ts", "./src/hooks/api/cards.ts", "./src/hooks/api/courses.ts", "./src/hooks/api/crypto-banking.ts", "./src/hooks/api/crypto-portfolio.ts", "./src/hooks/api/crypto-savings.ts", "./src/hooks/api/crypto-wallets.ts", "./src/hooks/api/crypto.ts", "./src/hooks/api/fiat-banking.ts", "./src/hooks/api/fiat-savings.ts", "./src/hooks/api/fiat-wallets.ts", "./src/hooks/api/fiat.ts", "./src/hooks/api/index.ts", "./src/hooks/api/price-alerts.ts", "./src/hooks/api/swaps.ts", "./src/hooks/api/use-savings.ts", "./src/hooks/api/user-gateway.ts", "./src/hooks/api/user.ts", "./src/hooks/api/usercryptotransactions.ts", "./src/hooks/api/userfiattransactions.ts", "./src/hooks/api/usertransactions.ts", "./src/layouts/auth-layout.tsx", "./src/layouts/dashboard-layout.tsx", "./src/layouts/global-layout.tsx", "./src/layouts/section-container.tsx", "./src/lib/utils.ts", "./src/pages/not-found.tsx", "./src/pages/auth/confirm-email.tsx", "./src/pages/auth/create-new-password.tsx", "./src/pages/auth/forgot-password.tsx", "./src/pages/auth/login.tsx", "./src/pages/auth/register.tsx", "./src/pages/auth/reset-password.tsx", "./src/pages/auth/verify-email.tsx", "./src/pages/dashbaord/clyp-news.tsx", "./src/pages/dashbaord/clyphub-actions.tsx", "./src/pages/dashbaord/clyphub.tsx", "./src/pages/dashbaord/drawer.tsx", "./src/pages/dashbaord/home.tsx", "./src/pages/dashbaord/actions/cryptoloan.tsx", "./src/pages/dashbaord/actions/fiatloan.tsx", "./src/pages/dashbaord/actions/loans.tsx", "./src/pages/dashbaord/actions/request-crypto-loan.tsx", "./src/pages/dashbaord/actions/lifestyle/airtime.tsx", "./src/pages/dashbaord/actions/lifestyle/lifestyle.tsx", "./src/pages/dashbaord/profile/page.tsx", "./src/pages/dashbaord/reports/coin-details-drawer.tsx", "./src/pages/dashbaord/reports/coin-statistics.tsx", "./src/pages/dashbaord/reports/reports-page.tsx", "./src/pages/dashbaord/reports/transaction-logs.tsx", "./src/pages/dashbaord/reports/web3.tsx", "./src/pages/savings/success.tsx", "./src/pages/savings/auto-save/auto-save-card.tsx", "./src/pages/savings/auto-save/auto-save-details.tsx", "./src/pages/savings/auto-save/auto-save-form.tsx", "./src/pages/savings/auto-save/index.tsx", "./src/pages/savings/safe-lock/duration.tsx", "./src/pages/savings/safe-lock/index.tsx", "./src/pages/savings/safe-lock/safe-lock-card.tsx", "./src/pages/savings/safe-lock/safe-lock-dashboard.tsx", "./src/pages/savings/safe-lock/safe-lock-details.tsx", "./src/pages/savings/safe-lock/safe-lock-flow.tsx", "./src/pages/savings/safe-lock/safe-lock-form.tsx", "./src/pages/savings/safe-lock/safe-lock-preview.tsx", "./src/pages/savings/safe-lock/safe-lock-success.tsx", "./src/pages/savings/safe-lock/withdraw-safe-lock-drawer.tsx", "./src/routes/index.tsx", "./src/services/auth.ts", "./src/services/bills.ts", "./src/services/blog-post.ts", "./src/services/card.ts", "./src/services/crypto-banking.ts", "./src/services/crypto-portfolio.ts", "./src/services/crypto-savings.ts", "./src/services/crypto-wallets.ts", "./src/services/crypto.ts", "./src/services/fiat-banking.ts", "./src/services/fiat-savings.ts", "./src/services/fiat-wallets.ts", "./src/services/fiat.ts", "./src/services/price-alerts.ts", "./src/services/savings.ts", "./src/services/swap.ts", "./src/services/user-gateway.ts", "./src/services/user.ts", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>", "./src/shared/home/<USER>/card-item.tsx", "./src/shared/home/<USER>/change-design-section.tsx", "./src/shared/home/<USER>/create-virtual-card-form.tsx", "./src/shared/home/<USER>/index.tsx", "./src/shared/home/<USER>/manage-card-section.tsx", "./src/shared/home/<USER>/select-currency.tsx", "./src/shared/home/<USER>/transaction-item.tsx", "./src/shared/home/<USER>/view-transaction-section.tsx", "./src/shared/home/<USER>/manage-card/activate-face-id.tsx", "./src/shared/home/<USER>/manage-card/block-card.tsx", "./src/shared/home/<USER>/manage-card/change-card-pin.tsx", "./src/shared/home/<USER>/manage-card/change-funding-source.tsx", "./src/shared/home/<USER>/manage-card/manage-card-section.tsx", "./src/shared/home/<USER>/manage-card/manage-card-settings.tsx", "./src/shared/home/<USER>/manage-card/pause-transactions.tsx", "./src/shared/home/<USER>/manage-card/rename-card.tsx", "./src/shared/home/<USER>/manage-card/resume-transactions.tsx", "./src/shared/home/<USER>/buy/buy-crypto-step.tsx", "./src/shared/home/<USER>/buy/index.tsx", "./src/shared/home/<USER>/buy/preview-purchase-step.tsx", "./src/shared/home/<USER>/buy/receipt-step.tsx", "./src/shared/home/<USER>/buy/swap-pin-manager.tsx", "./src/shared/home/<USER>/receive/index.tsx", "./src/shared/home/<USER>/receive/select-dropdown.tsx", "./src/shared/home/<USER>/receive/select-step.tsx", "./src/shared/home/<USER>/receive/static-deposit-step.tsx", "./src/shared/home/<USER>/receive/universal-deposit-details-step.tsx", "./src/shared/home/<USER>/receive/universal-deposit-form-step.tsx", "./src/shared/home/<USER>/sell/index.tsx", "./src/shared/home/<USER>/sell/preview-sales-step.tsx", "./src/shared/home/<USER>/sell/receipt-step.tsx", "./src/shared/home/<USER>/sell/sell-crypto-step.tsx", "./src/shared/home/<USER>/sell/swap-pin-manager.tsx", "./src/shared/home/<USER>/send/confirm-transaction.tsx", "./src/shared/home/<USER>/send/index.tsx", "./src/shared/home/<USER>/send/initiate-send.tsx", "./src/shared/home/<USER>/send/network-selector.tsx", "./src/shared/home/<USER>/send/preview-send-step.tsx", "./src/shared/home/<USER>/send/qr-scanner.tsx", "./src/shared/home/<USER>/send/receipt-step.tsx", "./src/shared/home/<USER>/send/send-crypto-form.tsx", "./src/shared/home/<USER>/send/send-crypto-pin-manager.tsx", "./src/shared/home/<USER>/send/send-crypto-step.tsx", "./src/shared/home/<USER>/send/send-crypto-steps.tsx", "./src/shared/home/<USER>/send/transaction-receipt.tsx", "./src/shared/home/<USER>/send/transaction-success.tsx", "./src/shared/home/<USER>/swap/index.tsx", "./src/shared/home/<USER>/swap/preview-conversion.tsx", "./src/shared/home/<USER>/swap/transaction-receipt.tsx", "./src/shared/home/<USER>/swap/transaction-success.tsx", "./src/shared/home/<USER>/swap/internal-swap/index.tsx", "./src/shared/home/<USER>/swap/internal-swap/preview-swap-step.tsx", "./src/shared/home/<USER>/swap/internal-swap/receipt-step.tsx", "./src/shared/home/<USER>/swap/internal-swap/swap-crypto-step.tsx", "./src/shared/home/<USER>/swap/internal-swap/swap-pin-manager.tsx", "./src/shared/home/<USER>/convert/convert-fiat-receipt.tsx", "./src/shared/home/<USER>/convert/convert-fiat.tsx", "./src/shared/home/<USER>/convert/convert-pin-manager.tsx", "./src/shared/home/<USER>/convert/index.tsx", "./src/shared/home/<USER>/convert/preview-convert-fiat.tsx", "./src/shared/home/<USER>/deposit/index.tsx", "./src/shared/home/<USER>/deposit/payment-link-deposit.tsx", "./src/shared/home/<USER>/deposit/preview-deposit.tsx", "./src/shared/home/<USER>/deposit/process-payment-link.tsx", "./src/shared/home/<USER>/deposit/select-deposit-method.tsx", "./src/shared/home/<USER>/deposit/temporary-account-deposit.tsx", "./src/shared/home/<USER>/deposit/temporary-account-details.tsx", "./src/shared/home/<USER>/withdraw/index.tsx", "./src/shared/home/<USER>/withdraw/preview-withdrawal.tsx", "./src/shared/home/<USER>/withdraw/select-withdrawal-method.tsx", "./src/shared/home/<USER>/withdraw/transaction-receipt.tsx", "./src/shared/home/<USER>/withdraw/transaction-success.tsx", "./src/shared/home/<USER>/withdraw/withdrawal-pin-manager.tsx", "./src/shared/home/<USER>/withdraw/bank-withdrawal/index.tsx", "./src/shared/home/<USER>/withdraw/internal-transfer/index.tsx", "./src/shared/home/<USER>/savings-goal-card.tsx", "./src/shared/home/<USER>/create-savings-goal.tsx", "./src/shared/home/<USER>/fund-savings-success.tsx", "./src/shared/home/<USER>/savings-goal-card-skeleton.tsx", "./src/shared/home/<USER>/savings-goal-details.tsx", "./src/shared/home/<USER>/savings-success.tsx", "./src/shared/home/<USER>/auto-save/auto-save-card.tsx", "./src/shared/home/<USER>/auto-save/index.tsx", "./src/shared/home/<USER>/fund/enter-transaction-pin.tsx", "./src/shared/home/<USER>/fund/fund-savings-amount-form.tsx", "./src/shared/home/<USER>/fund/fund-savings-success.tsx", "./src/shared/home/<USER>/fund/index.tsx", "./src/shared/home/<USER>/fund/transaction-receipt.tsx", "./src/shared/home/<USER>/withdraw/index.tsx", "./src/shared/home/<USER>/withdraw/withdraw-savings-amount-form.tsx", "./src/shared/home/<USER>/withdraw/withdraw-savings-success.tsx", "./src/shared/home/<USER>/account-view.tsx", "./src/shared/home/<USER>/cards-view.tsx", "./src/shared/home/<USER>/savings-view.tsx", "./src/store/buy-crypto-store.ts", "./src/store/convert-fiat-store.ts", "./src/store/create-card-store.ts", "./src/store/deposit-fiat-store.ts", "./src/store/receive-crypto-store.ts", "./src/store/safe-lock-store.ts", "./src/store/sell-crypto-store.ts", "./src/store/send-crypto-store.ts", "./src/store/swap-crypto-store.ts", "./src/store/theme-store.ts", "./src/store/usegoalstore.ts", "./src/store/usewithdrawgoalstore.ts", "./src/store/user-store.ts", "./src/store/withdrawal-fiat-store.ts", "./src/types/auth.ts", "./src/types/bills.ts", "./src/types/cards.ts", "./src/types/crypto-banking.ts", "./src/types/crypto-portfolio.ts", "./src/types/crypto-savings.ts", "./src/types/crypto-wallets.ts", "./src/types/crypto.ts", "./src/types/fiat-banking.ts", "./src/types/fiat-savings.ts", "./src/types/fiat-wallets.ts", "./src/types/general.ts", "./src/types/index.ts", "./src/types/send-crypto.ts", "./src/types/swaps.ts", "./src/types/user.ts", "./src/types/wallet.ts", "./src/utils/calculate-resolved-amount.ts", "./src/utils/class-names.ts", "./src/utils/error-handler.ts", "./src/utils/format-amount.ts", "./src/utils/format-currency.ts", "./src/utils/format-date.tsx", "./src/utils/notify.ts", "./src/utils/parse-currency-symbol.ts", "./src/utils/parse-formatted-number.ts", "./src/utils/validators/common-rules.ts", "./src/utils/validators/forget-password.schema.ts", "./src/utils/validators/login.schema.ts", "./src/utils/validators/otp-code.schema.ts", "./src/utils/validators/transaction-pin.schema.ts"], "version": "5.7.3"}