@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Nunito:wght@300;400;600&display=swap");

@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

:root {
	--background: hsl(0 0% 100%);
	--foreground: hsl(0 0% 3.9%);
	--card: hsl(0 0% 100%);
	--card-foreground: hsl(0 0% 3.9%);
	--popover: hsl(0 0% 100%);
	--popover-foreground: hsl(0 0% 3.9%);
	--primary: #ea9924;
	--primary-foreground: hsl(0 0% 98%);
	--secondary: hsl(0 0% 96.1%);
	--secondary-foreground: hsl(0 0% 9%);
	--muted: hsl(0 0% 96.1%);
	--muted-foreground: hsl(0 0% 45.1%);
	--accent: hsl(0 0% 96.1%);
	--accent-foreground: hsl(0 0% 9%);
	--destructive: hsl(0 84.2% 60.2%);
	--destructive-foreground: hsl(0 0% 98%);
	--border: hsl(0 0% 89.8%);
	--input: hsl(0 0% 89.8%);
	--ring: hsl(0 0% 3.9%);
	--chart-1: hsl(12 76% 61%);
	--chart-2: hsl(173 58% 39%);
	--chart-3: hsl(197 37% 24%);
	--chart-4: hsl(43 74% 66%);
	--chart-5: hsl(27 87% 67%);
	--radius: 0.6rem;
}

.dark {
	--background: hsl(0 0% 3.9%);
	--foreground: hsl(0 0% 98%);
	--card: hsl(0 0% 3.9%);
	--card-foreground: hsl(0 0% 98%);
	--popover: hsl(0 0% 3.9%);
	--popover-foreground: hsl(0 0% 98%);
	--primary: #ea9924;
	--primary-foreground: hsl(0 0% 98%);
	--secondary: hsl(0 0% 14.9%);
	--secondary-foreground: hsl(0 0% 98%);
	--muted: hsl(0 0% 14.9%);
	--muted-foreground: hsl(0 0% 63.9%);
	--accent: hsl(0 0% 14.9%);
	--accent-foreground: hsl(0 0% 98%);
	--destructive: hsl(0 62.8% 30.6%);
	--destructive-foreground: hsl(0 0% 98%);
	--border: hsl(0 0% 14.9%);
	--input: hsl(0 0% 14.9%);
	--ring: hsl(0 0% 83.1%);
	--chart-1: hsl(220 70% 50%);
	--chart-2: hsl(160 60% 45%);
	--chart-3: hsl(30 80% 55%);
	--chart-4: hsl(280 65% 60%);
	--chart-5: hsl(340 75% 55%);
}

@theme inline {
	--font-heading: "Montserrat", sans-serif;
	--font-body: "Nunito", sans-serif;
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;
	--animate-caret-blink: caret-blink 1.25s ease-out infinite;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}

	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}

	@keyframes caret-blink {
		0%,
		70%,
		100% {
			opacity: 1;
		}
		20%,
		50% {
			opacity: 0;
		}
	}

	/* Custom animation for loading progress bar */
	@keyframes loading {
		0% {
			transform: translateX(-100%);
		}
		50% {
			transform: translateX(0%);
		}
		100% {
			transform: translateX(100%);
		}
	}
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		@apply font-heading tracking-tighter;
	}

	p,
	span {
		@apply font-body;
	}
}

@layer utilities {
	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	.scrollbar-hide {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}

	.whitecard-overlay {
		position: absolute;
		color: #222;
		font-weight: 600;
		font-size: 1rem;
		pointer-events: none;
		/* You can add more styles or override per usage */
	}
}

/* Custom animation for loading progress bar */
@keyframes loading {
	0% {
		transform: translateX(-100%);
	}
	50% {
		transform: translateX(0%);
	}
	100% {
		transform: translateX(100%);
	}
}

html {
	font-family: "Nunito", sans-serif;
}
