export const routes = {
    dashboard: "/",
    update: {
      list: "/update",
      details: (id: string) => `/update/${id}`,
    },
    transactions: "/transactions",
    finance_request: "/finance-request",
    business: {
      list: "/business",
      details: (id: string) => `/business/${id}`,
    },
    users: {
      list: "/collaborators",
      details: (id: string) => `/collaborators/${id}`,
    },
    organisation_users: (id: string) => `/business/${id}/organisation-users`,
    vendor: (id: string) => `/business/${id}/vendor`,
  
    subscription: "/subscription",
  
    auth: {
      signIn: "/login",
      verifyAccount: "/verify-account",
      forgotPassword: "/forgot-password",
      resetPassword: "/reset-password",
    },
    accessDenied: "/access-denied",
    notFound: "/not-found",
  };
  