import { useState, useEffect, useRef } from "react";
import { useIdleTimer } from "react-idle-timer";
import { Button } from "@/components/ui/button";
import { Clock, LogOut } from "lucide-react";
import { useModal } from "@/components/modal-view/use-modal";
import { useNavigate } from "react-router-dom";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";

interface IdleTimeoutWrapperProps {
	children: React.ReactNode;
	timeout?: number;
	promptBeforeIdle?: number;
	onSessionExtended?: () => void;
}

export const IdleTimeoutWrapper = ({
	children,
	timeout = 1000 * 60 * 300,
	promptBeforeIdle = 1000 * 60 * 1,
	onSessionExtended,
}: IdleTimeoutWrapperProps) => {
	const navigate = useNavigate();
	const { openModal, closeModal } = useModal();

	const handleLogoutRef = useRef<() => void>(() => {});
	const handleOnIdleRef = useRef<() => void>(() => {});
	const handleOnPromptRef = useRef<() => void>(() => {});
	const handleContinueSessionRef = useRef<() => void>(() => {});

	const { activate } = useIdleTimer({
		onIdle: () => handleOnIdleRef.current(),
		onPrompt: () => handleOnPromptRef.current(),
		timeout: timeout,
		promptBeforeIdle: promptBeforeIdle,
		crossTab: true,
		debounce: 500,
	});

	useEffect(() => {
		handleLogoutRef.current = () => {
			closeModal();
			localStorage.removeItem("auth_token");
			notify(
				"You have been logged out due to inactivity",
				ResponseStatus.ERROR,
			);
			setTimeout(() => {
				navigate("/login");
			}, 1500);
		};

		handleOnIdleRef.current = () => {
			handleLogoutRef.current();
		};

		handleContinueSessionRef.current = () => {
			closeModal();
			activate();
			notify("Your session has been extended", ResponseStatus.SUCCESS);
			if (onSessionExtended) {
				onSessionExtended();
			}
		};

		handleOnPromptRef.current = () => {
			openModal({
				view: (
					<IdleWarningModal
						initialRemaining={promptBeforeIdle}
						onContinue={() => handleContinueSessionRef.current()}
						onLogout={() => handleLogoutRef.current()}
					/>
				),
				size: "lg",
				closeOnOutsideClick: false,
				hideCloseButton: true,
			});
		};
	}, [
		closeModal,
		navigate,
		openModal,
		activate,
		onSessionExtended,
		promptBeforeIdle,
	]);

	return children;
};

const IdleWarningModal = ({
	initialRemaining,
	onContinue,
	onLogout,
}: {
	initialRemaining: number;
	onContinue: () => void;
	onLogout: () => void;
}) => {
	const [remainingTime, setRemainingTime] = useState(initialRemaining);
	const intervalRef = useRef<NodeJS.Timeout | null>(null);

	useEffect(() => {
		intervalRef.current = setInterval(() => {
			setRemainingTime((prevTime) => {
				const newTime = Math.max(0, prevTime - 1000);

				if (newTime <= 0) {
					if (intervalRef.current) {
						clearInterval(intervalRef.current);
						intervalRef.current = null;
					}

					setTimeout(onLogout, 100);
				}

				return newTime;
			});
		}, 1000);

		return () => {
			if (intervalRef.current) {
				clearInterval(intervalRef.current);
				intervalRef.current = null;
			}
		};
	}, [initialRemaining, onLogout]);

	const formatTimeRemaining = (milliseconds: number) => {
		const minutes = Math.floor(milliseconds / 60000);
		const seconds = Math.floor((milliseconds % 60000) / 1000);
		return `${minutes}:${seconds.toString().padStart(2, "0")}`;
	};

	return (
		<div
			className="p-6 text-center"
			role="alertdialog"
			aria-labelledby="timeout-title"
			aria-describedby="timeout-desc"
		>
			<div className="flex justify-center mb-4">
				<Clock className="h-12 w-12 text-amber-500" />
			</div>
			<h2 id="timeout-title" className="text-2xl font-bold mb-4">
				Session Timeout Warning
			</h2>
			<p id="timeout-desc" className="mb-6">
				You've been inactive for a while. For your security, you'll be
				automatically logged out in{" "}
				<span className="font-bold text-amber-600 text-xl">
					{formatTimeRemaining(remainingTime)}
				</span>
				.
			</p>
			<div className="flex justify-center space-x-4">
				<Button
					onClick={onLogout}
					variant="outline"
					className="px-6 flex items-center gap-2"
				>
					<LogOut className="h-4 w-4" />
					Log Out Now
				</Button>
				<Button
					onClick={onContinue}
					className="px-6 bg-primary hover:bg-primary/90 flex items-center gap-2"
				>
					<Clock className="h-4 w-4" />
					Keep Me Signed In
				</Button>
			</div>
		</div>
	);
};
