export const apiRoutes = {
	auth: {
		forget_password: '/user-gateway/forget-password',
		confirm_code_forget_password: '/user-gateway/confirm-code-forgot-password',
		activate_user: '/user-gateway/activate-user',
		retrieve_password_email: '/user-gateway/retrive-password-email',
		resend_auth_code: '/user-gateway/resend-auth-code',
		login: '/user-gateway/login',
		register: '/user-gateway/register',
		kyc_validation: '/user-gateway/kyc-validation',
		kyc_image_verification: '/user-gateway/kyc-image-verification',

		sso: {
			google: '/user-gateway/sso/google',
			apple: '/user-gateway/sso/apple',
		}
	},

	user: {
		chat_history: '/user-gateway/user/chat-bot/get-chat-history',
		full_user_data: '/user-gateway/user/get-full-user-data',
		get_user_by_cutomeries: (identifier: string) => `/user/get-user-by-cutomeries?identifier=${identifier}`,
		account_insights_overview: '/insights/account-insight-overview',
		unlink_account: '/insights/unlink-account',
		get_news: '/user-gateway/user/get-news',
		available_countries: '/user-gateway/user/get-available-countries',
		get_states: '/user-gateway/user/get-states',
		cities_in_states: '/user-gateway/user/get-cities-in-state',
		get_all_course_user: '/user-gateway/user/get-all-course-user',
		get_all_topics_user: '/user-gateway/user/get-all-topics-user',
		get_referral_data: '/user-gateway/user/get-referral-data',
		get_all_available_profile_image: '/user-gateway/user/get-all-avaliable-profile-image',
		get_user_notification: '/user-gateway/user/get-user-notification',
		mark_notification_read: '/user-gatewayuser//mark-notification-read',
		verify_pin: '/user-gateway/user/verify-pin',
		send_chat: '/user-gateway/user/send-chat',
		change_pin: '/user-gateway/user/change-pin',
		set_pin: '/user-gateway/user/set-pin',
		activate_deactivate_pro_mode: '/user-gateway/user/activate-deactivate-pro-mode',
		validate_business: '/user-gateway/user/validate-business',
		add_user_address: '/user-gateway/user/add-user-address',
		update_username: '/user-gateway/user/update-username',
		upload_profile_image: '/user-gateway/user/upload-profile-image',
		change_email_request: '/user-gateway/user/change-email-request',
		confirm_email_change_request: '/user-gateway/user/confirm-email-change-request',
		submit_loan_request: '/user-gateway/user/submit-loan-request',
		add_referee: '/user-gateway/user/add-refree',
		submit_code: '/insights/submit-code',
		read_unread_notification: '/user-gateway/read-unread-notification',
	},

	notification: {
		notification_settings: '/user-gateway/notification/notif-settings',
		read_unread_notification: '/user-gateway/notification/read-unread-notification',
	},

	currency: {
		bills: {
			get_all_biller_category: '/bills/get-all-biller-category',
			get_bill_from_category: '/bills/get-bill-from-category',
			validate_customer_bill: '/bills/validate-customer-bill',
			purchase_bill: '/bills/purchase-bill',
			get_list_of_beneficiary: (user_id: string, page: number, limit: string) => `/currency-gateway/fiat-banking/get-fiat-beneficiary?user_id=${user_id}&page=${page}&limit=${limit}`,
		},

		cards: {
			create_card: '/cards/create-card',
			fund_card: '/cards/fund-card',
			withdraw_card: '/cards/withdraw-card',
			terminate_card: '/cards/terminate-card',
			freeze_unfreeze_card: '/cards/freeze-unfreeze-card',
			get_user_cards_list: '/cards/get-user-cards-list',
			get_user_cards_details: '/cards/get-user-cards-details',
			get_card_transactions: '/cards/get-card-transactions',
		},

		crypto_banking: {
			send: '/crypto-banking/send',
			create_dynamic_deposit: '/crypto-banking/create-dynamic-deposit',
			get_currency_withdrawal_methods: (coin: string) => `/crypto-banking/get-currency-withdrawal-methods?currency=${coin}`,
			get_currency_universal_withdrawal_networks: (currency: string) => `/crypto-banking/get-currency-universal-withdrawal-networks?currency=${currency}`,
			get_currency_universal_deposit_networks: (currency: string) =>
				`/crypto-banking/get-currency-universal-deposit-networks?currency=${currency}`,
			get_coin_networks: '/crypto-banking/get-coin-networks',
			get_currency_deposit_methods: '/crypto-banking/get-currency-deposit-methods',
			validate_wallet_address: '/crypto-banking/validate-wallet-address',
			get_transactions: '/crypto-banking/get-transactions',
			get_transaction: '/crypto-banking/get-transaction',
			get_beneficiaries_list: '/crypto-banking/get-beneficiaries-list',
			get_fiat_beneficiary: '/crypto-banking/get-fiat-beneficiary',
			get_available_currencies_user: '/crypto-banking/get-available-currencies-user',
		},

		crypto_savings: {
			create_saving_goal: '/crypto-savings/create-saving-goal',
			fund_savings_manual: '/crypto-savings/fund-savings-manual',
			fund_savings_auto: '/crypto-savings/fund-savings-auto',
			withdraw_savings_funds: '/crypto-savings/withdraw-savings-funds',
			get_saving_goals: '/crypto-savings/get-saving-goals',
		},

		fiat_banking: {
			resolve_account: '/fiat-banking/resolve-account',
			get_bank_list: '/fiat-banking/get-bank-list',
			create_account_deposit_method: '/fiat-banking/create-account-deposit-method',
			create_deposit: '/fiat-banking/create-deposit',
			create_withdrawal: '/fiat-banking/create-withdrawal',
			get_deposit_method: '/fiat-banking/get-deposit-method',
			get_transaction_by_id: '/fiat-banking/get-transaction-by-id',
			get_withdrawal_method: '/fiat-banking/get-withdrawal-method',
		},

		otc: {
			create_otc: '/otc/create-otc',
		},

		price_alerts: {
			set_price_alert: '/price-alerts/set-price-alert',
			edit_price_alert_status: '/price-alerts/edit-price-alert-status',
			delete_price_alert: '/price-alerts/delete-price-alert',
			price_alerts_coins: '/price-alerts/price-alerts-coins',
			get_price_alert_list: '/price-alerts/get-price-alert-list',
		},

		fiat_savings: {
			get_saving_goals: '/fiat-savings/get-saving-goals',
			create_saving_goal: '/fiat-savings/create-saving-goal',
			fund_savings_manual: '/fiat-savings/fund-savings-manual',
			fund_savings_auto: '/fiat-savings/fund-savings-auto',
			withdraw_savings_funds: '/fiat-savings/withdraw-savings-funds',
		},

		swaps: {
			create_swap: '/swaps/create-swap',
			get_swapping_price: '/swaps/get-swapping-price',
			get_swaps_for_currency: (currency: string) => `/swaps/get-swaps-for-currency?currency=${currency}`,
			get_swapping_rate: (priceId: string) => `/swaps/get-swapping-rate?price_id=${priceId}`,
			get_swapping_e: (priceId: string) => `/swaps/get-swapping-rate?price_id=${priceId}`,
		},

		fiat_wallets: {
			get_user_wallets: '/fiat-wallets/get-user-wallets',
			add_wallet: '/fiat-wallets/add-wallet',
			get_available_currencies: '/fiat-wallets/get-available-currencies',
		},

		crypto_wallets: {
			add_wallet: '/crypto-wallets/add-wallet',
			create_wallet: '/crypto-wallets/create-wallet',
			get_user_wallets: '/crypto-wallets/get-user-wallets',
		},

		crypto_portfolio: {
			fetch_coins_market_data: (currency: string) => `/crypto-portfolio/fetch-coins-market-data?currency=${currency}`,
			fetch_coins_market_chart: '/crypto-portfolio/fetch-coins-market-chart',
			fetch_coin_data: (coinId: string) => `/crypto-portfolio/fetch-coin-data?coin_id=${coinId}`,
		},

		general: {
			user_transactions: '/general/user-transactions',
		},
	},
};
