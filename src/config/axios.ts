import axios, {
	AxiosInstance,
	AxiosRequestConfig,
	InternalAxiosRequestConfig,
	AxiosHeaders,
	AxiosResponse,
	AxiosError,
} from "axios";
import { APP_CONFIG } from ".";

export enum MicroServices {
	USER = "USER",
	CURRENCY = "CURRENCY",
}

const getBaseConfig = (service: MicroServices): AxiosRequestConfig => ({
	baseURL: APP_CONFIG.API_URLS[service],
	timeout: 15000,
	headers: new AxiosHeaders({
		Accept: "application/json",
	}),
});

const authInterceptor = (config: InternalAxiosRequestConfig) => {
	const token = localStorage.getItem("auth_token");
	if (token) {
		config.headers.set("Authorization", `Bearer ${token}`);
	}
	return config;
};

const errorInterceptor = (error: AxiosError) => {
	if (error.response?.status === 401) {
		localStorage.removeItem("auth_token");
		window.location.href = "/login";
	}
	return Promise.reject(error);
};

const createJsonInstance = (service: MicroServices): AxiosInstance => {
	if (!APP_CONFIG.API_URLS[service]) {
		throw new Error(`No API URL configured for service: ${service}`);
	}

	const instance = axios.create({
		...getBaseConfig(service),
		headers: new AxiosHeaders({
			...getBaseConfig(service).headers,
			"Content-Type": "application/json",
		}),
	});

	instance.interceptors.request.use(authInterceptor);
	instance.interceptors.response.use(
		(response: AxiosResponse) => response,
		errorInterceptor,
	);

	return instance;
};

const createFormDataInstance = (service: MicroServices): AxiosInstance => {
	if (!APP_CONFIG.API_URLS[service]) {
		throw new Error(`No API URL configured for service: ${service}`);
	}

	const instance = axios.create({
		...getBaseConfig(service),
		headers: new AxiosHeaders({
			...getBaseConfig(service).headers,
			"Content-Type": "multipart/form-data",
		}),
	});

	instance.interceptors.request.use((config) => {
		const modifiedConfig = authInterceptor(config);

		if (modifiedConfig.data instanceof FormData) {
			modifiedConfig.headers.setContentType("multipart/form-data");
		}
		return modifiedConfig;
	});

	instance.interceptors.response.use(
		(response: AxiosResponse) => response,
		errorInterceptor,
	);

	return instance;
};

export const userGateway = createJsonInstance(MicroServices.USER);
export const currencyGateway = createJsonInstance(MicroServices.CURRENCY);

export const userFormDataGateway = createFormDataInstance(MicroServices.USER);
export const currencyFormDataGateway = createFormDataInstance(
	MicroServices.CURRENCY,
);
