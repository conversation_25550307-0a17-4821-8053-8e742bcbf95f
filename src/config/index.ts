import env from "./env";

type MicroServicesType = {
	USER: string;
	CURRENCY: string;
};

type Environment = "DEVELOPMENT" | "STAGING" | "PRODUCTION";

type ApiUrlsStructure = {
	[key in Environment]: MicroServicesType;
};

const API_URLS: ApiUrlsStructure = {
	DEVELOPMENT: {
		USER: "https://clyp-user-staging-c8d72db35571.herokuapp.com",
		CURRENCY:
			"https://clyp-currency-service-staging-91ba28e1de33.herokuapp.com/currency-gateway",
	},
	STAGING: {
		USER: "https://clyp-user-staging-c8d72db35571.herokuapp.com",
		CURRENCY:
			"https://clyp-currency-service-staging-91ba28e1de33.herokuapp.com/currency-gateway",
	},
	PRODUCTION: {
		USER: "https://clyp-user-staging-c8d72db35571.herokuapp.com",
		CURRENCY:
			"https://clyp-currency-service-staging-91ba28e1de33.herokuapp.com/currency-gateway",
	},
} as const;

interface EnvironmentConfig {
	ENV: Lowercase<Environment>;
	API_URLS: MicroServicesType;
}

const getEnvironmentKey = (): Environment => {
	const envValue = env.VITE_ENV?.toUpperCase();
	return Object.keys(API_URLS).includes(envValue || "")
		? (envValue as Environment)
		: "DEVELOPMENT";
};

const getApiUrls = (): MicroServicesType => {
	const envKey = getEnvironmentKey();
	return API_URLS[envKey];
};

const appConfig = (): EnvironmentConfig => ({
	ENV: (env.VITE_ENV?.toLowerCase() ||
		"development") as Lowercase<Environment>,
	API_URLS: getApiUrls(),
});

export const APP_CONFIG = appConfig();
