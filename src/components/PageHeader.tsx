import React from "react";
import { IoChevronBack, IoChevronForward } from "react-icons/io5";
import { useNavigate } from "react-router-dom";

interface PageHeaderProps {
  title?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title }) => {

  const navigate = useNavigate();

  return (

    <div className="flex items-center gap-2 mb-4">
      <div className="flex text-primary items-center gap-4">
        <div
          className="bg-primary/20 p-2 rounded-full cursor-pointer"
          // onClick={onBack}
          role="button"
          tabIndex={0}
          onClick={() => navigate(-1)}

        >
          <IoChevronBack />
        </div>
      </div>
      <h2 className="text-2xl font-semibold ml-2">{title}</h2>
    </div>
  )
};

export default PageHeader;