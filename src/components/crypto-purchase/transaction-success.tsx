import type { FC } from "react";
import { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { Check } from "lucide-react";
import cn from "@/utils/class-names";
import Confetti from "react-confetti";

interface TransactionSuccessProps {
	title?: string;
	buttonClassName?: string;
	message: string | React.ReactNode;
	buttonText?: string;
	onNextAction: () => void;
	icon?: React.ReactNode;
	confettiDuration?: number;
}

export const TransactionSuccess: FC<TransactionSuccessProps> = ({
	title = "Transaction Successful!",
	buttonClassName,
	message,
	buttonText = "Proceed",
	onNextAction,
	icon,
	confettiDuration = 3000,
}) => {
	const [showConfetti, setShowConfetti] = useState(true);
	const containerRef = useRef<HTMLDivElement>(null);
	const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

	useEffect(() => {
		const updateDimensions = () => {
			if (containerRef.current) {
				setDimensions({
					width: containerRef.current.offsetWidth,
					height: containerRef.current.offsetHeight,
				});
			}
		};

		updateDimensions();

		const resizeObserver = new ResizeObserver(updateDimensions);
		if (containerRef.current) {
			resizeObserver.observe(containerRef.current);
		}

		return () => {
			if (containerRef.current) {
				resizeObserver.unobserve(containerRef.current);
			}
		};
	}, []);

	useEffect(() => {
		const timer = setTimeout(() => {
			setShowConfetti(false);
		}, confettiDuration);

		return () => clearTimeout(timer);
	}, [confettiDuration]);

	return (
		<div
			ref={containerRef}
			className="flex flex-col items-center justify-center space-y-6 py-12 relative overflow-hidden h-full"
		>
			{dimensions.width > 0 && (
				<Confetti
					width={dimensions.width}
					height={dimensions.height}
					recycle={showConfetti}
					// run={showConfetti}
					numberOfPieces={150}
					gravity={0.2}
					confettiSource={{
						x: dimensions.width / 2,
						y: dimensions.height / 4,
						w: 0,
						h: 0,
					}}
					style={{ position: "absolute", top: 0, left: 0 }}
					
					friction={0.99}
					wind={0}
					opacity={1}
					initialVelocityX={5}
					initialVelocityY={10}
					tweenDuration={3000}
				/>
			)}

			<motion.div
				initial={{ scale: 0 }}
				animate={{ scale: 1 }}
				transition={{ type: "spring", duration: 0.5 }}
				className="w-32 h-32 bg-green-500 rounded-full flex items-center justify-center relative z-10"
			>
				{icon || (
					<Check className="w-16 h-16 text-white" strokeWidth={3} />
				)}
			</motion.div>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="text-center relative z-10"
			>
				<h2 className="text-2xl font-semibold mb-2">{title}</h2>
				<p className="text-gray-600 dark:text-gray-300">{message}</p>
			</motion.div>

			<motion.button
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ delay: 0.5 }}
				onClick={onNextAction}
				className={cn(
					"bg-primary hover:bg-primary/90 text-white rounded-full px-8 py-4 font-medium w-full max-w-xs relative z-10",
					buttonClassName,
				)}
			>
				{buttonText}
			</motion.button>
		</div>
	);
};
