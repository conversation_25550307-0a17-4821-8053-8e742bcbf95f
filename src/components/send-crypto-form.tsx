"use client"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

export function SendCryptoForm() {
  return (
    <form className="space-y-6">
      <div className="space-y-4">
        <Select>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Choose Coin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="btc">Bitcoin (BTC)</SelectItem>
            <SelectItem value="eth">Ethereum (ETH)</SelectItem>
            <SelectItem value="usdt">Tether (USDT)</SelectItem>
          </SelectContent>
        </Select>

        <Select>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Choose Network" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="bitcoin">Bitcoin</SelectItem>
            <SelectItem value="ethereum">Ethereum</SelectItem>
            <SelectItem value="polygon">Polygon</SelectItem>
          </SelectContent>
        </Select>

        <div className="space-y-2">
          <Input placeholder="Paste wallet address" className="w-full" />
          <p className="text-sm text-muted-foreground">Must be a valid wallet address</p>
        </div>

        <div className="space-y-2">
          <Input type="number" placeholder="Enter Amount" className="w-full" />
          <p className="text-sm text-muted-foreground">Fee is displayed here</p>
        </div>

        <div className="bg-slate-50 p-4 rounded-lg">
          <p className="text-sm text-center">Amount to be sent after fee is deducted</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="relative flex items-center justify-center">
          <span className="px-4 text-sm text-gray-500 bg-white">Or</span>
          <div className="absolute inset-x-0 top-1/2 h-px bg-gray-200" />
        </div>

        <div className="flex flex-col sm:flex-row gap-4">
          <Button variant="outline" className="flex-1">
            Scan QR code
          </Button>
          <Button className="flex-1 bg-[#CD853F] hover:bg-[#B8732F]">Send Crypto</Button>
        </div>
      </div>
    </form>
  )
}

