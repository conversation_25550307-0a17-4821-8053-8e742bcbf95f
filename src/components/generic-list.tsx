import React, { ReactNode } from "react";


interface ListItem {
	id: string | number;
}


interface GenericListProps<T extends ListItem> {
	
	items: T[];
	renderItem: (item: T, index: number) => ReactNode;

	
	className?: string;
	emptyMessage?: ReactNode;
	spacing?: "none" | "tight" | "normal" | "relaxed";
	layout?: "vertical" | "horizontal" | "grid";

	
	header?: ReactNode;
	footer?: ReactNode;
}


const spacingClasses = {
	none: "space-y-0",
	tight: "space-y-2",
	normal: "space-y-4",
	relaxed: "space-y-6",
};


const layoutClasses = {
	vertical: "flex flex-col",
	horizontal: "flex flex-row gap-4 overflow-x-auto",
	grid: "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4",
};

export function GenericList<T extends ListItem>({
	items,
	renderItem,
	className = "",
	emptyMessage = "No items found",
	spacing = "normal",
	layout = "vertical",
	header,
	footer,
}: GenericListProps<T>) {
	
	const containerClass = `${layoutClasses[layout]} ${
		layout === "vertical" ? spacingClasses[spacing] : ""
	} ${className}`;

	return (
		<div className={containerClass}>
			{/* Optional header */}
			{header}

			{/* Render items or empty message */}
			{items.length > 0 ? (
				items.map((item, index) => (
					<React.Fragment key={item.id}>
						{renderItem(item, index)}
					</React.Fragment>
				))
			) : (
				<div className="py-4 text-center text-gray-500">
					{emptyMessage}
				</div>
			)}

			{/* Optional footer */}
			{footer}
		</div>
	);
}
