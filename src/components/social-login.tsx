function FacebookIcon({ className = "" }: { className?: string }) {
	return (
		<svg
			className={className}
			viewBox="0 0 48 48"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<circle cx="24" cy="24" r="20" fill="#1877F2" />
			<path
				d="M32.5 24H26.5V19C26.5 17.62 27.62 17.5 28 17.5H30.5V12.5H26.5C22.63 12.5 20.5 15.51 20.5 18.5V24H15.5V29H20.5V42.5H26.5V29H31L32.5 24Z"
				fill="white"
			/>
		</svg>
	);
}

function GoogleIcon({ className = "" }: { className?: string }) {
	return (
		<svg
			className={className}
			viewBox="0 0 48 48"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
				fill="#FFC107"
			/>
			<path
				d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
				fill="#FF3D00"
			/>
			<path
				d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
				fill="#4CAF50"
			/>
			<path
				d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
				fill="#1976D2"
			/>
		</svg>
	);
}

function AppleIcon({ className = "" }: { className?: string }) {
	return (
		<svg
			className={className}
			viewBox="0 0 48 48"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M31.6,24c0-5.2,4.2-7.7,4.4-7.8c-2.4-3.5-6.1-4-7.4-4c-3.1-0.3-6.1,1.8-7.7,1.8c-1.6,0-4-1.8-6.6-1.7c-3.4,0-6.5,2-8.2,5c-3.5,6.1-0.9,15.1,2.5,20c1.7,2.4,3.6,5.1,6.2,5c2.5-0.1,3.4-1.6,6.4-1.6c3,0,3.8,1.6,6.4,1.6c2.7,0,4.3-2.4,5.9-4.8c1.9-2.7,2.6-5.3,2.7-5.4C36.1,31.8,31.6,30,31.6,24z"
				fill="black"
			/>
			<path
				d="M28.4,12.5c1.3-1.7,2.3-4,2-6.3c-2,0.1-4.3,1.3-5.7,3c-1.2,1.4-2.3,3.8-2,6C25,15.3,27,14.2,28.4,12.5z"
				fill="black"
			/>
		</svg>
	);
}

const SocialLogin = () => {
	return (
		<>
			<div className="relative flex items-center justify-center mt-8">
				<div className="border-t border-gray-300 absolute w-full"></div>
				<div className="bg-white px-4 relative z-10 text-sm text-gray-500">
					or continue with
				</div>
			</div>

			<div className="flex justify-center space-x-6 mt-6">
				<button
					type="button"
					className="p-2 cursor-pointer"
					aria-label="Continue with Facebook"
				>
					<FacebookIcon className="h-8 w-8" />
				</button>
				<button
					type="button"
					className="p-2 cursor-pointer"
					aria-label="Continue with Google"
				>
					<GoogleIcon className="h-8 w-8" />
				</button>
				<button
					type="button"
					className="p-2 cursor-pointer"
					aria-label="Continue with Apple"
				>
					<AppleIcon className="h-8 w-8" />
				</button>
			</div>
		</>
	);
};

export default SocialLogin;
