import React, { useState, useRef, useEffect, ReactNode } from "react";
import { Search, ChevronDown, X, Loader2 } from "lucide-react";
import cn from "@/utils/class-names";

export interface IOption<T> {
	id?: string;
	hidden?: boolean;
	visible?: boolean;
	icon?: string | ReactNode;
	raw?: T;
	label: string;
	value: any;
}

interface EnhancedSelectProps<T> {
	options: IOption<T>[];
	value?: IOption<T> | null;
	onChange: (option: IOption<T> | null) => void;
	placeholder?: string;
	isLoading?: boolean;
	isSearchable?: boolean;
	isClearable?: boolean;
	renderOption?: (option: IOption<T>) => React.ReactNode;
	renderSelected?: (option: IOption<T>) => React.ReactNode;
	className?: string;
	header?: React.ReactNode;
	disabled?: boolean;
	displayClassName?: string;
	containerClassName?: string;
}

export const EnhancedSelect = <T extends any>({
	options,
	value,
	onChange,
	placeholder = "Select...",
	isLoading = false,
	isSearchable = true,
	isClearable = true,
	renderOption,
	renderSelected,
	className = "",
	header,
	disabled = false,
	displayClassName = "",
	containerClassName = "",
}: EnhancedSelectProps<T>) => {
	const [isOpen, setIsOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const dropdownRef = useRef<HTMLDivElement>(null);

	const filteredOptions = options.filter((option) =>
		option.label?.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () =>
			document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	const handleSelect = (option: IOption<T>) => {
		if (disabled) return;
		onChange(option);
		setIsOpen(false);
		setSearchTerm("");
	};

	const handleClear = (e: React.MouseEvent) => {
		if (disabled) return;
		e.stopPropagation();
		onChange(null);
	};

	const toggleOpen = () => {
		if (disabled) return;
		setIsOpen(!isOpen);
	};

	const renderIcon = (icon?: string | Element | ReactNode) => {
		if (!icon) return null;

		if (typeof icon === "string") {
			return (
				<img
					src={icon}
					alt="option icon"
					className="w-6 h-6 mr-2 flex-shrink-0 rounded-full object-cover"
				/>
			);
		}

		return <div className="mr-2 flex-shrink-0">{icon as ReactNode}</div>;
	};

	return (
		<div
			className={cn("relative", containerClassName, className)}
			ref={dropdownRef}
		>
			<div
				className={cn(
					"flex items-center justify-between p-3 border rounded-full cursor-pointer transition-colors",
					!disabled && "bg-gray-50 hover:bg-gray-100 dark:bg-input dark:hover:bg-input/80",
					disabled && "bg-gray-200 cursor-not-allowed opacity-60 dark:bg-muted/50 dark:text-muted-foreground/50",
					displayClassName,
				)}
				onClick={toggleOpen}
			>
				<div className="flex items-center min-w-0 flex-1">
					{isLoading ? (
						<span className="flex items-center text-gray-400 dark:text-muted-foreground">
							<Loader2 className="animate-spin mr-2 h-5 w-5 text-gray-400 dark:text-muted-foreground" />
							loading...
						</span>
					) : value ? (
						renderSelected ? (
							renderSelected(value)
						) : (
							<>
								{renderIcon(value.icon)}
								<span
									className={cn(
										"truncate",
							disabled && "text-muted-foreground",
						!disabled && "text-foreground" 
						)}
					>
						{value.label}
					</span>
							</>
						)
					) : (						<span
						className={cn(
							"text-muted-foreground truncate",
							disabled && "text-muted-foreground/70",
						!disabled && "dark:text-muted-foreground"
						)}
					>
							{placeholder}
						</span>
					)}
				</div>

				<div className="flex items-center ml-2 space-x-1">				{isClearable && value && !disabled && (
					<X
						className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
						onClick={handleClear}
					/>
				)}
				<ChevronDown
					className={cn(
						"h-5 w-5 transition-transform",
						isOpen ? "rotate-180" : "",
						disabled ? "text-muted-foreground" : "text-primary",
					)}
				/>
				</div>
			</div>

			{/* Dropdown menu */}
			{isOpen && !disabled && (
				<div className="absolute z-10 w-full mt-1 bg-white dark:bg-card border dark:border-border rounded-lg shadow-lg overflow-hidden p-2 px-4">
					{header && <div className="mb-2">{header}</div>}

					{/* Search input */}
					{isSearchable && (
						<div className="border-b dark:border-border pb-3">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-5 text-gray-400 dark:text-muted-foreground" />
								<input
									type="text"
									placeholder="Search"
									className="w-full pl-10 pr-8 py-4 text-sm rounded-full border dark:border-border focus:outline-none focus:ring-2 focus:ring-primary bg-transparent dark:text-foreground dark:placeholder:text-muted-foreground"
									value={searchTerm}
									onChange={(e) =>
										setSearchTerm(e.target.value)
									}
									autoFocus
								/>
								{searchTerm && (
									<X
										className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-muted-foreground cursor-pointer hover:text-gray-600 dark:hover:text-foreground"
										onClick={() => setSearchTerm("")}
									/>
								)}
							</div>
						</div>
					)}

					{/* Options list */}
					<div className="max-h-60 overflow-y-auto space-y-2 py-2">
						{filteredOptions.length > 0 ? (
							filteredOptions
								.filter((option) => !option.hidden)
								.map((option) => (
									<div
										key={
											option.id ||
											`${option.value}-${option.label}`
										}
										className={cn(

											"flex items-center p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-muted rounded-full",

											value?.id === option.id ||
												value?.value === option.value
												? "bg-slate-100 dark:bg-muted/80"
												: "",
										)}
										onClick={() => handleSelect(option)}
									>
										{renderOption ? (
											renderOption(option)
										) : (
											<>
												{renderIcon(option.icon)}
												<span className="truncate text-sm dark:text-foreground">
													{option.label}
												</span>
											</>
										)}
									</div>
								))
						) : (
							<div className="p-3 text-center text-sm text-gray-500 dark:text-muted-foreground">
								No options available
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default EnhancedSelect;
