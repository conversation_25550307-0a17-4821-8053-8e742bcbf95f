import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import OTPInput from "@/components/custom/input/otp-input";

export interface CreatePinStepProps {
	onPinCreated: () => void;
}

const CreatePinStep: React.FC<CreatePinStepProps> = ({ onPinCreated }) => {
	const [pin, setPin] = useState<string>("");
	const [loading, setLoading] = useState<boolean>(false);

	const handlePinChange = (value: string) => {
		setPin(value);
	};

	const handlePinComplete = (value: string) => {
		console.log("PIN completed:", value);
		
	};

	const handleCreatePin = async () => {
		if (pin.length !== 4) return;

		setLoading(true);
		try {
			await new Promise((resolve) => setTimeout(resolve, 800));

			onPinCreated();
		} catch (error) {
			console.error("Failed to create PIN:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="flex flex-col gap-4 p-4">
			<div className="text-center mb-12">
				<p>Create transaction pin to complete transaction.</p>
			</div>

			<div className="mb-8">
				<OTPInput
					length={4}
					value={pin}
					onChange={handlePinChange}
					onComplete={handlePinComplete}
					autoFocus
				/>
			</div>

			<div className="mt-8">
				<Button
					onClick={handleCreatePin}
					className="w-full py-7 rounded-full"
					disabled={pin.length !== 4 || loading}
				>
					{loading ? "Creating..." : "Create Pin"}
				</Button>
			</div>
		</div>
	);
};

export default CreatePinStep;
