import { create } from "zustand";

export type ModalSizes =
	| "xs"
	| "sm"
	| "md"
	| "lg"
	| "xl"
	| "2xl"
	| "3xl"
	| "4xl"
	| "5xl"
	| "full"
	| "auto";
export type ModalPositions = "center" | "top" | "bottom";
export type ModalAnimations =
	| "fade"
	| "zoom"
	| "slide-up"
	| "slide-down"
	| "none";

interface ModalState {
	isOpen: boolean;
	view: React.ReactNode;
	size: ModalSizes;
	position: ModalPositions;
	animation: ModalAnimations;
	fullHeight: boolean;
	maxHeight: boolean;
	closeOnOutsideClick: boolean;
	hideCloseButton: boolean;

	openModal: (params: {
		view: React.ReactNode;
		size?: ModalSizes;
		position?: ModalPositions;
		animation?: ModalAnimations;
		fullHeight?: boolean;
		maxHeight?: boolean;
		closeOnOutsideClick?: boolean;
		hideCloseButton?: boolean;
	}) => void;

	closeModal: () => void;
}

export const useModalStore = create<ModalState>((set) => ({
	isOpen: false,
	view: null,
	size: "md",
	position: "center",
	animation: "fade",
	fullHeight: false,
	maxHeight: true,
	closeOnOutsideClick: true,
	hideCloseButton: false,

	openModal: ({
		view,
		size = "md",
		position = "center",
		animation = "fade",
		fullHeight = false,
		maxHeight = true,
		closeOnOutsideClick = true,
		hideCloseButton = false,
	}) => {
		set({
			isOpen: true,
			view,
			size,
			position,
			animation,
			fullHeight,
			maxHeight,
			closeOnOutsideClick,
			hideCloseButton,
		});
	},

	closeModal: () => {
		set((state) => ({
			...state,
			isOpen: false,
		}));
	},
}));

export function useModal() {
	const {
		isOpen,
		view,
		size,
		position,
		animation,
		fullHeight,
		maxHeight,
		closeOnOutsideClick,
		hideCloseButton,
		openModal,
		closeModal,
	} = useModalStore();

	return {
		isOpen,
		view,
		size,
		position,
		animation,
		fullHeight,
		maxHeight,
		closeOnOutsideClick,
		hideCloseButton,
		openModal,
		closeModal,
	};
}
