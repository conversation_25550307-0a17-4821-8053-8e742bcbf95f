import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Modal } from "@/components/ui/modal";
import { useModal } from "./use-modal";

export default function GlobalModal() {
	const {
		isOpen,
		view,
		size,
		position,
		animation,
		fullHeight,
		maxHeight,
		closeOnOutsideClick,
		hideCloseButton,
		closeModal,
	} = useModal();

	const location = useLocation();

	useEffect(() => {
		if (isOpen) {
			closeModal();
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [location.pathname]);

	return (
		<Modal
			isOpen={isOpen}
			onClose={closeModal}
			size={size}
			position={position}
			animation={animation}
			fullHeight={fullHeight}
			maxHeight={maxHeight}
			closeOnOutsideClick={closeOnOutsideClick}
			hideCloseButton={hideCloseButton}
			overlayClassName="dark:bg-opacity-40 dark:backdrop-blur-md"
			className="dark:bg-card"
		>
			{view}
		</Modal>
	);
}
