import React from "react";

export interface IconProps extends React.SVGProps<SVGSVGElement> {
	/**
	 * Icon name
	 */
	name:
		| "file-check"
		| "clyppay_logo"
		| "clyppay-icon-logo"
		| "copy"
		| "home"
		| "report"
		| "clyphub"
		| "clypnews"
		| "my-profile"
		| "arrow-left"
		| "focus-camera"
		| "bell"
		| "twitter"
		| "facebook"
		| "linkedin"
		| "instagram"
		| "tiktok";
}

const IconComponent: React.FC<IconProps> = ({ name, ...props }) => {
	switch (name) {
		case "twitter":
			return (
				<svg
					viewBox="0 0 20 20"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M14.7918 2.5H17.3477L11.7643 8.85417L18.3327 17.5H13.1893L9.16185 12.2558L4.55268 17.5H1.99435L7.96685 10.7042L1.66602 2.5H6.93935L10.581 7.29333L14.7918 2.5ZM13.8952 15.9767H15.3118L6.16935 3.94333H4.64935L13.8952 15.9767Z"
						fill="#25292D"
					/>
				</svg>
			);
		case "tiktok":
			return (
				<svg
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M16.6002 5.82C15.9167 5.03953 15.5401 4.0374 15.5402 3H12.4502V15.4C12.4268 16.0712 12.1437 16.7071 11.6605 17.1735C11.1773 17.6399 10.5318 17.9004 9.86016 17.9C8.44016 17.9 7.26016 16.74 7.26016 15.3C7.26016 13.58 8.92016 12.29 10.6302 12.82V9.66C7.18016 9.2 4.16016 11.88 4.16016 15.3C4.16016 18.63 6.92016 21 9.85016 21C12.9902 21 15.5402 18.45 15.5402 15.3V9.01C16.7932 9.90985 18.2975 10.3926 19.8402 10.39V7.3C19.8402 7.3 17.9602 7.39 16.6002 5.82Z"
						fill="#25292D"
					/>
				</svg>
			);
		case "instagram":
			return (
				<svg
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M11.9991 8.87726C10.2788 8.87726 8.87492 10.2812 8.87492 12.0015C8.87492 13.7218 10.2788 15.1257 11.9991 15.1257C13.7194 15.1257 15.1234 13.7218 15.1234 12.0015C15.1234 10.2812 13.7194 8.87726 11.9991 8.87726ZM21.3694 12.0015C21.3694 10.7077 21.3812 9.4257 21.3085 8.13429C21.2359 6.63429 20.8937 5.30304 19.7968 4.20617C18.6976 3.10695 17.3687 2.7671 15.8687 2.69445C14.5749 2.62179 13.2929 2.63351 12.0015 2.63351C10.7077 2.63351 9.4257 2.62179 8.13429 2.69445C6.63429 2.7671 5.30304 3.10929 4.20617 4.20617C3.10695 5.30538 2.7671 6.63429 2.69445 8.13429C2.62179 9.42804 2.63351 10.7101 2.63351 12.0015C2.63351 13.2929 2.62179 14.5773 2.69445 15.8687C2.7671 17.3687 3.10929 18.6999 4.20617 19.7968C5.30538 20.896 6.63429 21.2359 8.13429 21.3085C9.42804 21.3812 10.7101 21.3694 12.0015 21.3694C13.2952 21.3694 14.5773 21.3812 15.8687 21.3085C17.3687 21.2359 18.6999 20.8937 19.7968 19.7968C20.896 18.6976 21.2359 17.3687 21.3085 15.8687C21.3835 14.5773 21.3694 13.2952 21.3694 12.0015ZM11.9991 16.8085C9.33898 16.8085 7.1921 14.6616 7.1921 12.0015C7.1921 9.34132 9.33898 7.19445 11.9991 7.19445C14.6593 7.19445 16.8062 9.34132 16.8062 12.0015C16.8062 14.6616 14.6593 16.8085 11.9991 16.8085ZM17.003 8.12023C16.3819 8.12023 15.8804 7.61867 15.8804 6.99757C15.8804 6.37648 16.3819 5.87492 17.003 5.87492C17.6241 5.87492 18.1257 6.37648 18.1257 6.99757C18.1259 7.14505 18.097 7.29112 18.0406 7.42741C17.9843 7.5637 17.9016 7.68754 17.7973 7.79182C17.693 7.89611 17.5692 7.9788 17.4329 8.03515C17.2966 8.0915 17.1505 8.12041 17.003 8.12023Z"
						fill="#25292D"
					/>
				</svg>
			);
		case "linkedin":
			return (
				<svg
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M19 3C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19ZM18.5 18.5V13.2C18.5 12.3354 18.1565 11.5062 17.5452 10.8948C16.9338 10.2835 16.1046 9.94 15.24 9.94C14.39 9.94 13.4 10.46 12.92 11.24V10.13H10.13V18.5H12.92V13.57C12.92 12.8 13.54 12.17 14.31 12.17C14.6813 12.17 15.0374 12.3175 15.2999 12.5801C15.5625 12.8426 15.71 13.1987 15.71 13.57V18.5H18.5ZM6.88 8.56C7.32556 8.56 7.75288 8.383 8.06794 8.06794C8.383 7.75288 8.56 7.32556 8.56 6.88C8.56 5.95 7.81 5.19 6.88 5.19C6.43178 5.19 6.00193 5.36805 5.68499 5.68499C5.36805 6.00193 5.19 6.43178 5.19 6.88C5.19 7.81 5.95 8.56 6.88 8.56ZM8.27 18.5V10.13H5.5V18.5H8.27Z"
						fill="#25292D"
					/>
				</svg>
			);
		case "facebook":
			return (
				<svg
					viewBox="0 0 20 20"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M17 1H3C1.9 1 1 1.9 1 3V17C1 18.101 1.9 19 3 19H10V12H8V9.525H10V7.475C10 5.311 11.212 3.791 13.766 3.791L15.569 3.793V6.398H14.372C13.378 6.398 13 7.144 13 7.836V9.526H15.568L15 12H13V19H17C18.1 19 19 18.101 19 17V3C19 1.9 18.1 1 17 1Z"
						fill="#25292D"
					/>
				</svg>
			);
		case "file-check":
			return (
				<svg
					viewBox="0 0 15 18"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M9.45022 1H1.64997C1.47759 1 1.31226 1.07024 1.19037 1.19526C1.06848 1.32029 1 1.48986 1 1.66667V16.3333C1 16.5101 1.06848 16.6797 1.19037 16.8047C1.31226 16.9298 1.47759 17 1.64997 17H13.35C13.4354 17 13.5199 16.9828 13.5988 16.9493C13.6776 16.9157 13.7493 16.8666 13.8096 16.8047C13.87 16.7428 13.9179 16.6693 13.9505 16.5885C13.9832 16.5076 14 16.4209 14 16.3333V5.66667M9.45022 1L14 5.66667M9.45022 1V5.66667H14"
						stroke="currentColor"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
					<path
						d="M4.25 11.5L6.10714 13.5L10.75 9"
						stroke="currentColor"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
				</svg>
			);
		case "home":
			return (
				<svg
					viewBox="0 0 20 20"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M6.73517 2.02926L0.265679 7.69037C-0.289113 8.17584 0.0828658 9.03809 0.847093 9.03809C1.31399 9.03809 1.69248 9.38769 1.69248 9.81895V13.4229C1.69248 16.5234 1.69248 18.0736 2.73529 19.0368C3.7781 20 5.45647 20 8.81321 20H11.1868C14.5435 20 16.2219 20 17.2647 19.0368C18.3075 18.0736 18.3075 16.5234 18.3075 13.4229V9.81895C18.3075 9.3877 18.686 9.03809 19.1529 9.03809C19.9171 9.03809 20.2891 8.17584 19.7343 7.69037L13.2648 2.02926C11.7188 0.676419 10.9458 0 10 0C9.0542 0 8.28119 0.67642 6.73517 2.02926Z"
						// fill="#EA9924"
					/>
					<path
						d="M10 14.4453H10.009"
						stroke="white"
						strokeWidth="2"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
				</svg>
			);
		case "report":
			return (
				<svg
					viewBox="0 0 20 20"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<g clipPath="url(#clip0_13691_6538)">
						<path
							d="M10.7653 3.70613L10.7588 3.7022C10.3227 3.43836 9.77953 3.43206 9.34415 3.70653C8.91833 3.97275 8.6832 4.45447 8.719 4.94057V15.11V15.1395L8.72248 15.1689C8.80265 15.8448 9.36995 16.36 10.049 16.36C10.7325 16.36 11.3124 15.849 11.3769 15.1563L11.379 15.1332V15.11V4.94819C11.4289 4.4526 11.1869 3.97009 10.7653 3.70618L10.7653 3.70613ZM6.09712 6.98733L6.09641 6.98688C5.66413 6.71471 5.11593 6.71403 4.68359 6.98688C4.25961 7.25322 4.02284 7.73223 4.06 8.22122V15.11V15.1326L4.06204 15.1552C4.12501 15.8494 4.70707 16.36 5.389 16.36C6.07254 16.36 6.65241 15.849 6.71685 15.1563L6.719 15.1332V15.11V8.22189C6.75754 7.73008 6.5164 7.25231 6.09712 6.98733ZM13.9405 10.6126L13.9405 10.6126L13.9343 10.6164C13.5142 10.88 13.2699 11.3498 13.32 11.8575V15.11V15.1332L13.3222 15.1563C13.3866 15.849 13.9665 16.36 14.65 16.36C15.329 16.36 15.8964 15.8448 15.9765 15.1689L15.98 15.1395V15.11V11.85C16.016 11.3513 15.7785 10.883 15.3561 10.6173C14.9208 10.3422 14.3767 10.3478 13.9405 10.6126ZM14.669 0.5C16.2763 0.5 17.4644 0.954156 18.2524 1.74298C19.0405 2.53201 19.4952 3.7228 19.5 5.33083C19.5 5.33104 19.5 5.33126 19.5 5.33147L19.5 14.67C19.5 16.2768 19.0456 17.4673 18.2563 18.2564C17.4671 19.0455 16.2763 19.5 14.669 19.5H5.33C3.72266 19.5 2.53217 19.0456 1.7432 18.2565C0.95422 17.4674 0.5 16.2769 0.5 14.67V5.33C0.5 3.72259 0.954238 2.53212 1.74318 1.74318C2.53212 0.954238 3.72259 0.5 5.33 0.5H14.669Z"
							stroke="inherit"
						/>
					</g>
					<defs>
						<clipPath id="clip0_13691_6538">
							<rect width="20" height="20" fill="white" />
						</clipPath>
					</defs>
				</svg>
			);
		case "clyphub":
			return (
				<svg
					viewBox="0 0 32 32"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M6 22C6 20.4596 6 19.6893 6.34673 19.1235C6.54074 18.8069 6.80693 18.5407 7.12353 18.3467C7.68934 18 8.45956 18 10 18C11.5404 18 12.3107 18 12.8765 18.3467C13.1931 18.5407 13.4593 18.8069 13.6533 19.1235C14 19.6893 14 20.4596 14 22C14 23.5404 14 24.3107 13.6533 24.8765C13.4593 25.1931 13.1931 25.4593 12.8765 25.6533C12.3107 26 11.5404 26 10 26C8.45956 26 7.68934 26 7.12353 25.6533C6.80693 25.4593 6.54074 25.1931 6.34673 24.8765C6 24.3107 6 23.5404 6 22Z"
						stroke="inherit"
						strokeWidth="1.5"
					/>
					<path
						d="M18 22C18 20.4596 18 19.6893 18.3467 19.1235C18.5407 18.8069 18.8069 18.5407 19.1235 18.3467C19.6893 18 20.4596 18 22 18C23.5404 18 24.3107 18 24.8765 18.3467C25.1931 18.5407 25.4593 18.8069 25.6533 19.1235C26 19.6893 26 20.4596 26 22C26 23.5404 26 24.3107 25.6533 24.8765C25.4593 25.1931 25.1931 25.4593 24.8765 25.6533C24.3107 26 23.5404 26 22 26C20.4596 26 19.6893 26 19.1235 25.6533C18.8069 25.4593 18.5407 25.1931 18.3467 24.8765C18 24.3107 18 23.5404 18 22Z"
						stroke="inherit"
						strokeWidth="1.5"
					/>
					<path
						d="M6 10C6 8.45956 6 7.68934 6.34673 7.12353C6.54074 6.80693 6.80693 6.54074 7.12353 6.34673C7.68934 6 8.45956 6 10 6C11.5404 6 12.3107 6 12.8765 6.34673C13.1931 6.54074 13.4593 6.80693 13.6533 7.12353C14 7.68934 14 8.45956 14 10C14 11.5404 14 12.3107 13.6533 12.8765C13.4593 13.1931 13.1931 13.4593 12.8765 13.6533C12.3107 14 11.5404 14 10 14C8.45956 14 7.68934 14 7.12353 13.6533C6.80693 13.4593 6.54074 13.1931 6.34673 12.8765C6 12.3107 6 11.5404 6 10Z"
						stroke="inherit"
						strokeWidth="1.5"
					/>
					<path
						d="M18 10C18 8.45956 18 7.68934 18.3467 7.12353C18.5407 6.80693 18.8069 6.54074 19.1235 6.34673C19.6893 6 20.4596 6 22 6C23.5404 6 24.3107 6 24.8765 6.34673C25.1931 6.54074 25.4593 6.80693 25.6533 7.12353C26 7.68934 26 8.45956 26 10C26 11.5404 26 12.3107 25.6533 12.8765C25.4593 13.1931 25.1931 13.4593 24.8765 13.6533C24.3107 14 23.5404 14 22 14C20.4596 14 19.6893 14 19.1235 13.6533C18.8069 13.4593 18.5407 13.1931 18.3467 12.8765C18 12.3107 18 11.5404 18 10Z"
						stroke="inherit"
						strokeWidth="1.5"
					/>
				</svg>
			);
		case "clypnews":
			return (
				<svg
					viewBox="0 0 32 32"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M14.5 12H22.5M14.5 16H17M22.5 16H20M14.5 20H17M22.5 20H20"
						stroke="inherit"
						// stroke="#25292D"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
					<path
						d="M11 11.5H10C8.11438 11.5 7.17157 11.5 6.58579 12.0858C6 12.6716 6 13.6144 6 15.5V22C6 23.3807 7.11929 24.5 8.5 24.5C9.88071 24.5 11 23.3807 11 22V11.5Z"
						stroke="inherit"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
					<path
						d="M20 7.5H15C14.07 7.5 13.605 7.5 13.2235 7.60222C12.1883 7.87962 11.3796 8.68827 11.1022 9.72354C11 10.105 11 10.57 11 11.5V22C11 23.3807 9.88071 24.5 8.5 24.5H20C22.8284 24.5 24.2426 24.5 25.1213 23.6213C26 22.7426 26 21.3284 26 18.5V13.5C26 10.6716 26 9.25736 25.1213 8.37868C24.2426 7.5 22.8284 7.5 20 7.5Z"
						stroke="inherit"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
				</svg>
			);
		case "my-profile":
			return (
				<svg
					viewBox="0 0 32 32"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M10.5776 19.4816C9.1628 20.324 5.45336 22.0441 7.71266 24.1966C8.81631 25.248 10.0455 26 11.5909 26H20.4091C21.9545 26 23.1837 25.248 24.2873 24.1966C26.5466 22.0441 22.8372 20.324 21.4224 19.4816C18.1048 17.5061 13.8952 17.5061 10.5776 19.4816Z"
						stroke="inherit"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
					<path
						d="M20.5 10.5C20.5 12.9853 18.4853 15 16 15C13.5147 15 11.5 12.9853 11.5 10.5C11.5 8.01472 13.5147 6 16 6C18.4853 6 20.5 8.01472 20.5 10.5Z"
						stroke=""
						strokeWidth="1.5"
					/>
				</svg>
			);
		case "arrow-left":
			return (
				<svg
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M15 6C15 6 9.00001 10.4189 9 12C8.99999 13.5812 15 18 15 18"
						stroke="#EA9924"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
				</svg>
			);
		case "focus-camera":
			return (
				<svg
					viewBox="0 0 21 22"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						d="M0 5.375C0 4.08207 0.513614 2.84209 1.42785 1.92785C2.34209 1.01361 3.58207 0.5 4.875 0.5H6.894C7.19237 0.5 7.47852 0.618526 7.6895 0.829505C7.90047 1.04048 8.019 1.32663 8.019 1.625C8.019 1.92337 7.90047 2.20952 7.6895 2.4205C7.47852 2.63147 7.19237 2.75 6.894 2.75H4.875C4.17881 2.75 3.51113 3.02656 3.01884 3.51885C2.52656 4.01113 2.25 4.67881 2.25 5.375V7.394C2.25 7.69237 2.13147 7.97852 1.9205 8.1895C1.70952 8.40047 1.42337 8.519 1.125 8.519C0.826631 8.519 0.540483 8.40047 0.329505 8.1895C0.118526 7.97852 0 7.69237 0 7.394V5.375ZM12.981 1.625C12.981 1.32663 13.0995 1.04048 13.3105 0.829505C13.5215 0.618526 13.8076 0.5 14.106 0.5H16.125C17.4179 0.5 18.6579 1.01361 19.5721 1.92785C20.4864 2.84209 21 4.08207 21 5.375V7.394C21 7.69237 20.8815 7.97852 20.6705 8.1895C20.4595 8.40047 20.1734 8.519 19.875 8.519C19.5766 8.519 19.2905 8.40047 19.0795 8.1895C18.8685 7.97852 18.75 7.69237 18.75 7.394V5.375C18.75 4.67881 18.4734 4.01113 17.9812 3.51885C17.4889 3.02656 16.8212 2.75 16.125 2.75H14.106C13.8076 2.75 13.5215 2.63147 13.3105 2.4205C13.0995 2.20952 12.981 1.92337 12.981 1.625ZM1.125 13.481C1.42337 13.481 1.70952 13.5995 1.9205 13.8105C2.13147 14.0215 2.25 14.3076 2.25 14.606V16.625C2.25 18.074 3.426 19.25 4.875 19.25H6.894C7.19237 19.25 7.47852 19.3685 7.6895 19.5795C7.90047 19.7905 8.019 20.0766 8.019 20.375C8.019 20.6734 7.90047 20.9595 7.6895 21.1705C7.47852 21.3815 7.19237 21.5 6.894 21.5H4.875C3.58207 21.5 2.34209 20.9864 1.42785 20.0721C0.513614 19.1579 0 17.9179 0 16.625V14.606C0 14.3076 0.118526 14.0215 0.329505 13.8105C0.540483 13.5995 0.826631 13.481 1.125 13.481ZM19.875 13.481C20.1734 13.481 20.4595 13.5995 20.6705 13.8105C20.8815 14.0215 21 14.3076 21 14.606V16.625C21 17.9179 20.4864 19.1579 19.5721 20.0721C18.6579 20.9864 17.4179 21.5 16.125 21.5H14.106C13.8076 21.5 13.5215 21.3815 13.3105 21.1705C13.0995 20.9595 12.981 20.6734 12.981 20.375C12.981 20.0766 13.0995 19.7905 13.3105 19.5795C13.5215 19.3685 13.8076 19.25 14.106 19.25H16.125C16.8212 19.25 17.4889 18.9734 17.9812 18.4812C18.4734 17.9889 18.75 17.3212 18.75 16.625V14.606C18.75 14.3076 18.8685 14.0215 19.0795 13.8105C19.2905 13.5995 19.5766 13.481 19.875 13.481ZM10.5 12.5C10.8978 12.5 11.2794 12.342 11.5607 12.0607C11.842 11.7794 12 11.3978 12 11C12 10.6022 11.842 10.2206 11.5607 9.93934C11.2794 9.65804 10.8978 9.5 10.5 9.5C10.1022 9.5 9.72064 9.65804 9.43934 9.93934C9.15804 10.2206 9 10.6022 9 11C9 11.3978 9.15804 11.7794 9.43934 12.0607C9.72064 12.342 10.1022 12.5 10.5 12.5ZM4.5 14V9.5C4.5 9.10218 4.65804 8.72064 4.93934 8.43934C5.22064 8.15804 5.60218 8 6 8H7.5L8.5545 6.4175C8.69152 6.21213 8.87712 6.04375 9.09483 5.92731C9.31253 5.81087 9.55561 5.74996 9.8025 5.75H11.1975C11.4444 5.74996 11.6875 5.81087 11.9052 5.92731C12.1229 6.04375 12.3085 6.21213 12.4455 6.4175L13.5 8H15C15.3978 8 15.7794 8.15804 16.0607 8.43934C16.342 8.72064 16.5 9.10218 16.5 9.5V14C16.5 14.3978 16.342 14.7794 16.0607 15.0607C15.7794 15.342 15.3978 15.5 15 15.5H6C5.60218 15.5 5.22064 15.342 4.93934 15.0607C4.65804 14.7794 4.5 14.3978 4.5 14ZM13.5 11C13.5 10.2044 13.1839 9.44129 12.6213 8.87868C12.0587 8.31607 11.2956 8 10.5 8C9.70435 8 8.94129 8.31607 8.37868 8.87868C7.81607 9.44129 7.5 10.2044 7.5 11C7.5 11.7957 7.81607 12.5587 8.37868 13.1213C8.94129 13.6839 9.70435 14 10.5 14C11.2956 14 12.0587 13.6839 12.6213 13.1213C13.1839 12.5587 13.5 11.7957 13.5 11Z"
						fill="#EA9924"
					/>
				</svg>
			);
		case "bell":
			return (
				<svg
					width="23"
					height="24"
					viewBox="0 0 23 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<g clipPath="url(#clip0_1367_170584)">
						<path
							d="M11.5 23.5013C12.2625 23.5013 12.9938 23.1984 13.5329 22.6592C14.0721 22.12 14.375 21.3888 14.375 20.6263H8.625C8.625 21.3888 8.9279 22.12 9.46707 22.6592C10.0062 23.1984 10.7375 23.5013 11.5 23.5013ZM11.5 3.25838L10.3543 3.48982C9.05476 3.75461 7.8866 4.46028 7.04756 5.4874C6.20852 6.51452 5.75014 7.8 5.75 9.12626C5.75 10.029 5.55737 12.2844 5.09019 14.5054C4.86019 15.6079 4.54969 16.7565 4.13713 17.7513H18.8629C18.4503 16.7565 18.1412 15.6094 17.9098 14.5054C17.4426 12.2844 17.25 10.029 17.25 9.12626C17.2495 7.80025 16.791 6.51511 15.952 5.48828C15.113 4.46146 13.945 3.756 12.6457 3.49126L11.5 3.25695V3.25838ZM20.4412 17.7513C20.7618 18.3938 21.1327 18.9027 21.5625 19.1888H1.4375C1.86731 18.9027 2.23819 18.3938 2.55875 17.7513C3.8525 15.1638 4.3125 10.3913 4.3125 9.12626C4.3125 5.64751 6.785 2.74376 10.0697 2.08107C10.0496 1.88119 10.0717 1.67933 10.1344 1.48849C10.1972 1.29766 10.2992 1.1221 10.434 0.973129C10.5687 0.824158 10.7332 0.705087 10.9169 0.623596C11.1005 0.542104 11.2991 0.5 11.5 0.5C11.7009 0.5 11.8995 0.542104 12.0831 0.623596C12.2668 0.705087 12.4313 0.824158 12.566 0.973129C12.7008 1.1221 12.8028 1.29766 12.8656 1.48849C12.9283 1.67933 12.9504 1.88119 12.9303 2.08107C14.5551 2.41155 16.0157 3.2934 17.0649 4.57729C18.1141 5.86118 18.6873 7.46821 18.6875 9.12626C18.6875 10.3913 19.1475 15.1638 20.4412 17.7513Z"
							fill="#EA9924"
						/>
					</g>
					<defs>
						<clipPath id="clip0_1367_170584">
							<rect
								width="23"
								height="23"
								fill="white"
								transform="translate(0 0.5)"
							/>
						</clipPath>
					</defs>
				</svg>
			);
		case "clyppay-icon-logo":
			return (
				<svg
					viewBox="0 0 48 53"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						fillRule="evenodd"
						clipRule="evenodd"
						d="M20.5337 51.8239C22.6678 52.2802 24.9011 52.5083 27.2335 52.5083C31.5928 52.5083 35.5449 51.7419 39.0898 50.2089C42.6347 48.676 45.6048 46.4485 48 43.5263L40.7674 36.85C37.7295 40.5305 33.1324 42.8764 27.9875 42.8764C25.2705 42.8764 22.7062 42.2222 20.4438 41.0627L20.5337 51.8239ZM11.4246 26.3136C11.4246 26.8366 11.4488 27.354 11.4963 27.8646V48.1322C8.21502 46.0048 5.55658 43.2719 3.52096 39.9335C1.17365 35.9574 0 31.4784 0 26.4964C0 21.5143 1.17365 17.0592 3.52096 13.1311C5.91617 9.15504 9.17365 6.06521 13.2934 3.86162C17.4611 1.61012 22.1317 0.484375 27.3054 0.484375C31.6647 0.484375 35.5928 1.25084 39.0898 2.78378C42.6347 4.31671 45.6048 6.52031 48 9.39456L40.904 15.9447C37.8682 12.168 33.2102 9.75068 27.9875 9.75068C18.84 9.75068 11.4246 17.1661 11.4246 26.3136ZM27.9524 34.0379C32.2384 34.0379 35.7129 30.5635 35.7129 26.2775C35.7129 21.9915 32.2384 18.517 27.9524 18.517C23.6664 18.517 20.1919 21.9915 20.1919 26.2775C20.1919 30.5635 23.6664 34.0379 27.9524 34.0379Z"
						fill="#EA9924"
					/>
				</svg>
			);
		case "clyppay_logo":
			return (
				<svg
					viewBox="0 0 188 48"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
					{...props}
				>
					<path
						fillRule="evenodd"
						clipRule="evenodd"
						d="M25.1271 48C22.9755 48 20.9154 47.7896 18.9467 47.3688L18.8638 37.4421C20.9511 38.5118 23.3169 39.1153 25.8235 39.1153C30.5709 39.1153 34.8127 36.9505 37.6156 33.5542L44.2873 39.7127C42.0773 42.4088 39.337 44.4641 36.0663 45.8785C32.7956 47.2928 29.1492 48 25.1271 48ZM10.6084 25.2703C10.5643 24.7973 10.5417 24.318 10.5417 23.8335C10.5417 15.3936 17.3836 8.55176 25.8235 8.55176C30.6415 8.55176 34.9386 10.7813 37.7396 14.265L44.2873 8.221C42.0773 5.56906 39.337 3.53591 36.0663 2.12155C32.8398 0.707182 29.2155 0 25.1934 0C20.4199 0 16.1105 1.03867 12.2652 3.11602C8.46409 5.14917 5.45856 8 3.24862 11.6685C1.08287 15.2928 0 19.4033 0 24C0 28.5967 1.08287 32.7293 3.24862 36.3978C5.12707 39.4785 7.58033 42.0003 10.6084 43.9633V25.2703ZM32.9503 23.8023C32.9503 27.7568 29.7446 30.9625 25.7901 30.9625C21.8356 30.9625 18.6299 27.7568 18.6299 23.8023C18.6299 19.8478 21.8356 16.6421 25.7901 16.6421C29.7446 16.6421 32.9503 19.8478 32.9503 23.8023ZM74.313 12.3018H79.0345V34.7591H74.313V12.3018ZM62.588 35.1223C60.429 35.1223 58.4718 34.6583 56.7164 33.7301C54.9811 32.7818 53.6091 31.4803 52.6002 29.8258C51.6115 28.1511 51.1172 26.2645 51.1172 24.166C51.1172 22.0676 51.6115 20.1911 52.6002 18.5366C53.6091 16.8618 54.9811 15.5604 56.7164 14.6322C58.4718 13.6839 60.4391 13.2097 62.6183 13.2097C64.4544 13.2097 66.1089 13.5326 67.5819 14.1783C69.075 14.8239 70.326 15.7521 71.3349 16.9627L68.1872 19.8683C66.7546 18.2137 64.979 17.3864 62.8604 17.3864C61.5489 17.3864 60.3786 17.679 59.3495 18.2642C58.3205 18.8291 57.5134 19.6261 56.9283 20.6552C56.3633 21.6842 56.0808 22.8545 56.0808 24.166C56.0808 25.4776 56.3633 26.6478 56.9283 27.6769C57.5134 28.7059 58.3205 29.513 59.3495 30.0982C60.3786 30.6631 61.5489 30.9456 62.8604 30.9456C64.979 30.9456 66.7546 30.1083 68.1872 28.4335L71.3349 31.3391C70.326 32.5699 69.075 33.5081 67.5819 34.1538C66.0888 34.7995 64.4241 35.1223 62.588 35.1223ZM92.2239 35.7579L99.5785 18.476H95.0386L90.4382 29.5231L85.868 18.476H80.9952L88.0471 34.8802L87.9866 35.0315C87.6638 35.7579 87.3006 36.2724 86.897 36.5751C86.4935 36.8979 85.9689 37.0594 85.3232 37.0594C84.8591 37.0594 84.385 36.9686 83.9007 36.787C83.4366 36.6054 83.023 36.3531 82.6598 36.0303L80.9346 39.3898C81.4593 39.8539 82.1352 40.2171 82.9625 40.4794C83.7897 40.7417 84.6271 40.8729 85.4745 40.8729C87.0282 40.8729 88.3498 40.4895 89.4394 39.7228C90.5491 38.956 91.4773 37.6344 92.2239 35.7579ZM111.13 18.2339C112.643 18.2339 114.015 18.587 115.246 19.2932C116.497 19.9792 117.475 20.9578 118.182 22.229C118.888 23.48 119.241 24.9429 119.241 26.6176C119.241 28.2923 118.888 29.7652 118.182 31.0364C117.475 32.2874 116.497 33.266 115.246 33.9722C114.015 34.6583 112.643 35.0013 111.13 35.0013C109.051 35.0013 107.417 34.3455 106.227 33.034V40.6307H101.505V18.476H106.015V20.3525C107.185 18.9401 108.89 18.2339 111.13 18.2339ZM110.313 31.1272C111.523 31.1272 112.512 30.7237 113.279 29.9166C114.066 29.0893 114.459 27.9896 114.459 26.6176C114.459 25.2455 114.066 24.1559 113.279 23.3489C112.512 22.5216 111.523 22.1079 110.313 22.1079C109.102 22.1079 108.103 22.5216 107.316 23.3489C106.549 24.1559 106.166 25.2455 106.166 26.6176C106.166 27.9896 106.549 29.0893 107.316 29.9166C108.103 30.7237 109.102 31.1272 110.313 31.1272ZM145.349 14.5112C143.977 13.8857 142.353 13.5729 140.477 13.5729H131.306V34.7591H136.209V28.9178H140.477C142.353 28.9178 143.977 28.6151 145.349 28.0098C146.742 27.3843 147.811 26.4965 148.558 25.3464C149.304 24.1761 149.677 22.8142 149.677 21.2605C149.677 19.6867 149.304 18.3247 148.558 17.1746C147.811 16.0245 146.742 15.1367 145.349 14.5112ZM143.564 23.9844C142.797 24.6099 141.677 24.9227 140.204 24.9227H136.209V17.568H140.204C141.677 17.568 142.797 17.8909 143.564 18.5366C144.33 19.1621 144.714 20.07 144.714 21.2605C144.714 22.4308 144.33 23.3388 143.564 23.9844ZM159.265 18.2339C161.787 18.2339 163.724 18.8392 165.076 20.0499C166.428 21.2403 167.104 23.0462 167.104 25.4675V34.7591H162.685V32.7313C161.797 34.2446 160.143 35.0013 157.721 35.0013C156.47 35.0013 155.381 34.7894 154.453 34.3657C153.545 33.942 152.849 33.3568 152.364 32.6103C151.88 31.8637 151.638 31.0162 151.638 30.0679C151.638 28.5546 152.203 27.3641 153.333 26.4965C154.483 25.6289 156.248 25.1951 158.629 25.1951H162.382C162.382 24.166 162.07 23.3791 161.444 22.8343C160.819 22.2694 159.88 21.9869 158.629 21.9869C157.762 21.9869 156.904 22.1281 156.057 22.4106C155.229 22.6729 154.523 23.0361 153.938 23.5002L152.243 20.2012C153.131 19.5757 154.19 19.0914 155.421 18.7484C156.672 18.4054 157.953 18.2339 159.265 18.2339ZM158.902 31.8233C159.709 31.8233 160.425 31.6417 161.051 31.2785C161.676 30.8952 162.12 30.3403 162.382 29.6139V27.9493H159.144C157.207 27.9493 156.238 28.5849 156.238 29.856C156.238 30.4614 156.47 30.9456 156.934 31.3088C157.419 31.6518 158.074 31.8233 158.902 31.8233ZM179.889 35.7579L187.244 18.476H182.704L178.103 29.5231L173.533 18.476H168.66L175.712 34.8802L175.652 35.0315C175.329 35.7579 174.966 36.2724 174.562 36.5751C174.159 36.8979 173.634 37.0594 172.988 37.0594C172.524 37.0594 172.05 36.9686 171.566 36.787C171.102 36.6054 170.688 36.3531 170.325 36.0303L168.6 39.3898C169.124 39.8539 169.8 40.2171 170.627 40.4794C171.455 40.7417 172.292 40.8729 173.14 40.8729C174.693 40.8729 176.015 40.4895 177.104 39.7228C178.214 38.956 179.142 37.6344 179.889 35.7579Z"
						fill="#EA9924"
					/>
				</svg>
			);
		case "copy":
			return (
				<svg
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
          {...props}
				>
					<path
						d="M9 15C9 12.1716 9 10.7574 9.87868 9.87868C10.7574 9 12.1716 9 15 9L16 9C18.8284 9 20.2426 9 21.1213 9.87868C22 10.7574 22 12.1716 22 15V16C22 18.8284 22 20.2426 21.1213 21.1213C20.2426 22 18.8284 22 16 22H15C12.1716 22 10.7574 22 9.87868 21.1213C9 20.2426 9 18.8284 9 16L9 15Z"
						stroke="#EA9924"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
					<path
						d="M16.9999 9C16.9975 6.04291 16.9528 4.51121 16.092 3.46243C15.9258 3.25989 15.7401 3.07418 15.5376 2.90796C14.4312 2 12.7875 2 9.5 2C6.21252 2 4.56878 2 3.46243 2.90796C3.25989 3.07417 3.07418 3.25989 2.90796 3.46243C2 4.56878 2 6.21252 2 9.5C2 12.7875 2 14.4312 2.90796 15.5376C3.07417 15.7401 3.25989 15.9258 3.46243 16.092C4.51121 16.9528 6.04291 16.9975 9 16.9999"
						stroke="#EA9924"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
				</svg>
			);
		default:
			return null;
	}
};

export const Icon = React.memo(IconComponent);
