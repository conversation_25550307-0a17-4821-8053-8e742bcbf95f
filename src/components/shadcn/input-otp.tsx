import * as React from "react";
import { OTPInput as OTPInputPrimitive } from "input-otp";
import { Dot } from "lucide-react";

import { cn } from "@/lib/utils";

const OTPInput = React.forwardRef<
	React.ElementRef<typeof OTPInputPrimitive>,
	React.ComponentPropsWithoutRef<typeof OTPInputPrimitive>
>(({ className, containerClassName, ...props }, ref) => (
	<OTPInputPrimitive
		ref={ref}
		containerClassName={cn("flex items-center gap-2", containerClassName)}
		className={cn("disabled:cursor-not-allowed", className)}
		{...props}
	/>
));
OTPInput.displayName = "OTPInput";

const OTPInputGroup = React.forwardRef<
	React.ElementRef<"div">,
	React.ComponentPropsWithoutRef<"div">
>(({ className, ...props }, ref) => (
	<div ref={ref} className={cn("flex items-center", className)} {...props} />
));
OTPInputGroup.displayName = "OTPInputGroup";

const OTPInputSlot = React.forwardRef<
	React.ElementRef<"div">,
	React.ComponentPropsWithoutRef<"div"> & {
		char?: string | null;
		isActive?: boolean;
		hasFakeCaret?: boolean;
		placeholderChar?: string | null;
	}
>(({ className, char, isActive, hasFakeCaret, ...props }, ref) => (
	<div
		ref={ref}
		className={cn(
			"relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",
			char && "border-primary",
			isActive && "border-primary ring-2 ring-offset-1 ring-amber-500",
			hasFakeCaret && "animate-pulse",
			className,
		)}
		{...props}
	>
		{char}
	</div>
));
OTPInputSlot.displayName = "OTPInputSlot";

const OTPInputSeparator = React.forwardRef<
	React.ElementRef<"div">,
	React.ComponentPropsWithoutRef<"div">
>(({ className, ...props }, ref) => (
	<div
		ref={ref}
		className={cn(
			"flex items-center justify-center text-muted-foreground",
			className,
		)}
		{...props}
	>
		<Dot className="h-4 w-4" />
	</div>
));
OTPInputSeparator.displayName = "OTPInputSeparator";

export { OTPInput, OTPInputGroup, OTPInputSlot, OTPInputSeparator };
