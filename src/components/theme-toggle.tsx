import React from "react";
import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";
import useThemeStore, { Theme } from "@/store/theme-store";
import { cn } from "@/lib/utils";

interface ThemeToggleProps {
	variant?: "icon" | "button" | "dropdown";
	size?: "sm" | "md" | "lg";
	className?: string;
	showLabel?: boolean;
}

export function ThemeToggle({ 
	variant = "icon", 
	size = "md", 
	className,
	showLabel = false 
}: ThemeToggleProps) {
	const { theme, setTheme } = useThemeStore();

	const themes: { value: Theme; label: string; icon: React.ReactElement }[] = [
		{ 
			value: "light", 
			label: "Light", 
			icon: <Sun className={cn("h-4 w-4", size === "sm" && "h-3 w-3", size === "lg" && "h-5 w-5")} />
		},
		{ 
			value: "dark", 
			label: "Dark", 
			icon: <Moon className={cn("h-4 w-4", size === "sm" && "h-3 w-3", size === "lg" && "h-5 w-5")} />
		},
		{ 
			value: "system", 
			label: "System", 
			icon: <Monitor className={cn("h-4 w-4", size === "sm" && "h-3 w-3", size === "lg" && "h-5 w-5")} />
		},
	];

	const currentTheme = themes.find(t => t.value === theme) || themes[0];

	const handleToggle = () => {
		const currentIndex = themes.findIndex(t => t.value === theme);
		const nextIndex = (currentIndex + 1) % themes.length;
		setTheme(themes[nextIndex].value);
	};

	if (variant === "icon") {
		return (
			<Button
				variant="ghost"
				size={size === "sm" ? "sm" : size === "lg" ? "lg" : "icon"}
				onClick={handleToggle}
				className={cn(
					"relative overflow-hidden",
					className
				)}
				aria-label={`Switch to ${themes[(themes.findIndex(t => t.value === theme) + 1) % themes.length].label.toLowerCase()} theme`}
			>
				<AnimatePresence mode="wait" initial={false}>
					<motion.div
						key={theme}
						initial={{ scale: 0, rotate: -180 }}
						animate={{ scale: 1, rotate: 0 }}
						exit={{ scale: 0, rotate: 180 }}
						transition={{ duration: 0.1, type: "spring", stiffness: 200 }}
					>
						{currentTheme.icon}
					</motion.div>
				</AnimatePresence>
				{showLabel && (
					<span className="ml-2 text-sm">
						{currentTheme.label}
					</span>
				)}
			</Button>
		);
	}

	if (variant === "button") {
		return (
			<div className={cn("flex items-center gap-1 p-1 bg-muted rounded-lg", className)}>
				{themes.map((themeOption) => (
					<Button
						key={themeOption.value}
						variant={theme === themeOption.value ? "default" : "ghost"}
						size="sm"
						onClick={() => setTheme(themeOption.value)}
						className={cn(
							"flex items-center gap-2 transition-all",
							theme === themeOption.value && "bg-background shadow-sm"
						)}
					>
						{themeOption.icon}
						{showLabel && (
							<span className="text-xs">{themeOption.label}</span>
						)}
					</Button>
				))}
			</div>
		);
	}

	return null; // For dropdown variant - can be implemented later if needed
}

// Simplified toggle for quick switching between light/dark
export function SimpleThemeToggle({ className }: { className?: string }) {
	const { resolvedTheme, toggleTheme } = useThemeStore();

	return (
		<Button
			variant="ghost"
			size="icon"
			onClick={toggleTheme}
			className={cn("relative overflow-hidden", className)}
			aria-label={`Switch to ${resolvedTheme === "light" ? "dark" : "light"} theme`}
		>
			<AnimatePresence mode="wait" initial={false}>
				<motion.div
					key={resolvedTheme}
					initial={{ scale: 0, rotate: -180 }}
					animate={{ scale: 1, rotate: 0 }}
					exit={{ scale: 0, rotate: 180 }}
					transition={{ duration: 0.2, type: "spring", stiffness: 200 }}
				>
					{resolvedTheme === "light" ? (
						<Sun className="h-4 w-4" />
					) : (
						<Moon className="h-4 w-4" />
					)}
				</motion.div>
			</AnimatePresence>
		</Button>
	);
}
