const ComingSoon = () => {
	return (
		<div className="max-w-2xl w-full mx-auto bg-card rounded-lg p-8 text-center">
			{/* Main Heading with custom primary color gradient using your color code */}
			<h1
				className="text-6xl md:text-7xl font-bold mb-6 bg-clip-text text-transparent"
				style={{
					backgroundImage:
						"linear-gradient(to right, #ea9924, #f8b04c)",
				}}
			>
				COMING
				<br />
				SOON
			</h1>

			{/* Main Message */}
			<div className="space-y-4 mb-8">
				<p className="text-lg text-muted-foreground">
					We're working{" "}
					<span className="font-medium text-primary">tirelessly</span>{" "}
					to build something amazing for you.
				</p>
				<p className="text-lg text-muted-foreground">
					Join our mailing list or follow us for updates!
				</p>
			</div>

			{/* Email Signup */}
			<div className="flex flex-col sm:flex-row gap-3 justify-center mb-8">
				<input
					type="email"
					placeholder="Your email address"
					className="flex-grow max-w-md px-4 py-3 rounded-lg border border-primary focus:outline-none focus:ring-2 focus:ring-primary"
				/>
				<button className="px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-all">
					Notify Me
				</button>
			</div>

			{/* Footer message */}
			<div className="mt-8 text-gray-500 text-sm">
				<p>Our team is working hard to launch soon. Stay tuned!</p>
			</div>
		</div>
	);
};

export default ComingSoon;
