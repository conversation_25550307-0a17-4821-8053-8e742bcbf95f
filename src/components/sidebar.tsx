"use client";

import { LogOut } from "lucide-react";
import cn from "@/utils/class-names";
import { Icon, IconProps } from "@/components/icons";
import { GenericList } from "./generic-list";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { ResponseStatus } from "@/config/enums";
import { notify } from "@/utils/notify";


type IconName = IconProps["name"];


interface MenuItem {
	id: string;
	icon: IconName;
	label: string;
	href: string;
}

interface SidebarProps {
	isOpen: boolean;
	onClose: () => void;
}

export function Sidebar({ isOpen, onClose }: SidebarProps) {
	const location = useLocation();
	const navigate = useNavigate();


	const menuItems: MenuItem[] = [
		{ id: "home", icon: "home", label: "Home", href: "/" },
		{ id: "reports", icon: "report", label: "Reports", href: "/reports" },
		{ id: "clyphub", icon: "clyphub", label: "Clyphub", href: "/clyphub" },
		{
			id: "clypnews",
			icon: "clypnews",
			label: "Clypnews",
			href: "/clypnews",
		},
		{
			id: "profile",
			icon: "my-profile",
			label: "My Profile",
			href: "/profile",
		},
	];


	const renderMenuItem = (item: MenuItem) => {

		const isActive =
			location.pathname === item.href ||
			(item.href !== "/" && location.pathname.startsWith(item.href));

		return (<Link
			to={item.href}
			className={cn(
				"flex items-center gap-3 rounded-lg px-3 py-3 transition-colors",
				isActive
					? "bg-primary/10 text-primary dark:bg-primary/20"
					: "text-muted-foreground hover:bg-muted hover:text-foreground dark:hover:bg-muted/70",
			)}
		>
			<Icon
				name={item.icon}
				className={cn(
					"h-5 w-5",
					isActive
						? "text-primary fill-primary stroke-primary"
						: "stroke-muted-foreground",
				)}
			/>
			<span>{item.label}</span>
		</Link>
		);
	};

	const handleLogout = () => {
		localStorage.removeItem("auth_token");

		notify("Logged out successfully", ResponseStatus.SUCCESS);

		navigate("/login");
	};
	const renderLogout = () => (
		<button
			onClick={handleLogout}
			className="w-full flex items-center gap-3 rounded-lg px-3 py-3 text-muted-foreground transition-colors hover:bg-muted hover:text-foreground dark:hover:bg-muted/70 cursor-pointer"
		>
			<LogOut className="h-5 w-5 text-destructive" />
			<span>Logout</span>
		</button>
	);

	return (
		<>			{/* Mobile backdrop */}
			{isOpen && (
				<div
					className="fixed inset-0 z-30 bg-black/50 dark:bg-black/70 md:hidden"
					onClick={onClose}
					aria-hidden="true"
				/>
			)}{/* Sidebar */}
			<aside
				className={cn(
					"fixed inset-y-0 left-0 z-30 w-80 transform border-r border-border bg-background dark:bg-card pt-16 transition-transform duration-300 ease-in-out md:static md:translate-x-0",
					isOpen ? "translate-x-0" : "-translate-x-full",
				)}
			>
				<div className="flex h-full flex-col justify-between p-4">
					<div className="space-y-6">
						<nav className="flex flex-col">
							<GenericList
								items={menuItems}
								renderItem={renderMenuItem}
								spacing="tight"
								className="flex-col"
							/>
						</nav>
					</div>
					<div>{renderLogout()}</div>
				</div>
			</aside>
		</>
	);
}
