import { Button } from "./custom/button";
import { useModal } from "./modal-view/use-modal";

function ExampleModalContent() {
    const { closeModal } = useModal();
    
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">Modal Title</h2>
        <p className="mb-6">This is an example modal content.</p>
        <Button 
          onClick={closeModal} 
          className="w-full"
        >
          Close Modal
        </Button>
      </div>
    );
  }
  
  
  export function OpenModalButton() {
    const { openModal } = useModal();
    
    const handleOpenModal = () => {
      openModal({
        view: <ExampleModalContent />,
        size: "md",
        position: "center",
        animation: "fade"
      });
    };
    
    return (
      <Button onClick={handleOpenModal}>
        Open Modal
      </Button>
    );
  }