const QuickActionSkeleton = () => {
	return (
		<div className="bg-white p-6 rounded-lg shadow animate-pulse">
			{/* Section Title */}
			<div className="h-6 w-36 bg-gray-200 rounded-md mb-6"></div>

			{/* Quick Action Cards Grid */}
			<div className="grid grid-cols-2 gap-4">
				{/* 4 Quick Action Cards */}
				{[...Array(4)].map((_, i) => (
					<div
						key={i}
						className="bg-gray-100 p-4 rounded-lg flex flex-col items-center justify-center"
					>
						<div className="h-10 w-10 bg-gray-200 rounded-full mb-2 animate-pulse"></div>
						<div className="h-4 w-20 bg-gray-200 rounded-md animate-pulse"></div>
					</div>
				))}
			</div>
		</div>
	);
};



export default QuickActionSkeleton;
