const RecentTransactionsSkeleton = () => {
	return (
		<div className="animate-pulse">
			{/* Transaction items */}
			{[...Array(5)].map((_, i) => (
				<div
					key={i}
					className="flex justify-between items-center py-4 border-b border-border last:border-b-0"
				>
					{/* Left side with icon and transaction details */}
					<div className="flex items-center space-x-3">
						<div className="h-10 w-10 bg-muted rounded-full"></div>
						<div>
							<div className="h-4 w-40 bg-muted rounded-md mb-2"></div>
							<div className="h-3 w-32 bg-muted rounded-md"></div>
						</div>
					</div>

					{/* Right side with amount and status */}
					<div className="flex flex-col items-end">
						<div className="h-5 w-24 bg-muted rounded-md mb-1"></div>
						<div className="h-4 w-20 bg-muted rounded-md"></div>
					</div>
				</div>
			))}
		</div>
	);
};


export default RecentTransactionsSkeleton;
