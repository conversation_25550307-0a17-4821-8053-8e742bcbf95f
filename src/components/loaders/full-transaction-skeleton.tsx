import RecentTransactionsSkeleton from "./recent-transactions-skeleton";

const FullTransactionSkeleton = () => {
  return (
    <div className="bg-card dark:bg-card p-6 rounded-2xl shadow-sm h-full border border-border">
      {/* Header with title and "See all" link */}
      <div className="flex justify-between items-center mb-6 animate-pulse">
        <div className="h-6 w-48 bg-muted dark:bg-muted rounded-md"></div>
        <div className="h-4 w-16 bg-muted dark:bg-muted rounded-md"></div>
      </div>
      
      {/* Use the existing skeleton for transaction items */}
      <RecentTransactionsSkeleton />
    </div>
  );
};

export default FullTransactionSkeleton;
