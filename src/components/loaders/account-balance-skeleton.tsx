const AccountBalanceSkeleton = () => {
	return (
		<div className="bg-card p-6 rounded-lg shadow animate-pulse">
			{/* Crypto dropdown skeleton */}
			<div className="inline-block mb-6">
				<div className="h-10 w-48 bg-gray-200 rounded-full"></div>
			</div>

			{/* Balance heading and eye icon */}
			<div className="flex justify-between items-center mb-4">
				<div className="h-6 w-48 bg-gray-300 rounded-md"></div>
				<div className="h-6 w-6 bg-gray-300 rounded-full"></div>
			</div>

			{/* Balance amount */}
			<div className="h-14 w-20 bg-gray-200 rounded-md mb-10"></div>

			{/* Action buttons */}
			<div className="flex justify-between">
				{["Buy", "Send", "Receive", "Sell", "Swap"].map((_, i) => (
					<div key={i} className="flex flex-col items-center">
						<div className="h-12 w-12 bg-gray-300 rounded-full mb-2"></div>
						<div className="h-4 w-16 bg-gray-300 rounded-md"></div>
					</div>
				))}
			</div>
		</div>
	);
};

export default AccountBalanceSkeleton;
