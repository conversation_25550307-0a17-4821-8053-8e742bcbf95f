import React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface DropdownSelectorProps {
	label: string;
	loading?: boolean;
	disabled?: boolean;
	action: () => void;
	active?: boolean;
	className?: string;
	selectedLabel?: string;
	icon?: string | React.ReactNode;
	labelClassName?: string;
	iconClassName?: string;
}

export const DropdownSelector: React.FC<DropdownSelectorProps> = ({
	label = "Choose",
	loading = false,
	disabled = false,
	active = false,
	labelClassName,
	iconClassName,
	selectedLabel,
	icon,
	className,
	action = () => {},
}) => {
	return (
		<button
			disabled={disabled || loading}
			className={cn(
				"my-2 w-full bg-white dark:bg-gray-800 border",
				active
					? "border-primary dark:border-primary"
					: "border-gray-100 dark:border-gray-700",
				"flex flex-row p-4 justify-between items-center rounded-full",
				disabled && "opacity-50 cursor-not-allowed",
				className,
			)}
			onClick={action}
		>
			<div className="flex flex-row items-center gap-x-2 max-w-[85%]">
				{loading ? (
					<div className="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
				) : icon && typeof icon === "string" ? (
					<img
						className={cn(
							"w-6 h-6 bg-gray-300 rounded-full",
							iconClassName,
						)}
						src={icon}
						alt="Icon"
					/>
				) : (
					<div className={cn("mr-2", iconClassName)}>{icon}</div>
				)}
				<span
					className={cn(
						"w-fit text-gray-500 dark:text-gray-400 truncate",
						labelClassName,
					)}
				>
					{loading
						? "Loading..."
						: selectedLabel
						? selectedLabel
						: label}
				</span>
			</div>

			<ChevronDown className="text-gray-500 dark:text-gray-400 w-5 h-5" />
		</button>
	);
};

export default DropdownSelector;
