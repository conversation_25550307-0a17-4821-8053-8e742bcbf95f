import { Menu } from "lucide-react";
import { Icon } from "./icons";
import Container from "@/layouts/section-container";
import { Link } from "react-router-dom";
import { SimpleThemeToggle } from "./theme-toggle";
import chat from "@/assets/images/chat.png";
interface NavbarProps {
	onMenuClick?: () => void;
}

export function Navbar({ onMenuClick }: NavbarProps) {
	return (
		<header className="sticky top-0 z-50 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<Container
				sectionClassName="max-w-screen-2xl"
				className="flex h-16 items-center justify-between px-4 md:px-8"
			>
				{/* Mobile: Menu + Logo */}
				<div className="flex items-center gap-3 md:hidden flex-shrink-0">
					<button
						className="rounded-lg p-2 text-muted-foreground hover:bg-muted hover:text-foreground transition-colors"
						onClick={onMenuClick}
					>
						<Menu className="h-5 w-5" />
						<span className="sr-only">Toggle menu</span>
					</button>
					<Link to="/" className="flex items-center">
						<img
							src="/favicon-512.png"
							alt="Clyppay Logo"
							className="size-7 object-contain"
						/>
					</Link>
				</div>

				{/* Desktop: Logo */}
				<Link
					to="/"
					className="hidden md:flex md:items-center flex-shrink-0 hover:opacity-80 transition-opacity"
				>
					<Icon name="clyppay_logo" className="w-28 sm:w-36 h-auto" />
				</Link>

				{/* Navigation Icons */}
				<div className="flex items-center gap-2 md:gap-4">
					{/* Chat Icon */}
					<Link
						to="/chatbot"
						className="p-2 rounded-lg hover:bg-muted transition-colors group relative"
						title="Chat"
					>
						<img
							src={chat}
							alt="Chat"
							className="size-5 md:size-6 object-contain group-hover:scale-110 transition-transform"
						/>
						<span className="sr-only">Chat</span>
					</Link>

					{/* Notifications Icon */}
					<Link
						to="/notifications"
						className="p-2 rounded-lg hover:bg-muted transition-colors group relative"
						title="Notifications"
					>
						<Icon
							name="bell"
							className="size-5 md:size-6 group-hover:scale-110 transition-transform"
						/>
						<span className="sr-only">Notifications</span>
					</Link>

					{/* Theme Toggle */}
					<div className="ml-2">
						<SimpleThemeToggle />
					</div>
				</div>
			</Container>
		</header>
	);
}
