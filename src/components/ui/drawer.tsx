"use client";

import * as React from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";


type DrawerContextType = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  toggle: () => void;
};

const DrawerContext = React.createContext<DrawerContextType | undefined>(
  undefined
);

export function useDrawer() {
  const context = React.useContext(DrawerContext);
  if (!context) {
    throw new Error("useDrawer must be used within a DrawerProvider");
  }
  return context;
}

interface DrawerProviderProps {
  children: React.ReactNode;
  defaultOpen?: boolean;
}

export function DrawerProvider({
  children,
  defaultOpen = false,
}: DrawerProviderProps) {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);

  const toggle = React.useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  return (
    <DrawerContext.Provider value={{ isOpen, setIsOpen, toggle }}>
      {children}
    </DrawerContext.Provider>
  );
}


interface DrawerProps {
  children: React.ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
  position?: "right" | "left";
  width?: string;
  className?: string;
  showOverlay?: boolean;
  closeOnOverlayClick?: boolean;
  duration?: number;
}

export function Drawer({
  children,
  isOpen: controlledIsOpen,
  onClose,
  position = "right",
  width = "480px",
  className,
  showOverlay = true,
  closeOnOverlayClick = true,
  duration = 300,
}: DrawerProps) {
  
  const drawerContext = React.useContext(DrawerContext);
  const isControlled = controlledIsOpen !== undefined;

  const isOpen = isControlled
    ? controlledIsOpen
    : drawerContext?.isOpen || false;

  const handleClose = React.useCallback(() => {
    if (isControlled && onClose) {
      onClose();
    } else if (drawerContext) {
      drawerContext.setIsOpen(false);
    }
  }, [isControlled, onClose, drawerContext]);

  
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  
  React.useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        handleClose();
      }
    };
    window.addEventListener("keydown", handleEsc);
    return () => {
      window.removeEventListener("keydown", handleEsc);
    };
  }, [isOpen, handleClose]);

  
  const drawerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (isOpen && drawerRef.current) {
      const focusableElements = drawerRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements.length) {
        (focusableElements[0] as HTMLElement).focus();
      }
    }
  }, [isOpen]);

  const translateValue =
    position === "right" ? "translateX(100%)" : "translateX(-100%)";

  return (
    <>
      {/* Overlay */}
      {showOverlay && (
        <div
          className={cn(
            "fixed inset-0 bg-black/50 z-40 transition-opacity",
            isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
          )}
          onClick={closeOnOverlayClick ? handleClose : undefined}
          style={{ transitionDuration: `${duration}ms` }}
          aria-hidden="true"
        />
      )}

      {/* Drawer */}
      <div
        ref={drawerRef}
        className={cn(
          "fixed top-0 h-full bg-white z-50 transition-transform shadow-xl",
          position === "right" ? "right-0" : "left-0",
          className
        )}
        style={{
          width: position === "right" || position === "left" ? width : "100%",
          maxWidth: "100%",
          transform: isOpen ? "translateX(0)" : translateValue,
          transitionDuration: `${duration}ms`,
        }}
        role="dialog"
        aria-modal="true"
      >
        {children}
      </div>
    </>
  );
}


interface DrawerHeaderProps {
  children?: React.ReactNode;
  title?: string;
  showCloseButton?: boolean;
  onClose?: () => void;
  className?: string;
}

export function DrawerHeader({
  children,
  
  showCloseButton = false,
  onClose,
  className,
}: DrawerHeaderProps) {
  const drawerContext = React.useContext(DrawerContext);

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else if (drawerContext) {
      drawerContext.setIsOpen(false);
    }
  };

  return (
    <div
      className={cn(
        "p-6",
        
        className
      )}
    >
      {/* {title ? <h2 className="text-2xl font-semibold">{title}</h2> : null} */}
      {children}
      {showCloseButton && (
        <Button
          variant="ghost"
          size="icon"
          onClick={handleClose}
          className="text-gray-500 hover:text-gray-700 ml-auto"
          aria-label="Close dialog"
        >
          <X className="h-6 w-6" />
        </Button>
      )}
    </div>
  );
}

interface DrawerContentProps {
  children: React.ReactNode;
  className?: string;
}

export function DrawerContent({ children, className }: DrawerContentProps) {
  return <div className={cn("p-6", className)}>{children}</div>;
}

interface DrawerFooterProps {
  children: React.ReactNode;
  className?: string;
}

export function DrawerFooter({ children, className }: DrawerFooterProps) {
  return (
    <div className={cn("p-6 border-t mt-auto", className)}>{children}</div>
  );
}

interface DrawerTriggerProps {
  children: React.ReactNode;
  className?: string;
}

export function DrawerTrigger({ children, className }: DrawerTriggerProps) {
  const drawerContext = React.useContext(DrawerContext);

  if (!drawerContext) {
    throw new Error("DrawerTrigger must be used within a DrawerProvider");
  }

  const { toggle } = drawerContext;

  return (
    <div onClick={toggle} className={cn("cursor-pointer", className)}>
      {children}
    </div>
  );
}
