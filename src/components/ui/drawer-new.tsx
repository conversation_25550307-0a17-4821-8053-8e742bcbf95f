import * as React from "react";
import { cn } from "@/lib/utils";


export const drawerClasses = {
  overlay: "fixed inset-0 bg-black/50 dark:bg-black/60 z-40 transition-opacity",
  placement: {
    top: "top-0 left-0 right-0",
    right: "top-0 right-0 h-full",
    bottom: "bottom-0 left-0 right-0",
    left: "top-0 left-0 h-full",
  },
  sizeOfYAxisDrawer: {
    sm: "h-1/4",
    DEFAULT: "h-1/3",
    lg: "h-1/2",
    xl: "h-2/3",
    full: "h-screen",
  },
  sizeOfXAxisDrawer: {
    sm: "w-full sm:w-64",
    DEFAULT: "w-full sm:w-80",
    lg: "w-full sm:w-96",
    xl: "w-full sm:w-[480px]",
    full: "w-screen",
  },
};

export type DrawerSize = "sm" | "DEFAULT" | "lg" | "xl" | "full";

export type DrawerProps = {
  /** Whether the Drawer is open or not */
  isOpen: boolean;
  /** Called when drawer is closed */
  onClose: () => void;
  /** Drawer placement: left, right, top, bottom */
  placement?: keyof typeof drawerClasses.placement;
  /** Preset sizes: sm, DEFAULT, lg, xl, full */
  size?: DrawerSize;
  /** Custom size (overrides size prop) */
  customSize?: string;
  /** Custom class for overlay */
  overlayClassName?: string;
  /** Custom class for container */
  containerClassName?: string;
  /** Custom class for the drawer */
  className?: string;
  /** Duration of transition animation in ms */
  duration?: number;
  /** Whether to close when clicking the overlay */
  closeOnOverlayClick?: boolean;
  /** Children to render inside the drawer */
  children: React.ReactNode;
};

export function Drawer({
  isOpen,
  onClose,
  placement = "right",
  size = "DEFAULT",
  customSize,
  overlayClassName,
  containerClassName,
  className,
  duration = 300,
  closeOnOverlayClick = true,
  children,
}: DrawerProps) {
  // Handle ESC key
  React.useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    window.addEventListener("keydown", handleEsc);
    return () => window.removeEventListener("keydown", handleEsc);
  }, [isOpen, onClose]);

  // Prevent scrolling when drawer is open
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  // Focus management
  const drawerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (isOpen && drawerRef.current) {
      const focusableElements = drawerRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements.length) {
        (focusableElements[0] as HTMLElement).focus();
      }
    }
  }, [isOpen]);

  
  const getTransformValue = () => {
    switch (placement) {
      case "top":
        return isOpen ? "translateY(0)" : "translateY(-100%)";
      case "right":
        return isOpen ? "translateX(0)" : "translateX(100%)";
      case "bottom":
        return isOpen ? "translateY(0)" : "translateY(100%)";
      case "left":
        return isOpen ? "translateX(0)" : "translateX(-100%)";
      default:
        return "";
    }
  };

  // Calculate drawer size and return both style and className
  const getSizeValue = () => {
    
    if (customSize) {
      if (placement === "top" || placement === "bottom") {
        return { style: { height: customSize }, className: "" };
      }
      return { style: { width: customSize }, className: "" };
    }

    
    if (placement === "top" || placement === "bottom") {
      return {
        style: {},
        className: drawerClasses.sizeOfYAxisDrawer[size]
      };
    }

    return {
      style: {},
      className: drawerClasses.sizeOfXAxisDrawer[size]
    };
  };

  const { style: sizeStyle, className: sizeClassName } = getSizeValue();
  return (
    <>
      {/* Overlay */}
      <div
        className={cn(
          drawerClasses.overlay,
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none",
          overlayClassName
        )}
        onClick={closeOnOverlayClick ? onClose : undefined}
        style={{ transitionDuration: `${duration}ms` }}
        aria-hidden="true"
      />      {/* Drawer */}
      <div
        ref={drawerRef}
        className={cn(
          "fixed bg-white dark:bg-card text-foreground dark:text-foreground z-50 transition-transform shadow-xl border-l dark:border-border",
          drawerClasses.placement[placement],
          sizeClassName,
          containerClassName,
          className
        )}
        style={{
          ...sizeStyle,
          transform: getTransformValue(),
          transitionDuration: `${duration}ms`,
        }}
        role="dialog"
        aria-modal="true"
      >
        {children}
      </div>
    </>
  );
}

export default Drawer;