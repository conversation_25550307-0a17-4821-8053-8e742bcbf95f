"use client";

import { useEffect, useRef, useState } from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { createPortal } from "react-dom";
import { ModalAnimations, ModalPositions } from "../modal-view/use-modal";

const modalVariants = cva(
	"bg-white dark:bg-card rounded-xl shadow-lg relative overflow-hidden flex flex-col",
	{
		variants: {
			size: {
				xs: "max-w-xs w-full",
				sm: "max-w-sm w-full",
				md: "max-w-md w-full",
				lg: "max-w-lg w-full",
				xl: "max-w-xl w-full",
				"2xl": "max-w-2xl w-full",
				"3xl": "max-w-3xl w-full",
				"4xl": "max-w-4xl w-full",
				"5xl": "max-w-5xl w-full",
				full: "max-w-full w-full",
				auto: "w-auto",
			},
			fullHeight: {
				true: "h-full",
				false: "",
			},
			maxHeight: {
				true: "max-h-[calc(100vh-2rem)]",
				false: "",
			},
		},
		defaultVariants: {
			size: "md",
			fullHeight: false,
			maxHeight: true,
		},
	},
);

export interface ModalProps extends VariantProps<typeof modalVariants> {
	isOpen: boolean;
	onClose: () => void;
	children: React.ReactNode;
	className?: string;
	contentClassName?: string;
	hideCloseButton?: boolean;
	closeOnOutsideClick?: boolean;
	closeOnEsc?: boolean;
	preventScroll?: boolean;
	showOverlay?: boolean;
	overlayClassName?: string;
	position?: ModalPositions;
	animation?: ModalAnimations;
}

export function Modal({
	isOpen,
	onClose,
	children,
	className,
	contentClassName,
	hideCloseButton = false,
	closeOnOutsideClick = true,
	closeOnEsc = true,
	preventScroll = true,
	showOverlay = true,
	overlayClassName,
	position = "center",
	animation = "fade",
	size,
	fullHeight,
	maxHeight,
}: ModalProps) {
	const [mounted, setMounted] = useState(false);
	const modalRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (e: MouseEvent) => {
			if (
				closeOnOutsideClick &&
				isOpen &&
				modalRef.current &&
				!modalRef.current.contains(e.target as Node)
			) {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [closeOnOutsideClick, isOpen, onClose]);

	useEffect(() => {
		const handleEsc = (e: KeyboardEvent) => {
			if (closeOnEsc && e.key === "Escape" && isOpen) {
				onClose();
			}
		};

		window.addEventListener("keydown", handleEsc);
		return () => window.removeEventListener("keydown", handleEsc);
	}, [closeOnEsc, isOpen, onClose]);

	useEffect(() => {
		if (!preventScroll) return;

		if (isOpen) {
			document.body.style.overflow = "hidden";
			document.body.style.paddingRight = "var(--scrollbar-width)";
		} else {
			document.body.style.overflow = "";
			document.body.style.paddingRight = "";
		}

		return () => {
			document.body.style.overflow = "";
			document.body.style.paddingRight = "";
		};
	}, [isOpen, preventScroll]);

	useEffect(() => {
		const scrollbarWidth =
			window.innerWidth - document.documentElement.clientWidth;
		document.documentElement.style.setProperty(
			"--scrollbar-width",
			`${scrollbarWidth}px`,
		);
	}, []);

	useEffect(() => {
		setMounted(true);
	}, []);

	if (!mounted || !isOpen) return null;

	const animationClasses = {
		fade: "animate-in fade-in duration-300",
		zoom: "animate-in zoom-in-95 duration-300",
		"slide-up": "animate-in slide-in-from-bottom-4 duration-300",
		"slide-down": "animate-in slide-in-from-top-4 duration-300",
		none: "",
	};

	const positionClasses = {
		center: "items-center justify-center",
		top: "items-start justify-center pt-4",
		bottom: "items-end justify-center pb-4",
	};

	return createPortal(
		<div
			className={cn(
				"fixed inset-0 z-50 flex p-4 overflow-auto",
				positionClasses[position],
				animationClasses[animation],
				className,
			)}
		>
			{/* Overlay */}
			{showOverlay && (
				<div
					className={cn(
						"fixed inset-0 bg-black/50 backdrop-blur-sm",
						animationClasses[animation],
						overlayClassName,
					)}
					aria-hidden="true"
				/>
			)}

			{/* Modal */}
			<div
				ref={modalRef}
				role="dialog"
				aria-modal="true"
				className={cn(
					modalVariants({ size, fullHeight, maxHeight }),
					"z-50",
					animationClasses[animation],
					className,
				)}
			>
				{/* Close button */}
				{!hideCloseButton && (
					<button
						onClick={onClose}
						className="absolute right-4 top-4 z-10 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary"
						aria-label="Close modal"
					>
						<X size={20} />
					</button>
				)}

				{/* Content */}
				<div className={cn("flex-1 overflow-auto", contentClassName)}>
					{children}
				</div>
			</div>
		</div>,
		document.body,
	);
}
