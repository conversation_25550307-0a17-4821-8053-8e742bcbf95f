import React from "react";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";

export interface PinSuccessStepProps {
	onProceed: () => void;
}

	const firstname = localStorage.getItem("firstName")
	const lastname= localStorage.getItem("lastName")

const PinSuccessStep: React.FC<PinSuccessStepProps> = ({ onProceed }) => {
	return (
		<div className="flex flex-col items-center justify-center gap-4 p-4 flex-grow">
			<TransactionSuccess
				title="Transaction Pin Created Successfully!"
				message={`Hi ${firstname} ${lastname}, you have successfully created your transaction pin.`}
				buttonText="Proceed"
				onNextAction={onProceed}
			/>
		</div>
	);
};

export default PinSuccessStep;
