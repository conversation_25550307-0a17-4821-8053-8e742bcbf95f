import { create } from 'zustand';

export type DrawerPlacements = 'left' | 'right' | 'top' | 'bottom';

interface DrawerState {
  isOpen: boolean;
  view: React.ReactNode;
  placement: DrawerPlacements;
  customSize?: string;
  openDrawer: (params: {
    view: React.ReactNode;
    placement?: DrawerPlacements;
    customSize?: string;
  }) => void;
  closeDrawer: () => void;
}

export const useDrawerStore = create<DrawerState>((set) => ({
  isOpen: false,
  view: null,
  placement: 'right',
  customSize: '100%',
  
  openDrawer: ({ view, placement = 'right', customSize = '100%' }) => {
    set({
      isOpen: true,
      view,
      placement,
      customSize,
    });
  },
  
  closeDrawer: () => {
    set((state) => ({
      ...state,
      isOpen: false,
    }));
  },
}));

export function useDrawer() {
  const { 
    isOpen, 
    view, 
    placement, 
    customSize, 
    openDrawer, 
    closeDrawer 
  } = useDrawerStore();

  return {
    isOpen,
    view,
    placement,
    customSize,
    openDrawer,
    closeDrawer,
  };
}