import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useDrawer } from "./use-drawer";
import Drawer from "../ui/drawer-new";

export default function GlobalDrawer() {
	const { isOpen, view, placement, customSize, closeDrawer } = useDrawer();
	const location = useLocation();

	useEffect(() => {
		
		if (isOpen) {
			closeDrawer();
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [location.pathname]);
	return (
		<Drawer
			isOpen={isOpen}
			onClose={closeDrawer}
			placement={placement}
			customSize={customSize}
			overlayClassName="dark:bg-opacity-40 dark:backdrop-blur-md"
			containerClassName="dark:bg-card dark:text-foreground overflow-y-scroll"
		>
			{view}
		</Drawer>
	);
}
