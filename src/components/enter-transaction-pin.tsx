import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import OTPInput from "@/components/custom/input/otp-input";

export interface EnterPinStepProps {
	onPinVerified: () => void;
}

const EnterPinStep: React.FC<EnterPinStepProps> = ({ onPinVerified }) => {
	const [pin, setPin] = useState<string>("");
	const [loading, setLoading] = useState<boolean>(false);

	const handlePinChange = (value: string) => {
		setPin(value);
	};

	const handlePinComplete = (value: string) => {
		console.log("PIN entered:", value);
		
	};

	const handleVerifyPin = async () => {
		if (pin.length !== 4) return;

		setLoading(true);
		try {
			await new Promise((resolve) => setTimeout(resolve, 800));

			onPinVerified();
		} catch (error) {
			console.error("Failed to verify PIN:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="flex flex-col gap-4 p-4">
			<div className="text-center mb-12">
				<p>Input transaction pin to complete transaction.</p>
			</div>

			<div className="mb-8">
				<OTPInput
					length={4}
					value={pin}
					onChange={handlePinChange}
					onComplete={handlePinComplete}
					autoFocus
				/>
			</div>

			<div className="mt-8">
				<Button
					onClick={handleVerifyPin}
					className="w-full py-7 rounded-full"
					disabled={pin.length !== 4 || loading}
				>
					{loading ? "Processing..." : "Send Crypto"}
				</Button>
			</div>
		</div>
	);
};

export default EnterPinStep;
