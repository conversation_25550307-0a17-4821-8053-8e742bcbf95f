import { X } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Check } from 'lucide-react'
import { Button } from "./ui/button";
import cn from "@/utils/class-names";


interface SuccessDrawerProps {
  title: string;
  description: string;
  buttonText: string;
  route: string;
  closeDrawer?: () => void;
}

export const SuccessDrawer = ({
  title,
  description,
  buttonText,
  route,
  closeDrawer,
}: SuccessDrawerProps) => {
  const navigate = useNavigate();

  return (
    <div className="mt-12 flex flex-col items-center px-6 pt-8 pb-12 max-w-md mx-auto relative">
      <button
        className="cursor-pointer absolute top-6 right-6 text-primary border-2 border-primary rounded-full p-1"
        onClick={closeDrawer}

      >
        <X size={20} />
      </button>
      <div className="mt-20 mb-8">
        <div className="w-40 h-40 rounded-full bg-green-700 flex items-center justify-center mx-auto">
          <Check className="text-white" size={80} />
        </div>
      </div>
      <h2 className="text-xl font-semibold text-center mb-2">{title}</h2>
      <p className="text-gray-500 text-center sm:mb-16">{description}</p>
      <Button
        className={cn(
          "w-[50%] text-white font-medium py-6 px-4 rounded-full transition-colors",
        )}
        onClick={() => navigate(route)}
      >
        {buttonText}
      </Button>
    </div>
  );
};