import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import useUserStore from "@/store/user-store";
import {
	createPinSchema,
	verifyPinSchema,
	TCreatePinSchema,
	TVerifyPinSchema,
} from "@/utils/validators/transaction-pin.schema";
import { useSetPin, useVerifyPin } from "@/hooks/api/user";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";

type PinStep = "create" | "create-success" | "verify";

interface PinManagerProps {
	onComplete: (() => void) | (() => Promise<void>);
	title?: string;
	message?: string;
	className?: string;
	isProcessing?: boolean;
}

export function PinManager({
	onComplete,
	title,
	message,
	className = "",
	isProcessing = false,
}: PinManagerProps) {
	const { user } = useUserStore();

	const [currentStep, setCurrentStep] = useState<PinStep | null>(null);
	const [direction, setDirection] = useState<"forward" | "backward">("forward");

	useEffect(() => {
		if (user === undefined) return;
		if (user?.pin) {
			setCurrentStep("verify");
		} else {
			setCurrentStep("create");
		}
	}, [user]);

	const {
		control: createControl,
		handleSubmit: handleCreateSubmit,
		formState: { errors: createErrors, isValid: isCreateValid },
		reset: resetCreateForm,
	} = useForm<TCreatePinSchema>({
		resolver: zodResolver(createPinSchema),
		mode: "onChange",
		defaultValues: {
			pin: "",
			confirm_pin: "",
		},
	});

	const {
		control: verifyControl,
		handleSubmit: handleVerifySubmit,
		formState: { errors: verifyErrors, isValid: isVerifyValid },
		reset: resetVerifyForm,
	} = useForm<TVerifyPinSchema>({
		resolver: zodResolver(verifyPinSchema),
		mode: "onChange",
		defaultValues: {
			pin: "",
		},
	});

	const { mutate: createPinMutation, isPending: isCreatingPin } = useSetPin();
	const { mutate: verifyPinMutation, isPending: isVerifyingPin } = useVerifyPin();

	useEffect(() => {
		resetCreateForm();
		resetVerifyForm();
	}, [currentStep, resetCreateForm, resetVerifyForm]);

	const onCreateSubmit = (data: TCreatePinSchema) => {
		createPinMutation(data.pin, {
			onSuccess: () => {
				notify("PIN created successfully.", ResponseStatus.SUCCESS);
				setDirection("forward");
				setCurrentStep("create-success");
			},
			onError: (err) => {
				const errorMessage =
					err?.message || "Failed to create PIN. Please try again.";
				notify(errorMessage, ResponseStatus.ERROR);
			},
		});
	};

	const onVerifySubmit = (data: TVerifyPinSchema) => {
		if (!user) return;

		verifyPinMutation(data.pin, {
			onSuccess: () => {
				notify("PIN verified successfully.", ResponseStatus.SUCCESS);
				try {
					if (typeof onComplete === "function") {
						onComplete();
					}
				} catch {
					notify("Transaction failed after PIN verification.", ResponseStatus.ERROR);
				}
			},
			onError: () => {
				notify("Failed to verify PIN. Please try again.", ResponseStatus.ERROR);
			},
		});
	};

	const handleProceedToVerify = () => {
		setDirection("forward");
		setCurrentStep("verify");
	};

	const getStepTitle = () => {
		if (title) return title;
		switch (currentStep) {
			case "create":
				return "Create PIN";
			case "create-success":
				return "PIN Created";
			case "verify":
				return "Enter PIN";
			default:
				return "";
		}
	};

	const getStepMessage = () => {
		if (message) return message;
		switch (currentStep) {
			case "create":
				return "Create a 4-digit PIN to secure your transactions. You'll need this PIN for future purchases.";
			case "create-success":
				return "Your PIN has been successfully created. Please enter it again to verify and proceed.";
			case "verify":
				return "Enter your 4-digit PIN to confirm this transaction.";
			default:
				return "";
		}
	};

	const slideVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	if (currentStep === null) {
		return (
			<div className={`flex items-center justify-center h-full ${className}`}>
				<p className="text-sm text-gray-500">Loading...</p>
			</div>
		);
	}

	return (
		<div className={`flex flex-col h-full ${className}`}>
			<div className="mb-6">
				<h2 className="text-2xl font-semibold text-center mb-2">
					{getStepTitle()}
				</h2>
				<p className="text-center text-gray-600">{getStepMessage()}</p>
			</div>

			<div className="flex-grow overflow-hidden relative">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{/* create step */}
					{currentStep === "create" && (
						<motion.div
							key="create"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{ x: { type: "spring", stiffness: 300, damping: 30 }, opacity: { duration: 0.2 } }}
							className="absolute inset-0 w-full h-full p-6"
						>
							<form onSubmit={handleCreateSubmit(onCreateSubmit)} className="space-y-6">
								<div className="flex flex-col items-center space-y-8">
									{/* PIN Input */}
									<p className="text-sm font-medium mb-3">Enter PIN</p>
									<Controller
										name="pin"
										control={createControl}
										render={({ field }) => (
											<InputOTP
												pattern={REGEXP_ONLY_DIGITS}
												maxLength={4}
												value={field.value}
												onChange={field.onChange}
												autoFocus
												containerClassName="gap-3"
											>
												{[0, 1, 2, 3].map((index) => (
													<InputOTPGroup key={index}>
														<InputOTPSlot index={index} className="size-16 text-lg" mask />
													</InputOTPGroup>
												))}
											</InputOTP>
										)}
									/>
									{createErrors.pin && (
										<p className="text-sm font-medium text-destructive mt-2">{createErrors.pin.message}</p>
									)}

									{/* Confirm PIN Input */}
									<p className="text-sm font-medium mb-3">Confirm PIN</p>
									<Controller
										name="confirm_pin"
										control={createControl}
										render={({ field }) => (
											<InputOTP
												pattern={REGEXP_ONLY_DIGITS}
												maxLength={4}
												value={field.value}
												onChange={field.onChange}
												containerClassName="gap-3"
											>
												{[0, 1, 2, 3].map((index) => (
													<InputOTPGroup key={index}>
														<InputOTPSlot index={index} className="size-16 text-lg" mask />
													</InputOTPGroup>
												))}
											</InputOTP>
										)}
									/>
									{createErrors.confirm_pin && (
										<p className="text-sm font-medium text-destructive mt-2">
											{createErrors.confirm_pin.message}
										</p>
									)}
								</div>

								<Button
									type="submit"
									disabled={!isCreateValid || isCreatingPin}
									className="w-full py-6 rounded-full bg-primary hover:bg-primary/90 text-white font-medium"
								>
									{isCreatingPin ? "Please wait..." : "Create PIN"}
								</Button>
							</form>
						</motion.div>
					)}

					{/* create-success step */}
					{currentStep === "create-success" && (
						<motion.div
							key="create-success"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{ x: { type: "spring", stiffness: 300, damping: 30 }, opacity: { duration: 0.2 } }}
							className="absolute inset-0 w-full p-6 flex flex-col items-center"
						>
							<div className="text-center">
								<div className="flex justify-center mb-6">
									<div className="bg-green-100 rounded-full p-4">
										<CheckCircle className="size-16 text-green-600" />
									</div>
								</div>
								<h3 className="text-xl font-semibold mb-3">PIN Created Successfully</h3>
								<p className="text-gray-600 mb-8">
									Your PIN has been securely created. You'll need to enter it to complete the current transaction.
								</p>
								<Button
									className="w-full py-6 rounded-full bg-primary hover:bg-primary/90 text-white font-medium"
									onClick={handleProceedToVerify}
								>
									Continue
								</Button>
							</div>
						</motion.div>
					)}

					{/* verify step */}
					{currentStep === "verify" && (
						<motion.div
							key="verify"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{ x: { type: "spring", stiffness: 300, damping: 30 }, opacity: { duration: 0.2 } }}
							className="absolute inset-0 w-full h-full p-6"
						>
							<form onSubmit={handleVerifySubmit(onVerifySubmit)} className="space-y-6">
								<div className="flex flex-col items-center">
									<Controller
										name="pin"
										control={verifyControl}
										render={({ field }) => (
											<InputOTP
												pattern={REGEXP_ONLY_DIGITS}
												maxLength={4}
												value={field.value}
												onChange={field.onChange}
												onComplete={() => handleVerifySubmit(onVerifySubmit)()}
												autoFocus
												containerClassName="gap-3"
											>
												{[0, 1, 2, 3].map((index) => (
													<InputOTPGroup key={index}>
														<InputOTPSlot index={index} className="size-16 text-lg" mask />
													</InputOTPGroup>
												))}
											</InputOTP>
										)}
									/>
									{verifyErrors.pin && (
										<p className="text-sm font-medium text-destructive mt-2">{verifyErrors.pin.message}</p>
									)}
								</div>

								<Button
									type="submit"
									disabled={!isVerifyValid || isVerifyingPin || isProcessing}
									className="w-full py-6 rounded-full bg-primary hover:bg-primary/90 text-white font-medium"
								>
									{isVerifyingPin || isProcessing ? "Processing..." : "Verify PIN"}
								</Button>
							</form>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}

////////////////////////////////////////////////////////////////////
// import { useState, useEffect } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import { useForm, Controller } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Button } from "@/components/ui/button";
// import {
// 	InputOTP,
// 	InputOTPGroup,
// 	InputOTPSlot,
// } from "@/components/ui/input-otp";
// import { REGEXP_ONLY_DIGITS } from "input-otp";
// import useUserStore from "@/store/user-store";
// import {
// 	createPinSchema,
// 	verifyPinSchema,
// 	TCreatePinSchema,
// 	TVerifyPinSchema,
// } from "@/utils/validators/transaction-pin.schema";
// import { useSetPin, useVerifyPin } from "@/hooks/api/user";
// import { notify } from "@/utils/notify";
// import { ResponseStatus } from "@/config/enums";

// type PinStep = "create" | "verify";

// interface PinManagerProps {
// 	onComplete: (() => void) | (() => Promise<void>);
// 	title?: string;
// 	message?: string;
// 	className?: string;
// 	isProcessing?: boolean;
// }

// export function PinManager({
// 	onComplete,
// 	title,
// 	message,
// 	className = "",
// 	isProcessing = false,
// }: PinManagerProps) {
	
// const { user } = useUserStore();

// 	const [currentStep, setCurrentStep] = useState<PinStep | null>(null);
// 	const [direction, setDirection] = useState<"forward" | "backward">("forward");
// 	const [isPinLoading, setIsPinLoading] = useState(true);

// useEffect(() => {
// 	if (user === undefined || user === null) return;

// 	// Set current step based on pin
// 	if (user.pin && typeof user.pin === "string" && user.pin.trim() !== "") {
// 		setCurrentStep("verify");
// 	} else {
// 		setCurrentStep("create");
// 	}

// 	setIsPinLoading(false);
// }, [user]);

// 	const {
// 		control: createControl,
// 		handleSubmit: handleCreateSubmit,
// 		formState: { errors: createErrors, isValid: isCreateValid },
// 		reset: resetCreateForm,
// 	} = useForm<TCreatePinSchema>({
// 		resolver: zodResolver(createPinSchema),
// 		mode: "onChange",
// 		defaultValues: {
// 			pin: "",
// 			confirm_pin: "",
// 		},
// 	});

// 	const {
// 		control: verifyControl,
// 		handleSubmit: handleVerifySubmit,
// 		formState: { errors: verifyErrors, isValid: isVerifyValid },
// 		reset: resetVerifyForm,
// 	} = useForm<TVerifyPinSchema>({
// 		resolver: zodResolver(verifyPinSchema),
// 		mode: "onChange",
// 		defaultValues: {
// 			pin: "",
// 		},
// 	});

// 	const { mutate: createPinMutation, isPending: isCreatingPin } = useSetPin();
// 	const { mutate: verifyPinMutation, isPending: isVerifyingPin } = useVerifyPin();

// 	useEffect(() => {
// 		resetCreateForm();
// 		resetVerifyForm();
// 	}, [currentStep, resetCreateForm, resetVerifyForm]);

// 	const onCreateSubmit = (data: TCreatePinSchema) => {
// 		createPinMutation(data.pin, {
// 			onSuccess: () => {
// 				notify("PIN created successfully.", ResponseStatus.SUCCESS);
// 				onComplete?.(); // go directly
// 			},
// 			onError: (err) => {
// 				const errorMessage = err?.message || "Failed to create PIN. Please try again.";
// 				notify(errorMessage, ResponseStatus.ERROR);
// 			},
// 		});
// 	};

// 	const onVerifySubmit = (data: TVerifyPinSchema) => {
// 		if (!user) return;

// 		verifyPinMutation(data.pin, {
// 			onSuccess: () => {
// 				notify("PIN verified successfully.", ResponseStatus.SUCCESS);
// 				onComplete?.();
// 			},
// 			onError: () => {
// 				notify("Failed to verify PIN. Please try again.", ResponseStatus.ERROR);
// 			},
// 		});
// 	};

// 	const getStepTitle = () => {
// 		if (title) return title;
// 		switch (currentStep) {
// 			case "create":
// 				return "Create PIN";
// 			case "verify":
// 				return "Enter PIN";
// 			default:
// 				return "";
// 		}
// 	};

// 	const getStepMessage = () => {
// 		if (message) return message;
// 		switch (currentStep) {
// 			case "create":
// 				return "Create a 4-digit PIN to secure your transactions.";
// 			case "verify":
// 				return "Enter your 4-digit PIN to confirm this transaction.";
// 			default:
// 				return "";
// 		}
// 	};

// 	const slideVariants = {
// 		enter: (direction: string) => ({
// 			x: direction === "forward" ? "100%" : "-100%",
// 			opacity: 0,
// 		}),
// 		center: {
// 			x: 0,
// 			opacity: 1,
// 		},
// 		exit: (direction: string) => ({
// 			x: direction === "forward" ? "-100%" : "100%",
// 			opacity: 0,
// 		}),
// 	};

// if (user === undefined || isPinLoading) {
// 	return (
// 		<div className={`flex items-center justify-center h-full ${className}`}>
// 			<p className="text-sm text-gray-500">Loading...</p>
// 		</div>
// 	);
// }
// 	return (
// 		<div className={`flex flex-col h-full ${className}`}>
// 			<div className="mb-6">
// 				<h2 className="text-2xl font-semibold text-center mb-2">
// 					{getStepTitle()}
// 				</h2>
// 				<p className="text-center text-gray-600">{getStepMessage()}</p>
// 			</div>

// 			<div className="flex-grow overflow-hidden relative">
// 				<AnimatePresence initial={false} mode="wait" custom={direction}>
// 					{/* Create PIN step */}
// 					{currentStep === "create" && (
// 						<motion.div
// 							key="create"
// 							custom={direction}
// 							variants={slideVariants}
// 							initial="enter"
// 							animate="center"
// 							exit="exit"
// 							transition={{ x: { type: "spring", stiffness: 300, damping: 30 }, opacity: { duration: 0.2 } }}
// 							className="absolute inset-0 w-full h-full p-6"
// 						>
// 							<form onSubmit={handleCreateSubmit(onCreateSubmit)} className="space-y-6">
// 								<div className="flex flex-col items-center space-y-8">
// 									<p className="text-sm font-medium mb-3">Enter PIN</p>
// 									<Controller
// 										name="pin"
// 										control={createControl}
// 										render={({ field }) => (
// 											<InputOTP
// 												pattern={REGEXP_ONLY_DIGITS}
// 												maxLength={4}
// 												value={field.value}
// 												onChange={field.onChange}
// 												autoFocus
// 												containerClassName="gap-3"
// 											>
// 												{[0, 1, 2, 3].map((index) => (
// 													<InputOTPGroup key={index}>
// 														<InputOTPSlot index={index} className="size-16 text-lg" mask />
// 													</InputOTPGroup>
// 												))}
// 											</InputOTP>
// 										)}
// 									/>
// 									{createErrors.pin && (
// 										<p className="text-sm font-medium text-destructive mt-2">{createErrors.pin.message}</p>
// 									)}

// 									<p className="text-sm font-medium mb-3">Confirm PIN</p>
// 									<Controller
// 										name="confirm_pin"
// 										control={createControl}
// 										render={({ field }) => (
// 											<InputOTP
// 												pattern={REGEXP_ONLY_DIGITS}
// 												maxLength={4}
// 												value={field.value}
// 												onChange={field.onChange}
// 												containerClassName="gap-3"
// 											>
// 												{[0, 1, 2, 3].map((index) => (
// 													<InputOTPGroup key={index}>
// 														<InputOTPSlot index={index} className="size-16 text-lg" mask />
// 													</InputOTPGroup>
// 												))}
// 											</InputOTP>
// 										)}
// 									/>
// 									{createErrors.confirm_pin && (
// 										<p className="text-sm font-medium text-destructive mt-2">
// 											{createErrors.confirm_pin.message}
// 										</p>
// 									)}
// 								</div>

// 								<Button
// 									type="submit"
// 									disabled={!isCreateValid || isCreatingPin}
// 									className="w-full py-6 rounded-full bg-primary hover:bg-primary/90 text-white font-medium"
// 								>
// 									{isCreatingPin ? "Please wait..." : "Create PIN"}
// 								</Button>
// 							</form>
// 						</motion.div>
// 					)}

// 					{/* Verify PIN step */}
// 					{currentStep === "verify" && (
// 						<motion.div
// 							key="verify"
// 							custom={direction}
// 							variants={slideVariants}
// 							initial="enter"
// 							animate="center"
// 							exit="exit"
// 							transition={{ x: { type: "spring", stiffness: 300, damping: 30 }, opacity: { duration: 0.2 } }}
// 							className="absolute inset-0 w-full h-full p-6"
// 						>
// 							<form onSubmit={handleVerifySubmit(onVerifySubmit)} className="space-y-6">
// 								<div className="flex flex-col items-center">
// 									<Controller
// 										name="pin"
// 										control={verifyControl}
// 										render={({ field }) => (
// 											<InputOTP
// 												pattern={REGEXP_ONLY_DIGITS}
// 												maxLength={4}
// 												value={field.value}
// 												onChange={field.onChange}
// 												onComplete={() => handleVerifySubmit(onVerifySubmit)()}
// 												autoFocus
// 												containerClassName="gap-3"
// 											>
// 												{[0, 1, 2, 3].map((index) => (
// 													<InputOTPGroup key={index}>
// 														<InputOTPSlot index={index} className="size-16 text-lg" mask />
// 													</InputOTPGroup>
// 												))}
// 											</InputOTP>
// 										)}
// 									/>
// 									{verifyErrors.pin && (
// 										<p className="text-sm font-medium text-destructive mt-2">{verifyErrors.pin.message}</p>
// 									)}
// 								</div>

// 								<Button
// 									type="submit"
// 									disabled={!isVerifyValid || isVerifyingPin || isProcessing}
// 									className="w-full py-6 rounded-full bg-primary hover:bg-primary/90 text-white font-medium"
// 								>
// 									{isVerifyingPin || isProcessing ? "Processing..." : "Verify PIN"}
// 								</Button>
// 							</form>
// 						</motion.div>
// 					)}
// 				</AnimatePresence>
// 			</div>
// 		</div>
// 	);
// }
