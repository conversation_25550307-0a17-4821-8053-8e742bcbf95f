import { type FC } from "react";

interface TransactionReceiptProps {
  data: {
    amount: string;
    coin: string;
    rate: string;
    fee: string;
    payment: string;
    date: string;
  };
}

export const TransactionReceipt: FC<TransactionReceiptProps> = ({ data }) => {
  return (
    <div className="space-y-6">
      <div>
        <p className="text-gray-600 text-center">
          This may take up to 2 mins to confirm transaction
        </p>
        <div className="flex justify-center mt-2">
          <span className="bg-green-100 text-green-600 px-4 py-1 rounded-full text-sm">
            Successful
          </span>
        </div>
      </div>

      <div className="text-center">
        <h2 className="text-3xl font-semibold">{data.amount}</h2>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between">
          <span className="text-gray-600">Coin</span>
          <span className="font-medium">{data.coin}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Rate</span>
          <span className="font-medium">{data.rate}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Amount</span>
          <span className="font-medium">
            {data.amount} {data.coin}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Fee</span>
          <span className="font-medium">
            {data.fee} {data.coin}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Payment</span>
          <span className="font-medium">{data.payment}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Date</span>
          <span className="font-medium">{data.date}</span>
        </div>
      </div>

      <button className="w-full bg-primary text-white rounded-lg py-4 font-medium hover:bg-primary/90">
        Share Receipt
      </button>
    </div>
  );
};
