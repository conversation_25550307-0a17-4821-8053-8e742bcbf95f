import { type FC } from "react";
import { motion } from "framer-motion";

	const firstname = localStorage.getItem("firstName")
	const lastname= localStorage.getItem("lastName")

interface TransactionSuccessProps {
  amount: string;
  onViewReceipt: () => void;
}

export const TransactionSuccess: FC<TransactionSuccessProps> = ({
  amount,
  onViewReceipt,
}) => {
  return (
    <div className="flex flex-col items-center justify-center h-full space-y-6 py-12">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", duration: 0.5 }}
        className="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center"
      >
        <svg
          className="w-12 h-12 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={3}
            d="M5 13l4 4L19 7"
          />
        </svg>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="text-center"
      >
        <h2 className="text-2xl font-semibold mb-2">Transaction Successful!</h2>
        <p className="text-gray-600">
          Hi {firstname} {lastname}, you have successfully purchased {amount} BTC
        </p>
      </motion.div>

      <motion.button
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        onClick={onViewReceipt}
        className="bg-primary text-white rounded-lg px-8 py-4 font-medium hover:bg-primary/90"
      >
        View Receipt
      </motion.button>
    </div>
  );
};
