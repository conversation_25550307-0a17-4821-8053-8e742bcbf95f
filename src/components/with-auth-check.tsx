import { IdleTimeoutWrapper } from "@/config/providers/idle-timer-provider";
import { ComponentType, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const withAuthCheck = <P extends object>(Component: ComponentType<P>) => {
	const AuthComponent = (props: P) => {
		const navigate = useNavigate();
		const [isAuthenticated, setIsAuthenticated] = useState(false);
		const [isLoading, setIsLoading] = useState(true);

		useEffect(() => {
			const token = localStorage.getItem("auth_token");

			if (!token) {
				navigate("/login", { replace: true });
			} else {
				setIsAuthenticated(true);
			}

			setIsLoading(false);
		}, [navigate]);

		if (isLoading) {
			return null;
		}

		return isAuthenticated ? (
			<IdleTimeoutWrapper>
				<Component {...props} />
			</IdleTimeoutWrapper>
		) : null;
	};

	const displayName = Component.displayName || Component.name || "Component";
	AuthComponent.displayName = `withAuthCheck(${displayName})`;

	return AuthComponent;
};

export default withAuthCheck;
