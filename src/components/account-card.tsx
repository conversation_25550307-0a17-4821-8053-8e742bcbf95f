import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';

const AccountCard = () => {
  const [activeTab, setActiveTab] = useState('fiat');

  return (
    <div className="flex flex-col items-center">
      <motion.div 
        className="w-full max-w-md bg-white dark:bg-gray-800 rounded-3xl shadow-md p-6 mb-4"
        layout
      >
        <AnimatePresence mode="wait">
          {activeTab === 'fiat' ? (
            <motion.div
              key="fiat"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="w-full"
            >
              {/* Fiat Account Content */}
              <div className="mb-6">
                <div className="w-full p-4 border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-full mb-8 flex justify-between items-center cursor-pointer">
                  <span className="text-2xl text-gray-800 dark:text-gray-200">Nigerian Account</span>
                  <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                    <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
                  <div className="mb-2 text-2xl text-muted-foreground dark:text-gray-400">Total Fiat Balance</div>
                <div className="flex justify-between items-center">
                  <div className="text-5xl md:text-6xl font-bold text-amber-500 dark:text-amber-400">NGN12,000.00</div>
                  <div className="p-2 rounded-full border border-border dark:border-gray-600 text-gray-700 dark:text-gray-300">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none">
                      <path d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12Z" stroke="currentColor" strokeWidth="2" />
                      <path d="M12 6V18" stroke="currentColor" strokeWidth="2" />
                      <path d="M7 12L17 12" stroke="currentColor" strokeWidth="2" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-muted dark:bg-gray-700 rounded-2xl p-4 flex flex-col items-center text-gray-700 dark:text-gray-200">
                  <div className="w-12 h-12 rounded-full border-2 border-foreground dark:border-gray-400 flex items-center justify-center mb-2">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M7 17L17 7M17 7H7M17 7V17" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-lg">Withdraw</span>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-700 rounded-2xl p-4 flex flex-col items-center text-gray-700 dark:text-gray-200">
                  <div className="w-12 h-12 rounded-full border-2 border-gray-700 dark:border-gray-400 flex items-center justify-center mb-2">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M17 7L7 17M7 17H17M7 17V7" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-lg">Deposit</span>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-700 rounded-2xl p-4 flex flex-col items-center text-gray-700 dark:text-gray-200">
                  <div className="w-12 h-12 rounded-full border-2 border-gray-700 dark:border-gray-400 flex items-center justify-center mb-2">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M7 16V8M7 8L3 12M7 8L11 12" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M17 8V16M17 16L13 12M17 16L21 12" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-lg">Convert</span>
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="crypto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="w-full"
            >
              {/* Crypto Account Content */}              
              <div className="mb-6">
                <div className="w-full py-3 px-6 border border-border dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-full mb-8 flex items-center cursor-pointer">
                  <svg className="w-6 h-6 mr-2 text-gray-600 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                    <path d="M9 8L5 12L9 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <span className="text-2xl text-foreground dark:text-gray-200">BTC</span>
                   <svg className="w-6 h-6 ml-auto text-gray-600 dark:text-gray-400" viewBox="0 0 24 24" fill="none">
                    <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
                
                <div className="mb-2 text-2xl text-muted-foreground dark:text-gray-400">Total Crypto Balance</div>
                <div className="flex justify-between items-center">
                  <div className="text-6xl font-bold text-amber-500 dark:text-amber-400">0.00000<span className="text-5xl">7123</span></div>
                  <div className="p-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none">
                      <path d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12Z" stroke="currentColor" strokeWidth="2" />
                      <path d="M12 6V18" stroke="currentColor" strokeWidth="2" />
                      <path d="M7 12L17 12" stroke="currentColor" strokeWidth="2" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-5 gap-2">
                <div className="bg-gray-100 dark:bg-gray-700 rounded-full p-3 flex flex-col items-center text-gray-700 dark:text-gray-300">
                  <div className="w-12 h-12 rounded-full border-2 border-gray-700 dark:border-gray-500 flex items-center justify-center mb-1">
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M7 17L17 7M17 7H7M17 7V17" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-sm">Send</span>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-700 rounded-full p-3 flex flex-col items-center text-gray-700 dark:text-gray-300">
                  <div className="w-12 h-12 rounded-full border-2 border-gray-700 dark:border-gray-500 flex items-center justify-center mb-1">
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M17 7L7 17M7 17H17M7 17V7" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-sm">Receive</span>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-700 rounded-full p-3 flex flex-col items-center text-gray-700 dark:text-gray-300">
                  <div className="w-12 h-12 rounded-full border-2 border-gray-700 dark:border-gray-500 flex items-center justify-center mb-1">
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M12 7V17M12 7H7M12 7C16 7 17 9 17 12" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-sm">Buy</span>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-700 rounded-full p-3 flex flex-col items-center text-gray-700 dark:text-gray-300">
                  <div className="w-12 h-12 rounded-full border-2 border-gray-700 dark:border-gray-500 flex items-center justify-center mb-1">
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M12 17V7M12 17H7M12 17C16 17 17 15 17 12" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-sm">Sell</span>
                </div>
                
                <div className="bg-gray-100 dark:bg-gray-700 rounded-full p-3 flex flex-col items-center text-gray-700 dark:text-gray-300">
                  <div className="w-12 h-12 rounded-full border-2 border-gray-700 dark:border-gray-500 flex items-center justify-center mb-1">
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M7 16V8M7 8L3 12M7 8L11 12" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M17 8V16M17 16L13 12M17 16L21 12" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <span className="text-sm">Swap</span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
      
      {/* Tab Switcher */}
      <div className="flex justify-center items-center w-24 bg-gray-200 dark:bg-gray-700 rounded-full p-1">
        <motion.button
          className={`relative rounded-full flex justify-center items-center ${activeTab === 'fiat' ? 'z-10' : 'z-0'}`}
          onClick={() => setActiveTab('fiat')}
        >
          <motion.div
            className="absolute bg-amber-500 rounded-full w-full h-full"
            initial={false}
            animate={{
              width: activeTab === 'fiat' ? 48 : 10,
              height: activeTab === 'fiat' ? 20 : 10,
            }}
            transition={{ duration: 0.3 }}
            style={{ 
              opacity: activeTab === 'fiat' ? 1 : 0,
              zIndex: -1
            }}
          />
          <div className="w-2 h-2 bg-white dark:bg-gray-600 rounded-full" />
        </motion.button>
        
        <motion.button
          className={`relative rounded-full flex justify-center items-center ml-4 ${activeTab === 'crypto' ? 'z-10' : 'z-0'}`}
          onClick={() => setActiveTab('crypto')}
        >
          <motion.div
            className="absolute bg-amber-500 rounded-full w-full h-full"
            initial={false}
            animate={{
              width: activeTab === 'crypto' ? 48 : 10,
              height: activeTab === 'crypto' ? 20 : 10,
            }}
            transition={{ duration: 0.3 }}
            style={{ 
              opacity: activeTab === 'crypto' ? 1 : 0,
              zIndex: -1
            }}
          />
          <div className="w-2 h-2 bg-white dark:bg-gray-600 rounded-full" />
        </motion.button>
      </div>
    </div>
  );
};

export default AccountCard;