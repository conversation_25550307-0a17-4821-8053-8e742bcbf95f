import { ReactNode, useEffect } from "react";
import useThemeStore from "@/store/theme-store";

interface ThemeProviderProps {
	children: ReactNode;
	defaultTheme?: "light" | "dark" | "system";
}

export function ThemeProvider({ children, defaultTheme = "system" }: ThemeProviderProps) {
	const { theme, setTheme, resolvedTheme } = useThemeStore();

	useEffect(() => {
		// Initialize theme on mount
		if (!theme) {
			setTheme(defaultTheme);
		}

		// Listen for system theme changes
		const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
		const handleChange = () => {
			if (theme === "system") {
				const systemTheme = mediaQuery.matches ? "dark" : "light";
				const root = document.documentElement;
				root.classList.remove("light", "dark");
				root.classList.add(systemTheme);
				
				// Update the resolved theme in store
				useThemeStore.setState({ resolvedTheme: systemTheme });
			}
		};

		mediaQuery.addEventListener("change", handleChange);
		
		// Apply initial theme
		const root = document.documentElement;
		root.classList.remove("light", "dark");
		root.classList.add(resolvedTheme);

		return () => mediaQuery.removeEventListener("change", handleChange);
	}, [theme, setTheme, defaultTheme, resolvedTheme]);

	return <>{children}</>;
}
