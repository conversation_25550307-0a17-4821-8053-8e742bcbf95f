import { Button } from "@/components/ui/button";
import { X, Check } from "lucide-react";
import { useDrawer } from "../drawer-view/use-drawer";
import { EmailVerificationDrawer } from "@/pages/dashbaord/profile/views/edit-profile/email-verification-code-drawer";
import { useState } from "react";
import { useRequestEmailChange } from "@/hooks/api/user";

const EditProfileField = ({
  label,
  type = "text",
  value,
  onChange,
  onCancel,
  onSave,
}) => {
  const { openDrawer, closeDrawer } = useDrawer();
  const { mutate: requestEmailChange, isPending } = useRequestEmailChange();
  
  const handleSave = () => {
    if (type === "email") {
      requestEmailChange(value, {
        onSuccess: () => {
          // Open the verification drawer
          openDrawer({
            view: (
              <EmailVerificationDrawer
                description={"Enter the verification code sent to your email."}
                title={"Verify Email Change"}
                buttonText={"Save Changes"}
                closeDrawer={closeDrawer}
                newEmail={value}
              />
            ),
            placement: "right",
            customSize: "400px",
          });
        }
      });
    } else {
      onSave();
    }
  };

  return (
    <div>
      <div className="w-full flex flex-col items-center">
        <label className="text-gray-500 mb-1 w-full text-left">{label}</label>
        <input
          type={type}
          value={value}
          onChange={onChange}
          className="w-full rounded-full border border-black dark:border-gray-600 px-6 py-4 text-sm"
        />
      </div>
      <div className="flex w-full justify-between mt-8">
        <Button
          variant="outline"
          size="lg"
          className="rounded-full font-light text-gray-500"
          onClick={onCancel}
        >
          Cancel <X size={18} />
        </Button>
        <Button
          variant="outline"
          size="lg"
          className="rounded-full font-light text-gray-500"
          onClick={handleSave}
          disabled={isPending}
        >
          {isPending ? "Processing..." : "Save Changes"} <Check size={18} />
        </Button>
      </div>
    </div>
  )
};

export default EditProfileField;
