"use client";

import * as React from "react";
import { X } from "lucide-react";

import cn from "@/utils/class-names";

interface SendCryptoDrawerProps {
	isOpen: boolean;
	onClose: () => void;
}

export function SendCryptoDrawer({ isOpen, onClose }: SendCryptoDrawerProps) {
	React.useEffect(() => {
		if (isOpen) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "unset";
		}
		return () => {
			document.body.style.overflow = "unset";
		};
	}, [isOpen]);

	return (
		<>
			{/* Overlay */}
			<div
				className={cn(
					"fixed inset-0 bg-black/50 z-40 transition-opacity duration-300",
					isOpen ? "opacity-100" : "opacity-0 pointer-events-none",
				)}
				onClick={onClose}
				aria-hidden="true"
			/>

			{/* Drawer */}
			<div
				className={cn(
					"fixed right-0 top-0 h-full w-full sm:w-[480px] bg-white z-50 transition-transform duration-300 ease-in-out",
					isOpen ? "translate-x-0" : "translate-x-full",
				)}
				role="dialog"
				aria-modal="true"
				aria-label="Send Crypto"
			>
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b">
					<h2 className="text-2xl font-semibold">Send Crypto</h2>
					<button
						onClick={onClose}
						className="text-gray-500 hover:text-gray-700"
						aria-label="Close dialog"
					>
						<X className="h-6 w-6" />
					</button>
				</div>

				{/* Content */}
				<div className="p-6">
					<form className="space-y-6">
						{/* <div className="space-y-4">
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Choose Coin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="btc">Bitcoin (BTC)</SelectItem>
                  <SelectItem value="eth">Ethereum (ETH)</SelectItem>
                  <SelectItem value="usdt">Tether (USDT)</SelectItem>
                </SelectContent>
              </Select>

              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Choose Network" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bitcoin">Bitcoin</SelectItem>
                  <SelectItem value="ethereum">Ethereum</SelectItem>
                  <SelectItem value="polygon">Polygon</SelectItem>
                </SelectContent>
              </Select>

              <div className="space-y-2">
                <Input placeholder="Paste wallet address" className="w-full" />
                <p className="text-sm text-muted-foreground">
                  Must be a valid wallet address
                </p>
              </div>

              <div className="space-y-2">
                <Input
                  type="number"
                  placeholder="Enter Amount"
                  className="w-full"
                />
                <p className="text-sm text-muted-foreground">
                  Fee is displayed here
                </p>
              </div>

              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-center">
                  Amount to be sent after fee is deducted
                </p>
              </div>
            </div> */}

						<div className="space-y-4">
							<div className="relative flex items-center justify-center">
								<span className="px-4 text-sm text-gray-500 bg-white">
									Or
								</span>
								<div className="absolute inset-x-0 top-1/2 h-px bg-gray-200" />
							</div>

							<div className="flex flex-col sm:flex-row gap-4">
								<button className="flex-1">Scan QR code</button>
								<button className="flex-1 bg-[#CD853F] hover:bg-[#B8732F]">
									Send Crypto
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</>
	);
}
