
import { Icon } from "@/components/icons";
import cn from "@/utils/class-names";
import React, { ReactNode } from "react";

interface AuthCardProps {
	children: ReactNode;
	title: string;
	titleClassName?: string;
	subtitle?: string;
	subtitleClassName?: string;
	showLogo?: boolean;
	showBackButton?: boolean;
	onBackClick?: () => void;
}

export const AuthCard: React.FC<AuthCardProps> = ({
	children,
	title,
	titleClassName,
	subtitle,
	subtitleClassName,
	showLogo = true,
	showBackButton = false,
	onBackClick,
}) => {
	return (
		<div className="w-full max-w-lg mx-auto">
			<div className="bg-white rounded-xl border border-gray-200 p-8">
				{/* Logo */}
				{showLogo && (
					<div className="flex justify-center mb-6">
						<Icon name="clyppay-icon-logo" className="h-14 w-14" />
					</div>
				)}

				{/* Back Button */}
				{showBackButton && (
					<button
						onClick={onBackClick}
						className="mb-6 p-2 rounded-full bg-amber-100 hover:bg-amber-200 transition-colors"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="24"
							height="24"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							strokeLinecap="round"
							strokeLinejoin="round"
						>
							<path d="M15 18l-6-6 6-6" />
						</svg>
					</button>
				)}

				{/* Title and Subtitle */}
				<h1 className={cn("text-3xl font-bold text-center mb-2 text-gray-900 font-body", titleClassName)}>
					{title}
				</h1>

				{subtitle && (
					<p className={cn("text-center text-gray-600 mb-8 font-body", subtitleClassName)}>
						{subtitle}
					</p>
				)}

				{/* Content */}
				{children}
			</div>
		</div>
	);
};
