import React from "react";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import { UseFormRegisterReturn } from "react-hook-form";
import cn from "@/utils/class-names";

interface AuthInputFieldProps {
	label: string;
	type: string;
	id?: string;
	register: UseFormRegisterReturn;
	error?: string;
	rightElement?: React.ReactNode;
	className?: string;
	labelClassName?: string;
}

export const AuthInputField: React.FC<AuthInputFieldProps> = ({
	label,
	type,
	id,
	register,
	error,
	rightElement,
	className,
	labelClassName,
}) => {
	return (
		<div>
			<div className="relative">
				<FloatingLabelInput
					label={label}
					type={type}
					id={id}
					{...register}
					required={register.required}
					className={cn(
						"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
						error &&
							"border-red-500 focus:ring-red-200 focus:border-red-500",
						className,
					)}
					labelClassName={cn(error && "text-red-500", labelClassName)}
				/>
				{rightElement && (
					<span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
						{rightElement}
					</span>
				)}
			</div>
			{error && <p className="mt-1 text-sm text-red-500 pl-4">{error}</p>}
		</div>
	);
};
