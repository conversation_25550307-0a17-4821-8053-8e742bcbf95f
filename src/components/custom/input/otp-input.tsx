"use client";

import React, { useState, useRef, useEffect } from "react";
import cn from "@/utils/class-names";

interface OTPInputProps {
	length?: number;
	value?: string;
	onChange?: (value: string) => void;
	onComplete?: (value: string) => void;
	autoFocus?: boolean;
	className?: string;
	inputClassName?: string;
}

const OTPInput: React.FC<OTPInputProps> = ({
	length = 4,
	value = "",
	onChange,
	onComplete,
	autoFocus = true,
	className,
	inputClassName,
}) => {
	const [otpValues, setOtpValues] = useState<string[]>(() => {
		const initialValues = value.split("").slice(0, length);
		return [
			...initialValues,
			...Array(length - initialValues.length).fill(""),
		];
	});

	const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

	useEffect(() => {
		inputRefs.current = inputRefs.current.slice(0, length);

		if (autoFocus && inputRefs.current[0]) {
			setTimeout(() => {
				inputRefs.current[0]?.focus();
			}, 100);
		}
	}, [length, autoFocus]);

	useEffect(() => {
		if (value) {
			const newValues = value.split("").slice(0, length);
			setOtpValues([
				...newValues,
				...Array(length - newValues.length).fill(""),
			]);
		} else {
			setOtpValues(Array(length).fill(""));
		}
	}, [value, length]);

	const handleChange = (
		e: React.ChangeEvent<HTMLInputElement>,
		index: number,
	) => {
		const newValue = e.target.value;

		if (newValue.length > 1) {
			const digits = newValue
				.split("")
				.filter((char) => /^\d$/.test(char))
				.slice(0, length - index);
			if (digits.length > 0) {
				const newOtpValues = [...otpValues];

				digits.forEach((digit, i) => {
					if (index + i < length) {
						newOtpValues[index + i] = digit;
					}
				});

				setOtpValues(newOtpValues);
				onChange?.(newOtpValues.join(""));

				const nextEmptyIndex = newOtpValues.findIndex(
					(val) => val === "",
				);
				if (nextEmptyIndex !== -1) {
					inputRefs.current[nextEmptyIndex]?.focus();
				} else {
					inputRefs.current[length - 1]?.focus();
				}

				if (!newOtpValues.includes("")) {
					onComplete?.(newOtpValues.join(""));
				}

				return;
			}
		}

		if (newValue && !/^\d$/.test(newValue)) {
			return;
		}

		const newOtpValues = [...otpValues];
		newOtpValues[index] = newValue;
		setOtpValues(newOtpValues);

		const newStringValue = newOtpValues.join("");
		onChange?.(newStringValue);

		if (newValue && index < length - 1) {
			setTimeout(() => {
				inputRefs.current[index + 1]?.focus();
			}, 10);
		}

		if (newValue && !newOtpValues.includes("")) {
			onComplete?.(newOtpValues.join(""));
		}
	};

	const handleKeyDown = (
		e: React.KeyboardEvent<HTMLInputElement>,
		index: number,
	) => {
		if (e.key === "Backspace") {
			if (!otpValues[index] && index > 0) {
				inputRefs.current[index - 1]?.focus();

				const newOtpValues = [...otpValues];
				newOtpValues[index - 1] = "";
				setOtpValues(newOtpValues);
				onChange?.(newOtpValues.join(""));
			}
		}

		if (e.key === "ArrowRight" && index < length - 1) {
			if (otpValues[index]) {
				inputRefs.current[index + 1]?.focus();
			}
		}

		if (e.key === "ArrowLeft" && index > 0) {
			inputRefs.current[index - 1]?.focus();
		}
	};

	const handlePaste = (e: React.ClipboardEvent) => {
		e.preventDefault();
		const pastedData = e.clipboardData.getData("text/plain").trim();

		if (/^\d+$/.test(pastedData)) {
			const digits = pastedData.split("").slice(0, length);
			const newOtpValues = [
				...digits,
				...Array(length - digits.length).fill(""),
			];
			setOtpValues(newOtpValues);

			const newStringValue = newOtpValues.join("");
			onChange?.(newStringValue);

			const lastIndex = Math.min(digits.length, length) - 1;
			if (lastIndex >= 0) {
				inputRefs.current[lastIndex]?.focus();
			}

			if (digits.length >= length) {
				onComplete?.(newOtpValues.join(""));
			}
		}
	};

	const handleFocus = (index: number) => {
		for (let i = 0; i < index; i++) {
			if (!otpValues[i]) {
				inputRefs.current[i]?.focus();
				return;
			}
		}
	};

	return (
		<div className={cn("flex justify-center gap-2", className)}>
			{Array.from({ length }, (_, index) => (
				<input
					key={index}
					ref={(el) => {
						inputRefs.current[index] = el;
					}}
					type="text"
					inputMode="numeric"
					maxLength={1}
					value={otpValues[index]}
					onChange={(e) => handleChange(e, index)}
					onKeyDown={(e) => handleKeyDown(e, index)}
					onPaste={index === 0 ? handlePaste : undefined}
					onFocus={() => handleFocus(index)}
					className={cn(
						"w-16 h-16 text-center text-2xl font-bold border rounded-lg",
						"focus:outline-none focus:ring-2 focus:border-amber-500 focus:ring-amber-500",
						"transition-all duration-200",
						otpValues[index]
							? "border-amber-500 bg-amber-50"
							: index === 0 ||
							  otpValues.slice(0, index).every((v) => v !== "")
							? "border-gray-300 bg-white"
							: "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed",
						inputClassName,
					)}
					disabled={
						index > 0 &&
						!otpValues.slice(0, index).every((v) => v !== "")
					}
					aria-label={`Digit ${index + 1}`}
				/>
			))}
		</div>
	);
};

export default OTPInput;
