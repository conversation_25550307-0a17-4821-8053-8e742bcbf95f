import { cn } from "@/lib/utils";

interface CheckboxProps {
    checked: boolean;
    onChange: () => void;
    label: string;
    className?: string;
}

const Checkbox = ({
    checked,
    onChange,
    label,
    className,
}: CheckboxProps) => {
    return (
        <div className="flex items-center">
            <div
                className={cn(
                    "w-6 h-6 flex items-center justify-center border-2 border-gray-400 rounded-md cursor-pointer",
                    checked ? "bg-primary" : "bg-white",
                    className,
                )}
                onClick={onChange}
            >
                {checked && <span className="text-white text-xs">✓</span>}
            </div>
            <span className="ml-2 text-gray-700">{label}</span>
        </div>
    );
};

export default Checkbox;