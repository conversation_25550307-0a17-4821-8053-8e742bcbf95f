import React, { useState, useEffect, useRef } from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

export interface SelectOption {
	id?: string;
	label: string | React.ReactNode;
	value: string | number;
	icon?: string;
	balance?: string;
	code?: string;
	bgColor?: string;
}

interface CustomComponents {
	Option?: React.ComponentType<{ option: SelectOption }>;
	MenuHeader?: React.ComponentType;
}

interface CustomSelectProps {
	options: SelectOption[];
	placeholder?: string;
	value?: SelectOption | null;
	onChange: (option: SelectOption) => void;
	className?: string;
	triggerClassName?: string;
	dropdownClassName?: string;
	optionClassName?: string;
	components?: CustomComponents;
	formatOptionLabel?: (option: SelectOption) => React.ReactNode;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
	options,
	placeholder = "Select an option",
	value,
	onChange,
	className,
	triggerClassName,
	dropdownClassName,
	optionClassName,
	components,
	formatOptionLabel
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const handleSelect = (option: SelectOption) => {
		onChange(option);
		setIsOpen(false);
	};

	const renderOption = (option: SelectOption) => {
		if (components?.Option) {
			return <components.Option option={option} />;
		}
		
		if (formatOptionLabel) {
			return formatOptionLabel(option);
		}

		return option.label;
	};

	return (
		<div className={cn("relative w-full", className)} ref={dropdownRef}>
			{/* Select Trigger */}
			<div
				className={cn(
					"w-full px-4 py-2.5 flex items-center justify-between rounded-full border border-gray-200 bg-white cursor-pointer",
					value ? "text-gray-900" : "text-gray-400",
					triggerClassName,
				)}
				onClick={() => setIsOpen(!isOpen)}
			>
				<span className="flex items-center gap-2">
					{value ? renderOption(value) : placeholder}
				</span>
				<ChevronDown
					className="h-4 w-4 text-gray-400 flex-shrink-0"
					style={{
						transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
						transition: "transform 0.2s ease-in-out",
					}}
				/>
			</div>

			{/* Dropdown */}			{isOpen && (
				<div
					className={cn(
						"absolute z-50 mt-1 w-full bg-background border border-border rounded-2xl shadow-lg overflow-hidden",
						dropdownClassName,
					)}
				>
					{components?.MenuHeader && <components.MenuHeader />}
					<div className="max-h-80 overflow-y-auto divide-y divide-border">
						{options.map((option) => (
							<div
								key={option.id}
								className={cn(
									"px-4 py-3 cursor-pointer",
									option.bgColor || "bg-background",
									value?.id === option.id
										? "bg-muted"
										: "hover:bg-muted",
									optionClassName,
								)}
								onClick={() => handleSelect(option)}
							>
								{renderOption(option)}
							</div>
						))}
						{options.length === 0 && (
							<div className="px-4 py-3 text-gray-400 text-center">
								No options available
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default CustomSelect;
