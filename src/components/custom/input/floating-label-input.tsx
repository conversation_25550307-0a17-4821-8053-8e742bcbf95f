import {
	useState,
	InputHTMLAttributes,
	ChangeEvent,
	forwardRef,
	useEffect,
} from "react";
import cn from "@/utils/class-names";

interface FloatingLabelInputProps
	extends Omit<InputHTMLAttributes<HTMLInputElement>, "onChange"> {
	label: string;
	id?: string;
	onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
	className?: string;
	labelClassName?: string;
	error?: string;
}

const FloatingLabelInput = forwardRef<
	HTMLInputElement,
	FloatingLabelInputProps
>(
	(
		{
			label,
			type = "text",
			id,
			name,
			required = false,
			className = "",
			labelClassName = "",
			value: externalValue,
			onChange: externalOnChange,
			...props
		},
		ref,
	) => {
		const [internalValue, setInternalValue] = useState("");
		const [isFocused, setIsFocused] = useState(false);

		
		const isControlled = externalValue !== undefined;
		const value = isControlled ? externalValue : internalValue;

		
		useEffect(() => {
			if (isControlled) {
				
				setIsFocused(
					!!externalValue && String(externalValue).length > 0,
				);
			}
		}, [externalValue, isControlled]);

		
		const shouldFloat =
			isFocused || (value !== undefined && String(value).length > 0);

		const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
			if (!isControlled) {
				setInternalValue(e.target.value);
			}

			if (externalOnChange) {
				externalOnChange(e);
			}
		};
		
		//   						"w-full px-6 py-4 text-gray-800 border border-gray-300 rounded-full outline-none transition-all duration-200",
		//   						shouldFloat ? "pt-6 pb-2" : "py-4",
		//   						"focus:ring-2 focus:ring-gray-200 focus:border-gray-400",
		//   						className,
		//   					)}

		return (
			<div className="relative w-full">
				<input
					ref={ref}
					id={id}
					name={name}
					type={type}
					value={value}
					onChange={handleChange}
					required={required}
					className={cn(
						"w-full px-6 py-4 text-gray-800 border border-gray-300 rounded-full outline-none transition-all duration-200",
						shouldFloat ? "pt-6 pb-2" : "py-4",
						className,
					)}
					onFocus={() => setIsFocused(true)}
					onBlur={() => setIsFocused(false)}
					placeholder=" " 
					{...props}
				/>

				<label
					htmlFor={id}
					className={cn(
						"absolute duration-200 transform pointer-events-none text-gray-500",
						shouldFloat
							? "text-xs top-2 left-6 scale-90 origin-left"
							: "text-base top-1/2 left-6 -translate-y-1/2",
						labelClassName,
					)}
				>
					{label}
				</label>
			</div>
		);
	},
);

FloatingLabelInput.displayName = "FloatingLabelInput";

export default FloatingLabelInput;
