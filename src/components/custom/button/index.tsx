export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
	children: React.ReactNode;
	variant?: "primary" | "secondary";
	type?: "button" | "submit";
	onClick?: () => void;
	className?: string;
}

export const Button = ({
	children,
	variant = "primary",
	type = "button",
	onClick,
	className = "",
	...props
}: ButtonProps) => {

	return (
		<button
			type={type}
			onClick={onClick}
			className={`
        px-6 py-3 rounded-full font-medium transition-all duration-200
        ${
			variant === "primary"
				? "bg-primary text-white hover:bg-[#d19642]"
				: "bg-transparent text-primary border border-primary hover:bg-[#E4A853] hover:text-white"
		}
        ${className}
		
      `
	}
		>
			{children}
		</button>
	);
};
