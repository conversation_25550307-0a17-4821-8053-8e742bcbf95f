"use client";

import type { ReactNode } from "react";
import { cn } from "@/lib/utils";


type StatusType =
	| "successful"
	| "processing"
	| "failed"
	| "pending"
	| undefined;

const StatusBadge = ({ status }: { status?: StatusType }) => {
	if (!status) return null;

	return (
		<div
			className={cn(
				"px-4 py-1 rounded-full text-sm font-medium inline-block mb-6",
				status === "successful" && "bg-green-100 text-green-800",
				status === "processing" && "bg-blue-100 text-blue-800",
				status === "failed" && "bg-red-100 text-red-800",
				status === "pending" && "bg-yellow-100 text-yellow-800",
			)}
		>
			{status.charAt(0).toUpperCase() + status.slice(1)}
		</div>
	);
};


type DetailRowProps = {
	label: string;
	value: string | ReactNode;
};

const DetailRow = ({ label, value }: DetailRowProps) => {
	return (
		<div className="flex justify-between items-center py-3">
			<div className="text-gray-600">{label}</div>
			<div className="text-right font-medium">{value}</div>
		</div>
	);
};


type AmountDisplayProps = {
	amount: string;
	currency: string;
	prefix?: string;
	highlighted?: boolean;
};

const AmountDisplay = ({
	amount,
	currency,
	prefix,
	highlighted = false,
}: AmountDisplayProps) => {
	return (
		<div
			className={cn(
				"text-4xl font-bold mb-8",
				highlighted && "text-blue-600 underline",
			)}
		>
			{prefix && <span>{prefix}</span>}
			{amount} {currency}
		</div>
	);
};


export type TransactionDetail = {
	label: string;
	value: string;
};

export type TransactionCardProps = {
	status?: StatusType;
	amount?: string;
	currency?: string;
	prefix?: string;
	highlightAmount?: boolean;
	details: TransactionDetail[];
	className?: string;
	statusInfo?: string;
};


export const TransactionSummaryCard = ({
	status,
	amount,
	currency = "",
	prefix,
	highlightAmount = false,
	details,
	className,
	statusInfo,
}: TransactionCardProps) => {
	return (
		<div className={cn("p-6", className)}>
			{statusInfo && <p className="text-gray-600">{statusInfo}</p>}
			{status && (
				<div className="flex justify-center mt-2">
					<StatusBadge status={status} />
				</div>
			)}

			{amount && (
				<div className="text-center">
					<AmountDisplay
						amount={amount}
						currency={currency}
						prefix={prefix}
						highlighted={highlightAmount}
					/>
				</div>
			)}

			<div className="space-y-1">
				{details.map((detail, index) => (
					<DetailRow
						key={index}
						label={detail.label}
						value={detail.value}
					/>
				))}
			</div>
		</div>
	);
};
