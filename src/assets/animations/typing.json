{"v": "5.1.16", "fr": 29.9700012207031, "ip": 0, "op": 27.0000010997325, "w": 200, "h": 100, "nm": "Comp 1", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 5, "s": [0], "e": [100]}, {"t": 14.0000005702317}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 6, "s": [72, 74.8, 0], "e": [72, 62.8, 0], "to": [0, -2, 0], "ti": [0, 1.45000004768372, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 12, "s": [72, 62.8, 0], "e": [72, 66.1, 0], "to": [0, -1.45000004768372, 0], "ti": [0, -0.28333333134651, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 18, "s": [72, 66.1, 0], "e": [72, 64.5, 0], "to": [0, 0.28333333134651, 0], "ti": [0, 0.21666666865349, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "n": "0p667_1_0p167_0", "t": 24, "s": [72, 64.5, 0], "e": [72, 64.8, 0], "to": [0, -0.21666666865349, 0], "ti": [0, -0.05000000074506, 0]}, {"t": 30.0000012219251}], "ix": 2}, "a": {"a": 0, "k": [-32.5, 5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 5, "s": [20, 20, 100], "e": [100, 100, 100]}, {"t": 14.0000005702317}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [15, 15], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.870588235294, 0.874509803922, 0.878431372549, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-32.618, 4.916], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -4.00000016292334, "op": 86.0000035028518, "st": -4.00000016292334, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [100], "e": [0]}, {"t": 9.00000036657752}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 0, "s": [72, 64.75, 0], "e": [72, 44.8, 0], "to": [0, -3.32500004768372, 0], "ti": [0, 3.32500004768372, 0]}, {"t": 9.00000036657752}], "ix": 2}, "a": {"a": 0, "k": [-32.5, 5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 2, "s": [100, 100, 100], "e": [120, 120, 100]}, {"t": 11.0000004480392}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [15, 15], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.870588235294, 0.874509803922, 0.878431372549, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-32.618, 4.916], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Ã¥ÂÂÃ¤Â¸ÂªÃ§ÂÂ¹", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [49.75, 38.5, 0], "ix": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 100, "h": 100, "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Ã¥ÂÂÃ¤Â¸ÂªÃ§ÂÂ¹", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [74.75, 38.5, 0], "ix": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 100, "h": 100, "ip": 2.00000008146167, "op": 92.0000037472368, "st": 2.00000008146167, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [96.75, 53.25, 0], "ix": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime(sub(key(nearestKeyIndex).time, div(thisComp.frameDuration, 10)));\n    amplitude = 0.1;\n    frequency = 1.8;\n    decay = 10;\n    $bm_rt = sum(value, div(mul(mul(calculatedVelocity, amplitude), Math.sin(mul(mul(mul(frequency, currentTime), 2), Math.PI))), Math.exp(mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-32.5, 5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [15, 15], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.870588235294, 0.874509803922, 0.878431372549, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-32.618, 4.916], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 3.00000012219251, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [121.75, 53.25, 0], "ix": 2, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime(sub(key(nearestKeyIndex).time, div(thisComp.frameDuration, 10)));\n    amplitude = 0.1;\n    frequency = 1.8;\n    decay = 10;\n    $bm_rt = sum(value, div(mul(mul(calculatedVelocity, amplitude), Math.sin(mul(mul(mul(frequency, currentTime), 2), Math.PI))), Math.exp(mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [-32.5, 5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [15, 15], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.870588235294, 0.874509803922, 0.878431372549, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-32.618, 4.916], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 6.00000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "Ã¥ÂÂÃ¤Â¸ÂªÃ§ÂÂ¹", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [99.75, 38.5, 0], "ix": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 100, "h": 100, "ip": 4.00000016292334, "op": 94.0000038286985, "st": 4.00000016292334, "bm": 0}], "markers": []}