import { ReactNode } from "react";

export type PurchaseStep = "select" | "confirm" | "success" | "receipt";

export interface TransactionData {
	amount: string;
	fee: string;
	total: string;
	rate: string;
	date: string;
	paymentMethod: string;
}

export interface CryptoTransaction {
	amount: string;

	fee: string;

	address: string;

	network: string;

	coin: string;

	source: string;

	date: string;
}

export interface SelectOption {
	value: string;

	label: string;

	icon?: ReactNode;
}

export type SendMode = "wallet" | "username";

export type SendStep =
	| "initial"
	| "form"
	| "qrScanner"
	| "confirm"
	| "createPin"
	| "pinSuccess"
	| "enterPin"
	| "success"
	| "receipt";

export interface TransactionData {
	amount: string;

	fee: string;

	total: string;

	rate: string;

	date: string;

	paymentMethod: string;
}
