

export interface ISwapPriceData {
	created: string;
	rate: number;
	expires: boolean;
	expiry: number;
	price_id: string;
}

export interface SwapPriceData {
	created: string;
	rate: number;
	expires: boolean;
	expiry: number;
	"price id": string;
}

export interface SwappingPricePayload {
	from_currency: string;
	to_currency: string;
	amount: string;
	method: string;
}

export interface GetSwappingPriceResponse {
	message: string;
	swap_price_data: ISwapPriceData;
}

export interface CreateSwapPayload {
	rate_id: string;
	amount: string | number;
	from_account: string;
	to_account: string;
	type: "internal" | "external" | string;
}

export interface CreateSwapResponse {
	message: "success";
	details: string;
	swap_data:
		| {
				swap_id: string;
		  }
		| string;
}

export interface GetSwappingRateResponse {
	message: "success";
	swap_rate_data: {
		rate_id: string;
	};
}
