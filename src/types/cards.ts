
export interface CreateCardPayload {
	user_id:string
	phone_number: string
	street: string
	city: string
	state: string
	postal_code: string
}

export interface CreateCardResponse {
	message: string;
}

export interface FundCardPayload {
	amount: string;
	card_id: string;
}

export interface WithdrawCardPayload {
	amount: string;
	card_id: string;
}

export interface ICard {
	id: string
	masked_pan: string
	status: "DISABLED" | "ACTIVE"
	issuer: "MASTERCARD"
	currency: string
	balance: number
	response: string
}

export interface CardListResponse {
	message: string;
	data: Array<ICard>;
}


export interface CardDetailsResponse {
	message: string;
	data: {
		id: string;
		name: string;
		card_number: string;
		masked_pan: string;
		expiry: string;
		cvv: string;
		status: string;
		type: string;
		issuer: string;
		currency: string;
		balance: number;
		balance_updated_at: string;
		auto_approve: boolean;
		address: {
			street: string;
			city: string;
			state: string;
			postal_code: string;
			country: string;
		};
		created_at: string;
		updated_at: string;
	};
}

export interface CardTransactionsResponse {
	message: string;
	data: Array<{
		id: string;
		amount: number;
		currency: string;
		description: string;
		status: string;
		entry: string;
		type: string;
		merchant: {
			name: string;
			city: string;
			country: string;
		};
		authorization_amount: number;
		authorization_currency_code: string | null;
		authorization_code: string | null;
		card_acceptor_mcc: string;
		card_acceptor_mid: string;
		card_acceptor_state: string;
		created_at: string;
	}>;
}

export interface FreezeUnfreezeCardPayload {
	type: "FREEZE CARD" | "UNFREEZE CARD";
	card_id: string;
}
