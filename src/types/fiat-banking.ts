export interface IFiatWithdrawalMethod {
	currency_name: string;
	name: string;
	currency: string;
	image: string;
	restrictions: {
		FeeLimits: number[];
		RestrictedRegion: string[];
		OnlyAllowedRegion: string[];
		TransactionLimits: number[];
	};
	fee: string;
	"fee type": "STATIC" | "DYNAMIC" | "PERCENTAGE";
	fee_type: "STATIC" | "DYNAMIC" | "PERCENTAGE";
}

export interface IFiatDepositMethod {
	name: string;
	currency: string;
	image: string;
	is_general: boolean;
	data?: {
		"Bank name": string;
		"Account number": string;
	};
	restrictions: {
		FeeLimits: number[];
		TransactionLimits: number[];
		Transactionlimits?: number[];
	};
	fee: string;
	fee_type: "STATIC" | "PERCENTAGE";
}

export interface FiatWithdrawalMethodsResponse {
	message: string;
	data: IFiatWithdrawalMethod[];
}

export interface Bank {
	"Bank name": string;
	"Bank code": string;
}

export interface BankListResponse {
	message: string;
	data: Bank[];
}

export interface ResolveAccountPayload {
	accountNumber: string;
	bankCode: string;
	transaction_method: string;
}
export interface ResolveAccountResponse {
	message: string;
	data: {
		account_number: string;
		account_name: string;
		bank_id: number;
	};
}

// export interface CreateWithdrawalPayload {
// 	account_id: string;
// 	transaction_method: string;
// 	amount: string | number;
// 	accountNumber: string;
// 	bankCode: string;
// 	fee: string | number;
// 	description: string;
// }

export interface CreateWithdrawalPayload {
  account_id: string;
  transaction_method: string;
  amount: string | number;
  receiver?: string; // For internal transfers
  accountNumber?: string; // For external transfers
  bankCode?: string; // For external transfers
  fee: string | number;
  note: string;
//   recurrsion: {
//     start_date: string;
//     end_date: string;
//     frequency: string;
//     day_of_week: string | null;
//     date_of_month: string | null;
//   };
}

export interface CreateDepositPayload {
	account_id: string;
	transaction_method: string;
	amount: string | number;
}

type PaymentStatus = "CREATED";

interface PaymentDetail {
	account_name: string;
	account_number: string;
	bank_name: string;
	currency: string;
	expires_at: string;
	merchant_reference: string;
	amount: number;
	created_at: string;
	fee_bearer: string;
}

interface PaymentResponseData {
	id: string;
	status: PaymentStatus;
	use_link: boolean;
	link: string;
	payment_detail: string | PaymentDetail;
}

export interface CreateDepositResponse {
	message: string;
	data: PaymentResponseData;
}

interface WithdrawTransaction {
	id: string;
	currency: string;
	amount: string;
	fee: string;
	status: string;
	reference: string;
	transaction_type: string;
	receiver_account: string | null;
	receiver_bank: string | null;
	user_id: string;
	receiver_name: string | null;
	updatedAt: string;
	createdAt: string;
	meta: any | null;
}

export interface CreateWithdrawalResponse {
	message: string;
	data: WithdrawTransaction;
}

export interface GetDepositMethodResponse {
	message: string;
	data: IFiatDepositMethod[];
}
