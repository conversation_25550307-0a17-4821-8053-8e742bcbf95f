export interface MarketDataPayload {
	currency: string;
	page: number;
	size: number;

}

export interface CoinMarketData {
	id: string;
	symbol: string;
	name: string;
	image: string;
	current_price: number;
	market_cap: number;
	market_cap_rank: number;
	total_volume: number;
	price_change_percentage_24h: number;
	currency: string;
}

export type MarketDataResponse = { data: { coins: CoinMarketData[] } }

export interface MarketChartPayload {
	coin_id: string;
	currency: string;
	days: number;
	interval: string;
}

export interface MarketChartResponse {
	data: any;
	prices: [number, number][];
	market_caps: [number, number][];
	total_volumes: [number, number][];
}

export interface CoinDataPayload {
	coin_id: string;
	currency: string
}

export interface CoinDataResponse {
	data: any;
	id: string;
	symbol: string;
	name: string;
	description: {
		[lang: string]: string;
	};
	market_data: {
		current_price: {
			[currency: string]: number;
		};
		market_cap: {
			[currency: string]: number;
		};
		price_change_percentage_24h: number;
		price_change_percentage_7d: number;
		price_change_percentage_30d: number;
	};
	image: {
		thumb: string;
		small: string;
		large: string;
	};
	// data: string;
}
