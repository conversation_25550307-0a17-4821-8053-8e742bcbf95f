export interface GetSavingGoalsResponse {
	message: string;
	savingsGoals: SavingsGoal[];
}
export interface CreateFiatSavingGoalPayload {
	title: string;
	targetAmount: number;
	startDate: string;
	endDate: string;
	currency: string;
	wallet_id: string;
}

export interface CreateFiatSavingGoalResponse {
	message: string;
	data: SavingsGoal;
}

export interface SavingsDetails extends SavingsGoal {
	fundingSource: string;
	accountName: string;
}

export interface SavingsGoal {
	id: string;
	title: string;
	targetAmount: number;
	preferredTime?: string | null;
	amountSaved: number;
	startDate: string;
	endDate: string;
	amountPerInterval?: number | null;
	currency: string;
	wallet_id: string;
	autoSave: boolean;
	user_id: string;
	goalStatus: string;
	debited?: number | null;
	status: string;
	createdAt: string;
	updatedAt: string;
}
export interface FundFiatSavingsPayload {
	user_id: string;
	savingsGoalId: string;
	amount: number;
}
export interface WithdrawFiatSavingsPayload {
	user_id: string;
	savingsGoalId: string;
	amount: number;
}

export interface FundFiatSavingsResponse {
	message: string;
}
export interface WithdrawFiatSavingsResponse {
	message: string;
}
export interface AutoSavePayload {
	user_id: string;
	savingsGoalId: string;
	preferredTime: string;
	savePreference: string; 
}
