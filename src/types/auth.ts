interface User {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
	phone: string;
}

export interface RegisterUserResponse {
	message: string;
	user: User;
}

export interface CreateUserPayload {
	email: string;
	phone: string;
	password: string;
	referralCode?: string;
}

export interface ActivateUserPayload {
	user_id: string;
	authToken: string;
}

export interface ApiResponse {
	message: string;
	details?: string;
	error?: string;
}




export interface ResendOTPPayload {
	user_id: string;
}




export interface RetrievePasswordEmailPayload {
	identifier: string;
}




export interface ForgetPasswordPayload {
	identifier: string;
	password: string;
}

export interface UserData {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
	phone: string | null;
	proMode?: boolean;
}

export interface ForgetPasswordResponse extends ApiResponse {
	user: UserData;
}


export interface LoginPayload {
	identifier: string;
	password: string;
}

export interface LoginResponse extends ApiResponse {
	kycDone: boolean;
	bvnDone: boolean;
	user: UserData;
	token: string;
}


export interface ChangePasswordPayload {
	password: string;
	newPassword: string;
}

export interface ChangePasswordResponse extends ApiResponse {
	details: string;
}


export interface SetPinPayload {
	user_id: string;
	pin: string;
}

export interface ConfirmCodePayload {
	identifier: string;
	authToken: string;
}

export interface SetPinResponse extends ApiResponse {
	details: string;
}
