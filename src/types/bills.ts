interface IBillerData {
	id: number;
	operatorId: number;
	biller_code: string;
	name: string;
	default_commission: number;
	date_added: string;
	country: string;
	is_airtime: boolean;
	biller_name: string;
	item_code: string;
	short_name: string;
	fee: number;
	commission_on_fee: boolean;
	reg_expression: string;
	label_name: string;
	amount: number;
	is_resolvable: boolean;
	group_name: string;
	category_name: string;
	is_data: boolean | null;
	default_commission_on_amount: number | null;
	commission_on_fee_or_amount: number | null;
	validity_period: string | null;
}

export interface AirtimeBillsPayload {
  biller_type: string;
}

export interface AirtimeBillsResponse {
	message: string;
	data: IBillerData[];
}

export interface ValidateCustomerBillResponse {
	message: string;
	data: IBillerData[];
}

export interface ValidateCustomerBillPayload {
	biller_code: string;
}

export interface PurchaseBillPayload {
	biller_code: string;
	item_code: string;
	customer_id: string;
	amount: number;
}

export interface PurchaseBillResponse {
	message: string;
	data: {
		id: string;
		customer_id: string;
		vendor_charge: string;
		reference: string;
		totalCharge: string;
		clyp_charge: string;
		network: string;
		recharge_token: string | null;
		amount: string;
		totalAmount: string;
		user_id: string;
		status: string;
		updatedAt: string;
		createdAt: string;
	};
}


export interface PurchaseAirtimePayload {
    purchase_type: string;
    operator_id: string;
    phone_number: string;
    amount: number;
    country_code: string;
    currency: string;
    use_local_amount: boolean;
    user_id?: string;
}

export interface PurchaseAirtimeResponse {
    message: string;
    success: boolean;
    data: {
        id: string;
        customer_id: string;
        vendor_charge: string;
        reference: string;
        totalCharge: string;
        clyp_charge: string;
        network: string;
        recharge_token: string | null;
        amount: string;
        totalAmount: string;
        user_id: string;
        status: string;
        updatedAt: string;
        createdAt: string;
    };
}