export interface GetSavingGoalsResponse {
    message: string;
    data: { 
        savingsGoals: SavingsGoal[]; 
    };
}

export interface SavingsGoal {
  id: string;
  title: string;
  targetAmount: number;
  preferredTime?: string | null;
  amountSaved: number;
  startDate: string; 
  endDate: string;   
  amountPerInterval?: number | null;
  currency: string;
  wallet_id: string;
  autoSave: boolean;
  user_id: string;
  goalStatus: string;
  debited?: number | null;
  status: string;
  createdAt: string; 
  updatedAt: string; 
  
}
export interface CryptosavingsGoalPayload{
    title: string;
    targetAmount: number;
    startDate: string;
    endDate: string;
    currency: string;
    wallet_id: string;
}


export interface FundSavingsResponse{
  amount:string;
}
export interface FundSavingsPayload{
  user_id: string,
  savingsGoalId: string,
  amount: number
}
export interface WithdrawCryptoSavingsPayload {
  user_id: string;
  savingsGoalId: string;
  amount:number;
}
export interface WithdrawSavingsResponse{
  message:string;
}