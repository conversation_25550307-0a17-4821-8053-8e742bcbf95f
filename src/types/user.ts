export interface TUser {
	a_id: string;
	referee: string;
	activated: boolean;
	authToken: string | null;
	clyp_id: string;
	country: string;
	country_code: string;
	country_id: string;
	createdAt: string;
	email: string;
	emailNotifSetting: any | null;
	expoPushToken: string | null;
	extra_authorization: ExtraAuthorization[]
	f_id: string | null;
	first_name: string;
	g_id: string | null;
	general_authorization: GeneralAuthorization[]
	id: string;
	is_merchant: boolean | null;

	last_name: string;
	meta: any | null;
	password: string | null;
	phone: string | null;
	picture: string | null;
	pin: string | null;
	
	priceAlertSettings: any | null;
	proMode: any | null;
	role: string | null;
	sos: any | null;
	sso: string;
	status: string;
	token: string | null;
	updatedAt: string;
	username: string | null;
}

export interface KYCLevels {
	level_one: boolean;
	level_two: boolean;
	level_three: boolean;
	level_four: boolean;
	level_five: boolean;
}
export interface UserResponse {
	id:string;
	first_name: string;
	last_name: string;
	email: string;
	phone: string;
	promode?: boolean | null;
}

export interface FullUserDataResponse {
	message: string;
	user: IUser;
	kycDone: boolean;
	bvnDone: boolean;
	kyc_level_completed: KycLevels;
}

export interface IUser {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
	phone: string;
	password: string;
	status: string;
	country: string | null;
	country_id: string | null;
	country_code: string | null;
	proMode: boolean | null;
	role: string | null;
	emailNotifSetting: any | null;
	priceAlertSettings: any | null;
	pushNotificationSettings: any | null;
	activated: boolean;
	pin: string | null;
	picture: string | null;
	token: string | null;
	authToken: string;
	a_id: string | null;
	clyp_id: string;
	username: string | null;
	f_id: string | null;
	g_id: string | null;
	sos: any | null;
	sso: string;
	is_merchant: boolean | null;
	expoPushToken: string;
	referee: string | null;
	referral_balance: number | null;
	meta: any | null;
	createdAt: string;
	updatedAt: string;
	preference: Preference[];
	extra_authorization: ExtraAuthorization[];
	general_authorization: GeneralAuthorization[];
}

interface Preference {
	id: string;
	mode: string;
	currency_code: string;
	biometric: boolean | null;
	pin: boolean | null;
	private_mode: boolean;
	merchant_mode: boolean;
	meta: any | null;
	createdAt: string;
	updatedAt: string;
	user_id: string;
}

export interface ExtraAuthorization {
	id: string;
	status: string;
	permission_type: string;
	permission_name: string;
	meta: any | null;
	createdAt: string;
	updatedAt: string;
	user_id: string;
}

export interface GeneralAuthorization {
	id: string;
	can_withdraw: boolean;
	can_deposit: boolean;
	can_transfer: boolean;
	can_buy: boolean;
	can_sell: boolean;
	meta: any | null;
	createdAt: string;
	updatedAt: string;
	user_id: string;
}

interface KycLevels {
	level_one: boolean;
	level_two: boolean;
	level_three: boolean;
	level_four: boolean;
	level_five: boolean;
}

export interface IClypUser {
	first_name: string
	id: string
	last_name: string
}