export interface ICryptoWallet {
	id: string;
	coin_id: string;
	isFavorite: string;
	image: string;
	account_id: string;
	user_id: string;
	address: string;
	currency: string;
	coin_name: string;
	currency_id: string;
	balance: 0;
	can_deposit: boolean;
	can_withdraw: boolean;
	can_swap: boolean;
	can_buy: boolean;
	can_sell: boolean;
	has_card: boolean;
	can_save: boolean;
	meta: {
		"method name": string;
		detail: {
			CreationMetadata: string;
			metadata: {
				addresses: [string];
			};
			address: string;
		};
	};
	createdAt: string;
	updatedAt: string;
}

export interface CryptoWalletResponse {
    message: string;
    user_wallets: ICryptoWallet[];
}


export interface AddCryptoWalletResponse {
	currencies: string;
}
export type AddCryptoWalletPayload = {
  userId: string;
  currency: string;
  address: string;
  label?: string;
};