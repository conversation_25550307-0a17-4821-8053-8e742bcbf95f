interface Restrictions {
	FeeLimits: number[];
	RestrictedRegion: string[];
	OnlyAllowedRegion: string[];
	TransactionLimits: number[];
}

export interface ICryptoDepositMethod {
	name: string;
	image?: string;
	currency: string;
	restrictions: Restrictions;
	fee: string;
	fee_type: "STATIC" | "PERCENTAGE";
}

export interface DepositMethodsResponse {
	message: string;
	options: ICryptoDepositMethod[];
}

export interface ICryptoWithdrawalMethod {
	name: string;
	image?: string;
	currency: string;
	fee: string;
	network?: string;
	fee_type: "STATIC" | "PERCENTAGE";
	restrictions: {
		FeeLimits: number[];
		RestrictedRegion: string[];
		OnlyAllowedRegion: string[];
		TransactionLimits: number[];
	};
}

export interface WithdrawalMethodsResponse {
	message: string;
	options: ICryptoWithdrawalMethod[];
}

export interface ICryptoWithdrawalRequest {
	to_address: string;
	amount: number;
	currency: string;
	network: string;
	withdrawal_method: string;
	paynetwork: string;
}

interface TransactionDetails {
	clyp_txID: string;
	id: string;
	provider_txID: string | null;
	user_id: string;
	asset: string;
	wallet_id: string;
	date_time: string;
	amount: string;
	transaction_type: "debit" | "credit";
	status: string;
	fee: string;
	currency: string;
	provider: string;
	image: string;
	meta: null | any;
	updatedAt: string;
	createdAt: string;
}

interface WalletUpdate {
	id: string;
	account_id: string;
	user_id: string;
	address: string | null;
	currency: string;
	currency_id: string | null;
	balance: number;
	can_deposit: boolean;
	can_withdraw: boolean;
	can_swap: boolean;
	can_buy: boolean;
	can_sell: boolean;
	has_card: boolean;
	can_save: boolean;
	can_buy_gift_cards: boolean;
	can_pay_bills: boolean;
	can_pay_travel: boolean;
	can_apply_loan: boolean;
	can_ai: boolean;
	can_reward: boolean;
	can_invest: boolean;
	can_expert: boolean;
	can_otc: boolean;
	can_p2p: boolean;
	can_web3: boolean;
	can_price_alerts: boolean;
	meta: null | any;
	createdAt: string;
	updatedAt: string;
}

export interface ICryptoWithdrawalResponse {
	message: string;
	details: string;
	transaction_details: TransactionDetails;
	completion: [number, WalletUpdate[]];
}

export interface ICryptoUniversalNetworkData {
	currency: string;
	max_amount: number;
	min_amount: number;
}

export interface ICryptoUniversalDepositMethodsResponse {
	message: string;
	options: ICryptoUniversalNetworkData[];
}

export interface ICryptoWithdrawalRequest {
	to_address: string;
	amount: number;
	currency: string;
	network: string;
	withdrawal_method: string;
	paynetwork: string;
}

export interface ICryptoDepositRequest {
	currency: string;
	amount?: number;
	method: string;
	paynetwork?: string;
}

export interface ICryptoDepositResponse {
	message: string;
	deposit_data: {
		message: string;
		data: ICryptoDepositData;
		address?: string;
		name?: string;
	};
}

export interface ICryptoDepositData {
	deposit_address?: string;
	deposit_currency?: string;
	expected_amount?: number;
	expiry_time?: number;
	id?: string;
	status?: string;

	message?: string;
	address?: string;
	name?: string;
}


export interface AvailableCurrenciesResponse {
  message: string;
  available_currencies: AvailableCurrency[];
}

export interface AvailableCurrency {
  currency: string;
  restrictions: CurrencyRestrictions;
  coin_name: string;
  image: string;  
  coin_id: string;
}

export interface CurrencyRestrictions {
  precision: number;
  CanDeposit: boolean;
  CanWithdraw: boolean;
  RestrictedRegion: string[];
  OnlyAllowedRegion: string[];
  DebitTransactionLimits: number[];  // [min, max]
  CreditTransactionLimits: number[]; // [min, max]
}