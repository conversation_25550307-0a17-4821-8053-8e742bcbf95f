import { useState } from "react";
import { <PERSON>, EyeOff } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { AuthCard } from "@/components/custom/card/auth-card";
import { AuthInputField } from "@/components/custom/input/auth-input";
import Checkbox from "@/components/custom/input/checkbox";
// import SocialLogin from "@/components/social-login";
import { Link, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import cn from "@/utils/class-names";
import { useModal } from "@/components/modal-view/use-modal";
import { useLogin } from "@/hooks/api/auth";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import { loginSchema, TLoginSchema } from "@/utils/validators/login.schema";
import { useRefreshUserData } from "@/hooks/api/user";

export default function LoginPage() {
	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<TLoginSchema>({
		mode: "all",
		resolver: zodResolver(loginSchema),
	});

	const navigate = useNavigate();
	const [showPassword, setShowPassword] = useState(false);
	const [rememberMe, setRememberMe] = useState(false);

	const { mutate: loginMutation, isPending } = useLogin();
	const { mutateAsync: fetchUserDataMutation } = useRefreshUserData();

	const toggleRememberMe = () => {
		setRememberMe(!rememberMe);
	};

	const onSubmit = async (data: TLoginSchema) => {
		const payload = {
			identifier: data.email,
			password: data.password,
		};

		loginMutation(payload, {
			onSuccess: (response) => {
				// console.log(response)
				localStorage.setItem("auth_token", response.token);
				localStorage.setItem("firstName" , response.user.first_name)
				localStorage.setItem("lastName" , response.user.last_name)
				fetchUserDataMutation();

				notify(
					"Login successful! Redirecting...",
					ResponseStatus.SUCCESS,
				);

				setTimeout(() => {
					navigate("/");
				}, 1000);
			},
			onError: () => {
				const errorMessage =
					"Login failed. Please check your credentials.";
				notify(errorMessage, ResponseStatus.ERROR);
			},
		});
	};

	return (
		<div className="bg-white">
			<main className="flex justify-center items-center py-12 px-4">
				<AuthCard
					title="Welcome back!"
					subtitle="To log in your account with Clyppay, please put in your email address and password in the field below"
				>
					<form onSubmit={handleSubmit(onSubmit)}>
						<div className="space-y-6">
							<AuthInputField
								label="Email Address"
								type="email"
								id="email"
								register={register("email")}
								error={errors.email?.message}
							/>

							<AuthInputField
								label="Password"
								type={showPassword ? "text" : "password"}
								id="password"
								register={register("password")}
								error={errors.password?.message}
								rightElement={
									showPassword ? (
										<EyeOff
											className="h-5 w-5 cursor-pointer"
											onClick={() =>
												setShowPassword(false)
											}
										/>
									) : (
										<Eye
											className="h-5 w-5 cursor-pointer"
											onClick={() =>
												setShowPassword(true)
											}
										/>
									)
								}
							/>

							{/* Remember me and forgot password */}
							<div className="flex items-center justify-between">
								<Checkbox
									checked={rememberMe}
									onChange={toggleRememberMe}
									label="Remember Me"
									className="size-5"
								/>

								<Link
									to="/forgot-password"
									className="text-primary hover:text-primary/70 text-sm font-medium"
								>
									Forgot Password?
								</Link>
							</div>

							{/* Continue button */}
							<div className="text-center">
								<Button
									type="submit"
									disabled={isPending}
									className={cn(
										"w-[80%] text-white font-medium py-8 px-4 rounded-full transition-colors",
									)}
								>
									{isPending ? "Logging in..." : "Continue"}
								</Button>
							</div>

							{/* <SocialLogin /> */}
						</div>
					</form>

					{/* Register link */}
					<div className="mt-8 text-center">
						<span className="text-gray-600">
							Don&apos;t have an account?{" "}
						</span>
						<Link
							to="/register"
							className="text-primary hover:text-primary/80"
						>
							Register here
						</Link>
					</div>
				</AuthCard>
			</main>
			{/* <ModalTriggerExample /> */}
		</div>
	);
}

export function ModalTriggerExample() {
	const { openModal } = useModal();

	const handleOpenModal = () => {
		openModal({
			view: <ModalContent />,
			size: "md",
			position: "center",
			animation: "fade",
			closeOnOutsideClick: true,
		});
	};

	return (
		<div className="p-4">
			<Button onClick={handleOpenModal}>Open Example Modal</Button>
		</div>
	);
}

function ModalContent() {
	const { closeModal } = useModal();

	return (
		<div className="p-6">
			<h2 className="text-2xl font-bold mb-4">Modal Title</h2>
			<p className="mb-6">
				This is an example modal content. You can put any React
				components here.
			</p>
			<div className="flex justify-end space-x-3">
				<Button variant="outline" onClick={closeModal}>
					Cancel
				</Button>
				<Button onClick={closeModal}>Confirm</Button>
			</div>
		</div>
	);
}
