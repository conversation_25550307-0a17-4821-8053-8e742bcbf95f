import { Button } from "@/components/ui/button";
import { AuthInputField } from "@/components/custom/input/auth-input";
import { AuthCard } from "@/components/custom/card/auth-card";
import { useNavigate, useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import cn from "@/utils/class-names";
import { z } from "zod";
import { useEffect } from "react";

import { useForgetPassword } from "@/hooks/api/auth";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";

const setNewPasswordSchema = z
	.object({
		password: z.string().min(8, "Password must be at least 8 characters"),
		confirmPassword: z.string().min(1, "Please confirm your password"),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

type TSetNewPasswordSchema = z.infer<typeof setNewPasswordSchema>;

export default function CreateNewPasswordPage() {
	const navigate = useNavigate();
	const location = useLocation();
	const email = location.state?.email || "";

	useEffect(() => {
		if (!email) {
			notify("Email address is required", ResponseStatus.ERROR);
			navigate("/forgot-password"); 
		}
	}, [email, navigate]);

	const {
		register,
		handleSubmit,
		formState: { errors, isValid },
	} = useForm<TSetNewPasswordSchema>({
		mode: "onChange",
		resolver: zodResolver(setNewPasswordSchema),
	});

	const { mutate: forgotPasswordMutation, isPending } = useForgetPassword();

	const onSubmit = (data: TSetNewPasswordSchema) => {
		const payload = {
			identifier: email, 
			password: data.password,
		};

		forgotPasswordMutation(payload, {
			onSuccess: () => {
				notify("Password reset successful!", ResponseStatus.SUCCESS);

				setTimeout(() => {
					navigate("/login"); 
				}, 1500);
			},
			onError: (error) => {
				console.error(error);
				const errorMessage =
					error?.message ||
					"Failed to reset password. Please try again.";
				notify(errorMessage, ResponseStatus.ERROR);
			},
		});
	};

	return (
		<div className="py-14 w-[40rem]">
			<AuthCard
				title="Create New Password"
				subtitle={`For ${email}`} 
				titleClassName="text-start mt-12"
				subtitleClassName="text-start"
			>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="space-y-6">
						<AuthInputField
							label="New Password"
							type="password"
							register={register("password")}
							error={errors.password?.message}
							className={
								errors.password?.message ? "border-red-500" : ""
							}
							labelClassName={
								errors.password?.message ? "text-red-500" : ""
							}
						/>

						<AuthInputField
							label="Confirm Password"
							type="password"
							register={register("confirmPassword")}
							error={errors.confirmPassword?.message}
							className={
								errors.confirmPassword?.message
									? "border-red-500"
									: ""
							}
							labelClassName={
								errors.confirmPassword?.message
									? "text-red-500"
									: ""
							}
						/>

						<div className="text-center">
							<Button
								type="submit"
								disabled={isPending || !isValid}
								className={cn(
									"w-[80%] text-white font-medium py-8 px-4 rounded-full transition-colors",
									{
										"opacity-50 cursor-not-allowed":
											isPending || !isValid,
									},
								)}
							>
								{isPending ? "Processing..." : "Reset Password"}
							</Button>
						</div>
					</div>
				</form>
			</AuthCard>
		</div>
	);
}
