"use client";

import { useState } from "react";
import { <PERSON>, EyeOff } from "lucide-react";
import { AuthInputField } from "@/components/custom/input/auth-input";
import { AuthCard } from "@/components/custom/card/auth-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import SocialLogin from "@/components/social-login";
import { Link, useNavigate } from "react-router-dom";
import AuthService from "@/services/auth";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import cn from "@/utils/class-names";

const registerSchema = z.object({
	email: z
		.string()
		.email("Please enter a valid email address")
		.min(1, "Email is required"),
	phone: z
		.string()
		.min(1, "Phone number is required")
		.regex(
			/^\d{10,}$/,
			"Must be a valid phone number with at least 10 digits",
		),
	password: z
		.string()
		.min(8, { message: "Password must be at least 8 characters" })
		.regex(/[A-Z]/, {
			message: "Password must contain at least one uppercase letter",
		})
		.regex(/[0-9]/, {
			message: "Password must contain at least one number",
		})
		.regex(/[^A-Za-z0-9]/, {
			message: "Password must contain at least one special character",
		}),
	// referralCode: z.string().optional(),
});

type TRegisterSchema = z.infer<typeof registerSchema>;

export default function RegisterPage() {
	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<TRegisterSchema>({
		mode: "onChange",
		resolver: zodResolver(registerSchema),
		defaultValues: {
			email: "",
			phone: "",
			password: "",
			// referralCode: "",
		},
	});

	const navigate = useNavigate();
	const [showPassword, setShowPassword] = useState(false);

	const onSubmit = async (data: TRegisterSchema) => {
		try {
			const response = await AuthService.registerUser({
				email: data.email,
				phone: data.phone,
				password: data.password,
				// referralCode: data.referralCode,
			});

			if (response.user) {
				navigate("/confirm-email", {
					state: { user: response.user }
				});
			}
		} catch (error) {
			console.error("Registration failed:", error);
		}
	};

	return (
		<div className="py-14">
			<AuthCard
				title="Welcome to Clyp!"
				subtitle="To create an account with Clyppay, please put in your email address in the field below"
			>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="space-y-6">
						<AuthInputField
							label="Email Address"
							type="email"
							id="email"
							register={register("email")}
							error={errors.email?.message}
							className={
								errors.email?.message ? "border-red-500" : ""
							}
							labelClassName={
								errors.email?.message ? "text-red-500" : ""
							}
						/>

						<AuthInputField
							label="Phone Number"
							type="tel"
							id="phone"
							register={register("phone")}
							error={errors.phone?.message}
							className={
								errors.phone?.message ? "border-red-500" : ""
							}
							labelClassName={
								errors.phone?.message ? "text-red-500" : ""
							}
						/>

						<AuthInputField
							label="Password"
							type={showPassword ? "text" : "password"}
							id="password"
							register={register("password")}
							error={errors.password?.message}
							className={
								errors.password?.message ? "border-red-500" : ""
							}
							labelClassName={
								errors.password?.message ? "text-red-500" : ""
							}
							rightElement={
								showPassword ? (
									<EyeOff
										className="h-5 w-5 cursor-pointer"
										onClick={() => setShowPassword(false)}
									/>
								) : (
									<Eye
										className="h-5 w-5 cursor-pointer"
										onClick={() => setShowPassword(true)}
									/>
								)
							}
						/>

						{/* <AuthInputField
							label="Referral code (optional)"
							type="text"
							id="referralCode"
							register={register("referralCode")}
							error={errors.referralCode?.message}
							className={
								errors.referralCode?.message
									? "border-red-500"
									: ""
							}
							labelClassName={
								errors.referralCode?.message
									? "text-red-500"
									: ""
							}
						/> */}

						<div className="text-center">
							<Button
								type="submit"
								className={cn(
									"w-[80%] text-white font-medium py-8 px-4 rounded-full transition-colors",
								)}
							>
								Continue
							</Button>
						</div>

						{/* <SocialLogin /> */}
					</div>
				</form>

				{/* Login link */}
				<div className="mt-8 text-center">
					<span className="text-gray-600">
						Already have an account?{" "}
					</span>
					<Link
						to="/login"
						className="text-primary hover:text-primary/80"
					>
						Login here
					</Link>
				</div>
			</AuthCard>
		</div>
	);
}
