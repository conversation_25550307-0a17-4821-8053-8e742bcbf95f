import { But<PERSON> } from "@/components/ui/button";
import { AuthInputField } from "@/components/custom/input/auth-input";
import { AuthCard } from "@/components/custom/card/auth-card";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import cn from "@/utils/class-names";
import {
	forgotPasswordSchema,
	TForgotPasswordSchema,
} from "@/utils/validators/forget-password.schema";
import { useRetrievePasswordEmail } from "@/hooks/api/auth";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";

export default function ForgotPasswordPage() {
	const navigate = useNavigate();

	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<TForgotPasswordSchema>({
		mode: "onChange",
		resolver: zodResolver(forgotPasswordSchema),
		defaultValues: {
			email: "",
		},
	});

	const { mutate: retrievePasswordEmail, isPending } =
		useRetrievePasswordEmail();

	const onSubmit = (data: TForgotPasswordSchema) => {
		const payload = {
			identifier: data.email,
		};

		retrievePasswordEmail(payload, {
			onSuccess: () => {
				notify(
					"Password reset code sent! Please check your email.",
					ResponseStatus.SUCCESS,
				);

				setTimeout(() => {
					navigate("/email-verification", {
						state: { email: data.email },
					});
				}, 1500);
			},
			onError: (error) => {
				console.error(error);
				const errorMessage =
					error?.message ||
					"Failed to send password reset code. Please try again.";
				notify(errorMessage, ResponseStatus.ERROR);
			},
		});
	};

	return (
		<div className="py-14 w-[40rem]">
			<AuthCard
				title="Forgot Password"
				subtitle="Don't worry! It happens. Please enter the email associated with your account."
				titleClassName="text-start mt-12"
				subtitleClassName="text-start"
			>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="space-y-6">
						<AuthInputField
							label="Email Address"
							type="email"
							id="email"
							register={register("email")}
							error={errors.email?.message}
							className={
								errors.email?.message ? "border-red-500" : ""
							}
							labelClassName={
								errors.email?.message ? "text-red-500" : ""
							}
						/>

						<div className="text-center">
							<Button
								type="submit"
								disabled={isPending}
								className={cn(
									"w-[80%] text-white font-medium py-8 px-4 rounded-full transition-colors",
								)}
							>
								{isPending ? "Sending..." : "Send Code"}
							</Button>
						</div>
					</div>
				</form>
			</AuthCard>
		</div>
	);
}
