import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { AuthCard } from "@/components/custom/card/auth-card";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { useLocation, useNavigate } from "react-router-dom";
import { useRetrievePasswordEmail, useActivateUser } from "@/hooks/api/auth";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import {
	otpCodeSchema,
	TOtpCodeSchema,
} from "@/utils/validators/otp-code.schema";
import { useState, useEffect } from "react";

export default function ConfirmEmailPage() {
	const location = useLocation();
	const navigate = useNavigate();
	const [response, setResponse] = useState<"valid" | "invalid" | null>(null);

	const [timerActive, setTimerActive] = useState(false);
	const [timerMinutes, setTimerMinutes] = useState(2);
	const [timerSeconds, setTimerSeconds] = useState(0);
	const [canResend, setCanResend] = useState(true);

	const { mutate: confirmCodeMutation, isPending } = useActivateUser();
	const { mutate: resendCodeMutation, isPending: isResending } =
		useRetrievePasswordEmail();
	const email = location.state?.user?.email || "";
	const id = location.state?.user?.id || "";

	const {
		control,
		handleSubmit,
		formState: { errors, isValid },
	} = useForm<TOtpCodeSchema>({
		resolver: zodResolver(otpCodeSchema),
		defaultValues: {
			code: "",
		},
		mode: "onChange",
	});

	useEffect(() => {
		let intervalId: NodeJS.Timeout;

		if (timerActive) {
			intervalId = setInterval(() => {
				if (timerSeconds > 0) {
					setTimerSeconds((seconds) => seconds - 1);
				} else {
					if (timerMinutes === 0) {
						clearInterval(intervalId);
						setTimerActive(false);
						setCanResend(true);
					} else {
						setTimerMinutes((minutes) => minutes - 1);
						setTimerSeconds(59);
					}
				}
			}, 1000);
		}

		return () => {
			if (intervalId) clearInterval(intervalId);
		};
	}, [timerMinutes, timerSeconds, timerActive]);

	const onSubmit = (data: TOtpCodeSchema) => {
		if (!email) {
			notify(
				"Email address is missing. Please go back to the previous page.",
				ResponseStatus.ERROR,
			);
			return;
		}

		confirmCodeMutation(
			{
				user_id: id,
				authToken: data.code,
			},
			{
				onSuccess: () => {
					setResponse("valid");

					notify(
						"Email verification successful!",
						ResponseStatus.SUCCESS,
					);

					// setTimeout(() => {
					// 	navigate("/create-new-password", { state: { email } });
					// }, 1500);

					setTimeout(() => {
						navigate("/onboarding")
					}, 1000)
				},
				onError: (error) => {
					setResponse("invalid");

					const errorMessage =
						error?.message ||
						"Verification code is invalid or has expired.";
					notify(errorMessage, ResponseStatus.ERROR);
				},
			},
		);
	};

	const handleResendCode = () => {
		if (!email) {
			notify(
				"Email address is missing. Please go back to the previous page.",
				ResponseStatus.ERROR,
			);
			return;
		}

		resendCodeMutation(
			{ identifier: email },
			{
				onSuccess: () => {
					notify(
						"Verification code resent to your email",
						ResponseStatus.SUCCESS,
					);

					setTimerMinutes(2);
					setTimerSeconds(0);
					setCanResend(false);
					setTimerActive(true);
				},
				onError: (error) => {
					const errorMessage =
						error?.message ||
						"Failed to resend verification code. Please try again.";
					notify(errorMessage, ResponseStatus.ERROR);
				},
			},
		);
	};

	return (
		<AuthCard
			title="Confirm your email address"
			subtitle={`Input code sent to your email ${email}`}
		>
			<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
				<div className="flex flex-col items-center">
					<Controller
						name="code"
						control={control}
						render={({ field }) => (
							<InputOTP
								pattern={REGEXP_ONLY_DIGITS}
								maxLength={4}
								value={field.value}
								onChange={field.onChange}
								onComplete={() => handleSubmit(onSubmit)()}
								autoFocus
								containerClassName="gap-3"
							>
								<InputOTPGroup>
									<InputOTPSlot
										index={0}
										className="size-16 text-lg"
									/>
								</InputOTPGroup>
								<InputOTPGroup>
									<InputOTPSlot
										index={1}
										className="size-16 text-lg"
									/>
								</InputOTPGroup>
								<InputOTPGroup>
									<InputOTPSlot
										index={2}
										className="size-16 text-lg"
									/>
								</InputOTPGroup>
								<InputOTPGroup>
									<InputOTPSlot
										index={3}
										className="size-16 text-lg"
									/>
								</InputOTPGroup>
							</InputOTP>
						)}
					/>
					{errors.code && (
						<p className="text-sm font-medium text-destructive mt-2">
							{errors.code.message}
						</p>
					)}
				</div>

				{response === "valid" ? (
					<p className="text-green-500 dark:text-green-300 text-center my-4">
						Code Valid!
					</p>
				) : (
					response === "invalid" && (
						<p className="text-red-500 dark:text-red-300 text-center my-4">
							The code you entered is incorrect, please try again.
						</p>
					)
				)}

				{canResend ? (
					<div className="text-center mb-6">
						<p className="text-gray-600 text-sm">
							Didn't receive the code?{" "}
							<button
								type="button"
								onClick={handleResendCode}
								disabled={isResending}
								className="text-amber-500 hover:text-amber-600 font-medium disabled:opacity-70"
							>
								{isResending ? "Sending..." : "Resend Code"}
							</button>
						</p>
					</div>
				) : (
					<p className="text-gray-600 text-sm text-center my-4">
						You will be able to request new OTP in {timerMinutes}:
						{timerSeconds.toString().length < 2
							? `0${timerSeconds}`
							: timerSeconds}
					</p>
				)}

				<Button
					type="submit"
					disabled={!isValid || isPending}
					className="w-full py-6 rounded-full bg-primary hover:bg-primary/90 text-white font-medium"
				>
					{isPending ? "Verifying..." : "Verify Code"}
				</Button>
			</form>
		</AuthCard>
	);
}
