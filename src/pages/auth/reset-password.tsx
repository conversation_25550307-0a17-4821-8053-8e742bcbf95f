"use client";

import { useState } from "react";
import { <PERSON>, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AuthInputField } from "@/components/custom/input/auth-input";
import { AuthCard } from "@/components/custom/card/auth-card";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import cn from "@/utils/class-names";

const resetPasswordSchema = z
	.object({
		newPassword: z
			.string()
			.min(8, { message: "Password must be at least 8 characters" })
			.regex(/[A-Z]/, {
				message: "Password must contain at least one uppercase letter",
			})
			.regex(/[0-9]/, {
				message: "Password must contain at least one number",
			})
			.regex(/[^A-Za-z0-9]/, {
				message: "Password must contain at least one special character",
			}),
		confirmPassword: z.string(),
	})
	.refine((data) => data.newPassword === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"],
	});

type TResetPasswordSchema = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordPage() {
	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<TResetPasswordSchema>({
		mode: "onChange",
		resolver: zodResolver(resetPasswordSchema),
		defaultValues: {
			newPassword: "",
			confirmPassword: "",
		},
	});

	const [showNewPassword, setShowNewPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);

	const onSubmit = async (data: TResetPasswordSchema) => {
		console.log({
			newPassword: data.newPassword,
			confirmPassword: data.confirmPassword,
		});
	};

	return (
		<div className="py-14 w-[40rem]">
			<AuthCard
				title="Reset password!"
				subtitle="Please type something you'll remember."
				titleClassName="text-start mt-12"
				subtitleClassName="text-start"
			>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="space-y-6">
						{/* New Password Input */}
						<AuthInputField
							label="Password"
							type={showNewPassword ? "text" : "password"}
							id="newPassword"
							register={register("newPassword")}
							error={errors.newPassword?.message}
							className={
								errors.newPassword?.message
									? "border-red-500"
									: ""
							}
							labelClassName={
								errors.newPassword?.message
									? "text-red-500"
									: ""
							}
							rightElement={
								showNewPassword ? (
									<EyeOff
										className="h-5 w-5 cursor-pointer"
										onClick={() =>
											setShowNewPassword(false)
										}
									/>
								) : (
									<Eye
										className="h-5 w-5 cursor-pointer"
										onClick={() => setShowNewPassword(true)}
									/>
								)
							}
						/>

						<AuthInputField
							label="Confirm Password"
							type={showConfirmPassword ? "text" : "password"}
							id="confirmPassword"
							register={register("confirmPassword")}
							error={errors.confirmPassword?.message}
							className={
								errors.confirmPassword?.message
									? "border-red-500"
									: ""
							}
							labelClassName={
								errors.confirmPassword?.message
									? "text-red-500"
									: ""
							}
							rightElement={
								showConfirmPassword ? (
									<EyeOff
										className="h-5 w-5 cursor-pointer"
										onClick={() =>
											setShowConfirmPassword(false)
										}
									/>
								) : (
									<Eye
										className="h-5 w-5 cursor-pointer"
										onClick={() =>
											setShowConfirmPassword(true)
										}
									/>
								)
							}
						/>

						<div className="text-center">
							<Button
								type="submit"
								className={cn(
									"w-[80%] text-white font-medium py-8 px-4 rounded-full transition-colors",
								)}
							>
								Continue
							</Button>
						</div>
					</div>
				</form>
			</AuthCard>
		</div>
	);
}
