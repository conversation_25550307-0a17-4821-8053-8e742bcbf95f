// import { useParams, useNavigate } from 'react-router-dom';
// import { SavingsSuccess } from '@/shared/home/<USER>/savings-success';
// import { useSavingGoal } from '@/hooks/api/use-savings';

// export const SavingsSuccessPage = () => {
//     const { goalId } = useParams<{ goalId: string }>();
//     const navigate = useNavigate();
//     const { data: goal } = useSavingGoal(goalId || '');

//     if (!goal) {
//         return (
//             <div className="flex items-center justify-center h-full">
//                 <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
//             </div>
//         );
//     }

//     const handleViewGoal = () => {
//         navigate(`/savings/goal/${goalId}`);
//     };

//     return (
//         <div className="container mx-auto">
//             <SavingsSuccess 
//                 goalName={goal.title}
//                 goalId={goal.id}
//                 onViewGoal={handleViewGoal}
//             />
//         </div>
//     );
// }; 