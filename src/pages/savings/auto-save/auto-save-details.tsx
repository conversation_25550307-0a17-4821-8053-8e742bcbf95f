import React from "react";
import { ChevronLeft} from "lucide-react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { SavingsGoal } from "@/types/fiat-savings";
import { Button } from "@/components/ui/button";
import { AutoSaveCard } from "./auto-save-card";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { AutoSaveForm } from "./auto-save-form";
import TransactionItem from "@/shared/home/<USER>";

const mockActivities = [
	{
		id: 1,
		type: "Fund Savings Goal",
		subtype: "American Dollar Fiat Account",
		date: "10:30 AM - 12/06/2024",
		amount: "USD 200",
		status: "Successful" as "Successful",
		time: "10:30 AM",
		account: "American Dollar Fiat Account",
	},
	{
		id: 2,
		type: "Fund Savings Goal",
		subtype: "American Dollar Fiat Account",
		date: "10:30 AM - 12/05/2024",
		amount: "USD 200",
		status: "failed" as "Failed",
		time: "10:30 AM",
		account: "American Dollar Fiat Account",
	},
	{
		id: 3,
		type: "Fund Savings Goal",
		subtype: "American Dollar Fiat Account",
		date: "10:30 AM - 12/04/2024",
		amount: "USD 200",
		status: "pending" as "Pending",
		time: "10:30 AM",
		account: "American Dollar Fiat Account",
	},
];

const AutoSaveDetails = () => {
	const navigate = useNavigate();
	const { goalId } = useParams<{ goalId: string }>();
	const location = useLocation();
	const passedGoalData = location.state?.goalData as SavingsGoal | undefined;
	const { openDrawer, closeDrawer } = useDrawer();

	
	const goalTitle = passedGoalData?.title || "Savings Goal";

	function deactivateView() {
		return (
			<div>
				<button></button>

				<div>
					<h1>Are you sure you want to deactivate Auto Save?</h1>

					<div>
						<button>ye,deactivate</button>
						<button>Cancel</button>
					</div>
				</div>
			</div>
		);
	}
	const handleEditAutoSave = () => {
		openDrawer({
			view: (
				<AutoSaveForm
					goal={passedGoalData}
					onClose={() => closeDrawer()}
				/>
			), 
			placement: "right",
			customSize: "480px",
		});
	};
	const handleDeactivateAutoSave = () => {
		openDrawer({
			view: (
				<div className="p-6">
					<h1 className="text-xl font-semibold mb-4">
						Are you sure you want to deactivate Auto Save?
					</h1>
					<div className="flex gap-4">
						<Button
							className="bg-primary text-white px-4 py-2 rounded-full"
							onClick={() => {
								closeDrawer();
								
							}}
						>
							Yes, Deactivate
						</Button>
						<Button
							className="bg-gray-300 text-gray-800 px-4 py-2 rounded-full"
							onClick={closeDrawer}
						>
							Cancel
						</Button>
					</div>
				</div>
			),
			placement: "right",
			customSize: "480px",
		});
	};

	return (
		<div className="p-4 md:p-6  min-h-screen">
			{/* Header */}
			<div className="mb-6 flex items-center justify-between ">
				<div className="flex items-center gap-3">
					<button
						onClick={() => navigate(-1)}
						className="p-2 rounded-full bg-orange-100 dark:bg-gray-800 hover:bg-orange-200 dark:hover:bg-gray-700 transition-colors"
					>
						<ChevronLeft className="w-5 h-5 text-primary dark:text-orange-400" />
					</button>
					<h1 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-white">
						{goalTitle}
					</h1>
				</div>
			</div>

			<AutoSaveCard goal={passedGoalData} />

			<div className="flex items-center justify-start gap-5  mt-4">
				<Button
					className="rounded-full w-[180px] p-4 dark:bg-gray-900"
					onClick={handleEditAutoSave}
				>
					Edit
				</Button>
				<Button
					className="rounded-full w-[180px] p-4 bg-gray-900 dark:bg-primary"
					onClick={handleDeactivateAutoSave}
				>
					Deactivate
				</Button>
			</div>

			{/* Recent Activities Section */}
			<div className="">
				<h3 className="text-muted-forground text-lg font-semibold mt-8 mb-4">
					Recent activities
				</h3>
				<div className="space-y-4">
					{mockActivities.map((activity) => (
						<div className="border rounded-lg">
							<TransactionItem
								key={activity.id}
								transaction={activity}
							/>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};
export default AutoSaveDetails;
