import { Button } from "@/components/custom/button";
import { useState } from "react";
import { SavingsGoal } from "@/types/crypto-savings";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { AutoSaveForm } from "./auto-save-form";

interface AutoSaveCardProps {
    goal: SavingsGoal;
}

export const AutoSaveCard: React.FC<AutoSaveCardProps> = ({ goal }) => {
    const [isAutoSaveActive, setIsAutoSaveActive] = useState<boolean>(false);
    const { openDrawer,closeDrawer } = useDrawer();
    
    const handleToggle = () => {
        setIsAutoSaveActive(!isAutoSaveActive);
    };
    const onActivate = ()=>{
        openDrawer({
            view: <AutoSaveForm goal={goal} onClose={closeDrawer}/>,
            placement: "right",
            customSize: "480px"
        });


    }

    
    const progress = ((goal.amountSaved / goal.targetAmount) * 100);
    
    return (
        <div className="bg-green border border-border rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">{goal.title}</h3>
                <div 
                onClick={handleToggle}
                className={`flex items-center cursor-pointer  w-8 rounded-full transition-all p-1 h-4 ${isAutoSaveActive?"bg-green-400":"bg-gray-500"}`}>
                    <div className={`size-3 rounded-full bg-white ${isAutoSaveActive?"translate-x-3":"translate-x-0"}`}>

                    </div>
                </div>
            </div>
            
            <div className="flex justify-between mb-2">
                <div>
                    <p className="text-sm text-gray-500">Amount Saved</p>
                    <p className="font-medium">{goal.currency} {goal.amountSaved}</p>
                </div>
                <div className="text-right">
                    <p className="text-sm text-gray-500">Target Amount</p>
                    <p className="font-medium">{goal.currency} {goal.targetAmount}</p>
                </div>
            </div>
            
            <div className="w-full bg-[#fff1de] h-4 flex justify-start items-center p-1 rounded-full mb-4">
                <div
                    className="bg-primary h-3 w-2  rounded-full"
                    style={{ width: `${Math.min(100, progress) +2 }%` }}
                />
            </div>
            
            {!isAutoSaveActive && (
                <Button 
                    onClick={onActivate}
                    className="w-[250px] mt-2 cursor-pointer bg-primary text-white"
                >
                    Activate Auto Save
                </Button>
            )}
        </div>
    );
};
