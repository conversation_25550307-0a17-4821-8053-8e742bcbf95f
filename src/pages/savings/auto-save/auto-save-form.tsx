import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import { Button } from "@/components/custom/button";
import EnhancedSelect, { IOption } from "@/components/enhanced-select";
import { X } from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { SavingsGoal } from "@/types/fiat-savings";
import { cn } from "@/lib/utils";
import React from "react";
import { useState } from "react";
import { IFiatWallet } from "@/types/fiat-wallets";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { useFundCryptoSavingsAuto } from "@/hooks/api/crypto-savings";
import { AutoSavePayload } from "@/types/fiat-savings";
import { useFundFiatSavingsAuto } from "@/hooks/api/fiat-savings";
import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";

interface AutoSaveFormProps {
	onClose: () => void;
	goal: SavingsGoal;
}
interface AutoSave {
	amount: number;
	frequency: string;
	day: string;
	time: string;
	startDate: string;
	fundingSourceType: string;
	fundingSourceAccountId: string;
}

export const AutoSaveForm = ({ onClose, goal }: AutoSaveFormProps) => {
	const { data: fiatWallets = [] } = useFiatUserWallets();
	const { data: cryptoWallets = [] } = useCryptoUserWallets();
	const fundFiatSavingsAuto = useFundFiatSavingsAuto();
	const fundCryptoSavingsAuto = useFundCryptoSavingsAuto();
	const [success, setIsSuccess] = useState<false | true>(false);

	const {
		control,
		handleSubmit,
		watch,
		setValue,
		reset, // Added reset here
		formState: { errors },
	} = useForm<AutoSave>({
		mode: "onSubmit",
		defaultValues: {
			amount: 0,
			frequency: "",
			day: "",
			time: "",
			startDate: "",
			fundingSourceType: "fiat",
			fundingSourceAccountId: "",
		},
	});

	const selectedFundingSource = watch("fundingSourceType");

	React.useEffect(() => {
		setValue("fundingSourceAccountId", "");
	}, [selectedFundingSource, setValue]);

	React.useEffect(() => {
		reset({
			amount: 0,
			frequency: "",
			day: "",
			time: "",
			startDate: "",
			fundingSourceType: "fiat",
			fundingSourceAccountId: "",
		});
	}, [goal, reset]);

	const handleSumbit = (data: AutoSave) => {
		const payload: AutoSavePayload = {
			user_id: goal.user_id,
			savingsGoalId: goal.id,
			preferredTime: data.time,
			savePreference: data.frequency,
		};

		if (data.fundingSourceType === "fiat") {
			fundFiatSavingsAuto.mutate(payload as any, {
				onSuccess: () => {
					// console.log("Fiat auto save activated successfully");
					// onClose();
					setIsSuccess(true);
				},
				onError: (error) => {
					// Handle error
					console.error("Error activating fiat auto save:", error);
				},
			});
		} else if (data.fundingSourceType === "crypto") {
			fundCryptoSavingsAuto.mutate(payload as any, {
				onSuccess: () => {
					setIsSuccess(true);
					// Handle success
					// console.log("Crypto auto save activated successfully");
					// onClose();
				},
				onError: (error) => {
					// Handle error
					console.error("Error activating crypto auto save:", error);
				},
			});
		}
	};
	const fundingSourceOptions: IOption<string>[] = [
		{ id: "fiat", label: "Fiat Account", value: "fiat" },
		{ id: "crypto", label: "Crypto Wallet", value: "crypto" },
	];
	const daysOfWeek: IOption<string>[] = [
		{ id: "monday", label: "Monday", value: "monday" },
		{ id: "tuesday", label: "Tuesday", value: "tuesday" },
		{ id: "wednesday", label: "Wednesday", value: "wednesday" },
		{ id: "thursday", label: "Thursday", value: "thursday" },
		{ id: "friday", label: "Friday", value: "friday" },
		{ id: "saturday", label: "Saturday", value: "saturday" },
		{ id: "sunday", label: "Sunday", value: "sunday" },
	];

	const frequencyOptions: IOption<string>[] = [
		{ id: "daily", label: "Daily", value: "daily" },
		{ id: "weekly", label: "Weekly", value: "weekly" },
		{ id: "bi-weekly", label: "Bi-weekly", value: "bi-weekly" },
		{ id: "monthly", label: "Monthly", value: "monthly" },
	];

	const fiatAccountOptions: IOption<string>[] = fiatWallets.map(
		(wallet: IFiatWallet) => ({
			id: wallet.id,
			label:
				wallet.name ||
				wallet.currency_name ||
				wallet.currency ||
				"Unnamed Fiat Wallet", 
			value: wallet.id,
		}),
	);

	const cryptoAccountOptions: IOption<string>[] = cryptoWallets.map(
		(wallet: ICryptoWallet) => ({
			id: wallet.id,
			label:
				wallet.coin_name || wallet.currency || "Unnamed Crypto Wallet", // Use available name fields
			value: wallet.id,
		}),
	);

	if (success) {
		return (
			<div className="flex flex-col items-center justify-center h-full space-y-6 py-12 px-6">
				<motion.div
					initial={{ scale: 0 }}
					animate={{ scale: 1 }}
					transition={{ type: "spring", duration: 0.5 }}
					className="size-24 bg-green-600 rounded-full justify-center items-center"
				>
					<CheckCircle className="size-12 text-white" />
				</motion.div>

				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					className="text-center space-y-4"
				>
					<h2>AutoSave activated successfully</h2>
					<p>
						Dear Mary, you have successfully activated your{" "}
						{goal.title} AutoSave
					</p>
				</motion.div>

				<Button onClick={onClose}>Back to autoSave</Button>
			</div>
		);
	}
	return (
		<div className="min-h-screen flex flex-col items-center justify-center relative p-4 md:p-6 dark:bg-background">
			<button
				className="absolute top-4 border border-primary cursor-pointer right-4 p-2 rounded-full bg-background dark:bg-card hover:bg-muted dark:hover:bg-muted-foreground/20 transition-colors"
				onClick={onClose}
			>
				<X className="h-6 w-6 text-primary" />
			</button>

			<div className="w-full text-center max-w-md bg-background dark:bg-card mb-6">
				<h2 className=" text-xl font-bold dark:text-white">
					Activate {goal.title} AutoSave
				</h2>
				<p className="text-lg text-muted-foreground dark:text-gray-400">
					Create a savings goal and build wealth easily
				</p>
			</div>

			<form
				onSubmit={handleSubmit(handleSumbit)}
				className="w-full h-full flex flex-col gap-4 overflow-y-auto pr-2 hide-scrollbar max-w-md"
			>
				<Controller
					name="amount"
					control={control}
					rules={{
						required: "Amount is required",
						validate: {
							positive: (value) =>
								Number(value) > 0 || "Amount must be positive",
						},
					}}
					render={({ field, fieldState: { error } }) => (
						<div>
							<FloatingLabelInput
								label="Enter amount"
								type="number"
								min="1"
								{...field}
								className={cn(
									"w-full focus:outline-none bg-background dark:bg-input px-4 text-base text-foreground dark:text-white h-[48px] rounded-full border dark:border-input",
									error &&
										"border-red-500 dark:border-red-500 focus:ring-red-200 dark:focus:ring-red-500 focus:border-red-500 dark:focus:border-red-500",
								)}
							/>
							{error && (
								<p className="text-xs text-red-500 mt-1 ml-3 dark:text-red-400">
									{error.message}
								</p>
							)}
						</div>
					)}
				/>

				<Controller
					control={control}
					name="frequency"
					rules={{ required: "Please select a frequency" }}
					render={({ field, fieldState: { error } }) => (
						<div>
							<EnhancedSelect
								options={frequencyOptions}
								isSearchable={false}
								placeholder="Select Frequency"
								value={
									field.value
										? frequencyOptions.find(
												(option) =>
													option.value ===
													field.value,
										  )
										: null
								}
								onChange={(option) =>
									field.onChange(option ? option.value : "")
								}
								displayClassName="h-[48px] rounded-full bg-background dark:bg-input border dark:border-input text-foreground dark:text-white"
							/>
							{error && (
								<p className="text-xs text-red-500 mt-1 ml-3 dark:text-red-400">
									{error.message}
								</p>
							)}
						</div>
					)}
				/>
				<div className="flex gap-3">
					<Controller
						name="day"
						control={control}
						rules={{ required: "Please select a day" }}
						render={({ field, fieldState: { error } }) => (
							<div className="flex-1">
								<EnhancedSelect
									options={daysOfWeek}
									isSearchable={false}
									placeholder="Select Day"
									value={
										field.value
											? daysOfWeek.find(
													(option) =>
														option.value ===
														field.value,
											  )
											: null
									}
									onChange={(option) =>
										field.onChange(
											option ? option.value : "",
										)
									}
									displayClassName="h-[48px] rounded-full bg-background dark:bg-input border dark:border-input text-foreground dark:text-white"
								/>
								{error && (
									<p className="text-xs text-red-500 mt-1 ml-3 dark:text-red-400">
										{error.message}
									</p>
								)}
							</div>
						)}
					/>
					<Controller
						name="time"
						control={control}
						rules={{ required: "Please select a time" }}
						render={({ field, fieldState: { error } }) => (
							<div className="flex-1">
								<DatePicker
									selected={
										field.value
											? new Date(
													`1970-01-01T${field.value}:00`,
											  )
											: null
									} // Construct a valid date for DatePicker
									onChange={(date: Date | null) => {
										if (date) {
											const hours = date
												.getHours()
												.toString()
												.padStart(2, "0");
											const minutes = date
												.getMinutes()
												.toString()
												.padStart(2, "0");
											field.onChange(
												`${hours}:${minutes}`,
											);
										} else {
											field.onChange("");
										}
									}}
									showTimeSelect
									showTimeSelectOnly
									timeIntervals={15}
									timeCaption="Time"
									dateFormat="h:mm aa"
									placeholderText="Select Time"
									className={cn(
										"w-full h-[48px] rounded-full border bg-background dark:bg-input px-4 text-base text-foreground dark:text-white placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring dark:border-input",
										error &&
											"border-red-500 dark:border-red-500 focus:ring-red-200 dark:focus:ring-red-500 focus:border-red-500 dark:focus:border-red-500",
									)}
									wrapperClassName="w-full"
								/>
								{error && (
									<p className="text-xs text-red-500 mt-1 ml-3 dark:text-red-400">
										{error.message}
									</p>
								)}
							</div>
						)}
					/>
				</div>

				<Controller
					control={control}
					name="startDate"
					rules={{ required: "Start date is required" }}
					render={({ field, fieldState: { error } }) => (
						<div>
							<DatePicker
								selected={
									field.value ? new Date(field.value) : null
								}
								onChange={(date: Date | null) =>
									field.onChange(
										date
											? date.toISOString().split("T")[0]
											: "",
									)
								}
								dateFormat="MM/dd/yyyy"
								placeholderText="Select Start Date"
								className={cn(
									"w-full h-[48px] rounded-full border bg-background dark:bg-input px-4 text-base text-foreground dark:text-white placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring dark:border-input",
									error &&
										"border-red-500 dark:border-red-500 focus:ring-red-200 dark:focus:ring-red-500 focus:border-red-500 dark:focus:border-red-500",
								)}
								wrapperClassName="w-full"
							/>
							{error && (
								<p className="text-xs text-red-500 mt-1 ml-3 dark:text-red-400">
									{error.message}
								</p>
							)}
						</div>
					)}
				/>

				<Controller
					name="fundingSourceType"
					control={control}
					rules={{ required: "Please select a funding source type" }}
					render={({ field, fieldState: { error } }) => (
						<div>
							<EnhancedSelect
								options={fundingSourceOptions}
								isSearchable={false}
								placeholder="Select Funding Source Type"
								value={
									field.value
										? fundingSourceOptions.find(
												(opt) =>
													opt.value === field.value,
										  )
										: null
								}
								onChange={(option) =>
									field.onChange(option ? option.value : "")
								}
								displayClassName="h-[48px] rounded-full bg-background dark:bg-input border dark:border-input text-foreground dark:text-white"
							/>
							{error && (
								<p className="text-xs text-red-500 mt-1 ml-3 dark:text-red-400">
									{error.message}
								</p>
							)}
						</div>
					)}
				/>

				{selectedFundingSource && (
					<Controller
						name="fundingSourceAccountId"
						control={control}
						rules={{ required: "Please select an account" }}
						render={({ field, fieldState: { error } }) => (
							<div>
								<EnhancedSelect
									options={
										selectedFundingSource === "fiat"
											? fiatAccountOptions
											: cryptoAccountOptions
									}
									isSearchable={true}
									placeholder={`Select ${
										selectedFundingSource === "fiat"
											? "Fiat Account"
											: "Crypto Wallet"
									}`}
									value={
										field.value
											? (selectedFundingSource === "fiat"
													? fiatAccountOptions
													: cryptoAccountOptions
											  ).find(
													(opt) =>
														opt.value ===
														field.value,
											  )
											: null
									}
									onChange={(option) =>
										field.onChange(
											option ? option.value : "",
										)
									}
									displayClassName="h-[48px] rounded-full bg-background dark:bg-input border dark:border-input text-foreground dark:text-white"
								/>
								{error && (
									<p className="text-xs text-red-500 mt-1 ml-3 dark:text-red-400">
										{error.message}
									</p>
								)}
							</div>
						)}
					/>
				)}
				<Button
					type="submit"
					className="w-full cursor-pointer rounded-full bg-primary hover:bg-primary/90 text-primary-foreground h-[48px] mt-6 dark:bg-primary dark:hover:bg-primary/80 dark:text-white"
					disabled={Object.keys(errors).length > 0}
				>
					Activate Auto Savings
				</Button>
			</form>
		</div>
	);
};
