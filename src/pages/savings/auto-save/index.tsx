import { useEffect } from "react";
import { AutoSaveCard } from "./auto-save-card";
import { Button } from "@/components/custom/button";
import { ChevronLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAllGoalsStore } from "@/store/useGoalStore";
import{Link} from "react-router-dom"


const AutoSave  = () => {
	// const { openDrawer } = useDrawer();
	const navigate = useNavigate();
	const  {goals,fetchAllGoals,isLoading}  = useAllGoalsStore();

	useEffect(() => {
		fetchAllGoals();
	}, [fetchAllGoals]);
	
	// const handleActivateAutoSave = () => {
	// 	openDrawer({
	// 		view: <AutoSaveForm  />,
	// 		placement: "right",
	// 		customSize: "480px"
	// 	});
	// };
	
	return (
		<div className="p-6">
			<div className="mb-8">
				<div className="flex items-center gap-4 mb-4">
					<button
						onClick={() => navigate(-1)}
						className="p-2 rounded-full bg-[#fff1de] cursor-pointer hover:bg-[#ffe0b3] transition-colors"
					>
						<ChevronLeft className="w-5 h-5 text-primary" />
					</button>
					<h1 className="text-2xl font-semibold">Auto Save</h1>
				</div>
				<p className="text-gray-600">
					Save automatically without the stress of having to transfer
					into your savings
				</p>
			</div>

			{isLoading ? (
				<div className="flex justify-center p-8">
					<div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
				</div>
			) : (
				<div className="space-y-4">
					{goals?.length > 0 ? (
						goals.map((goal) => (
							<div>
								<Link
									key={goal.id}
									state={{ goalData: goal }}
									to={`/details/${goal.id}`}
								>
									<AutoSaveCard goal={goal} />
								</Link>
							</div>
						))
					) : (
						<div className="text-center p-8 bg-card border shadow-lg rounded-lg">
							<p className="text-muted-forground mb-4">
								You don't have any savings goals set up yet.
							</p>
							<Button className="bg-primary text-white">
								Create a Savings Goal
							</Button>
						</div>
					)}
				</div>
			)}
		</div>
	);
};


export default AutoSave;