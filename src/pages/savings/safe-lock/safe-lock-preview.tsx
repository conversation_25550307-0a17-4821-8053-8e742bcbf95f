import { Button } from "@/components/ui/button";
import useSafeLockStore from "@/store/safe-lock-store";


export const SafeLockPreview = () => {
	const { selectedDuration, formData, setCurrentStep } = useSafeLockStore();

	
	
	const handleConfirm = () => {
		// Here you would typically make an API call to create the safe lock
		setCurrentStep("success", true);
	};

	return (
		<div className="p-6 space-y-6">
			<div className="text-center space-y-2">
				<p className="text-lg font-medium">Preview Safe Lock</p>
				<p className="text-sm text-muted-foreground">
					Review your safe lock for {selectedDuration.label} details
					before confirming
				</p>
			</div>

			<div className="space-y-4">
				<div className="bg-muted/50 rounded-lg p-4 space-y-3">
					<div className="flex justify-between items-center">
						<span className="text-sm text-muted-foreground">
							title
						</span>
						<span className="font-semibold text-lg">
							{formData?.title}
						</span>
					</div>
					<div className="flex justify-between items-center">
						<span className="text-sm text-muted-foreground">
							currency
						</span>
						<span className="font-semibold text-lg">
							{formData?.currency}
						</span>
					</div>
					<div className="flex justify-between items-center">
						<span className="text-sm text-muted-foreground">
							Amount to Lock away
						</span>
						<span className="font-semibold text-lg">
							${formData?.targetAmount}
						</span>
					</div>

					<div className="flex justify-between items-center">
						<span className="text-sm text-muted-foreground">
							Duration interest
						</span>
						<span className="font-medium">
							{selectedDuration?.label} days
						</span>
					</div>

					<div className="flex justify-between items-center">
						<span className="text-sm text-muted-foreground">
							Start Date
						</span>
						<span className="font-medium text-foreground">
							{formData?.startDate
								? new Date(formData.startDate).toLocaleDateString("en-GB", {
										day: "2-digit",
										month: "2-digit",
										year: "numeric",
									}).replace(/-/g, "/")
								: ""}
						</span>
					</div>

					<div className="flex justify-between items-center">
						<span className="text-sm text-muted-foreground">
							End date
						</span>
						<span className="font-semibold text-foreground">
							{formData?.endDate? new Date(formData.endDate).toLocaleDateString("en-GB", {
								day: "2-digit",
								month: "2-digit",
								year: "numeric",
							}).replace(/-/g, "/") : ""}
						</span>
					</div>

					<div className="flex justify-between items-center">
						<span className="text-sm text-muted-foreground">
							Funding Source
						</span>
						<span className="font-medium text-foreground">
							{formData?.funding_source_type} wallet
						</span>
					</div>
				</div>

				<div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
					<p className="text-sm text-amber-800">
						<strong>Important:</strong> Once confirmed, your funds
						will be locked for the selected duration and cannot be
						withdrawn early.
					</p>
				</div>
			</div>

			<div className="space-y-3">
				<Button onClick={handleConfirm} className="w-full rounded-full">
					Confirm Safe Lock
				</Button>

				<Button
					variant="outline"
					onClick={() => setCurrentStep("form", false)}
					className="w-full rounded-full cursor-pointer"
				>
					Back to Edit
				</Button>
			</div>
		</div>
	);
};
