import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import useSafeLockStore from "@/store/safe-lock-store";
import { DurationOptions } from "./duration";
import { SafeLockForm } from "./safe-lock-form";
import { SafeLockPreview } from "./safe-lock-preview";
import { SafeLockSuccess } from "./safe-lock-success";
import { ChevronLeft } from "lucide-react";

export const SafeLockFlow = () => {
	const { closeDrawer } = useDrawer();

	const [direction, setDirection] = useState<"foreward" | "backward">(
		"foreward",
	);

	const { currentStep, setCurrentStep } = useSafeLockStore();

	const handleClose = () => {
		closeDrawer();
	};
	const handleStepChange = (
		step: typeof currentStep,
		dir: "foreward" | "backward" = "foreward",
	) => {
		setDirection(dir);
		setCurrentStep(step, dir === "foreward");
	};
	const handleBack = () => {
		switch (currentStep) {
			case "form":
				handleStepChange("duration", "backward");
				break;
			case "preview":
				handleStepChange("form", "backward");
				break;
		}
	};

	const showBackButton = ["form", "preview"].includes(currentStep);

	const slideVariants = {
		enter: (direction: string) => ({
			x: direction === "foreward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "foreward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};
	return (
		<div className="flex flex-col h-full overflow-y-auto">
			<div className="flex justify-betweeen mt-10 relative px-6">
				<Button
					onClick={handleBack}
					className={`${
						showBackButton ? "flex" : "hidden"
					} rounded-full bg-primary/10 p-1 hover:bg-primary/20`}
				>
					<ChevronLeft className="text-primary size-6" />
				</Button>

				<Button
					size="icon"
					onClick={handleClose}
					variant="ghost"
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>
				{/* {(getStepTitle() || showBackButton) && (
					<div className="flex items-center justify-center relative">
						{showBackButton && (
							<span
								onClick={handleBack}
								className="absolute left-0 bg-primary-light text-primary rounded-full p-2 cursor-pointer"
							>
								back
							</span>
						)}
						{getStepTitle() && (
							<h2 className="text-2xl font-semibold w-full text-center">
								{getStepTitle()}
							</h2>
						)}
					</div>
				)} */}
			</div>

			<div className="p-0 h-full overflow-hidden relative flex-grow">
				<AnimatePresence mode="wait" custom={direction} initial={false}>
					{currentStep === "duration" && (
						<motion.div
							key="duration"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
						>
							<DurationOptions />
						</motion.div>
					)}

					{currentStep === "form" && (
						<motion.div
							key="form"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
						>
							<SafeLockForm />
						</motion.div>
					)}

					{currentStep === "preview" && (
						<motion.div
							key="preview"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
						>
							<SafeLockPreview />
						</motion.div>
					)}

					{currentStep === "success" && (
						<motion.div
							key="success"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
						>
							<SafeLockSuccess />
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
};
