import { ChevronLeft, Calendar, DollarSign, TrendingUp, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useDrawer } from "@/components/drawer-view/use-drawer";

// Define the SafeLock interface
interface SafeLock {
  id: string;
  title: string;
  targetAmount: number;
  savedAmount: number;
  daysGone?: number;
  totalDays?: number;
  interestRate: number;
  nextPayment?: string;
  completedDate?: string;
  totalEarned?: number;
}

export const SafeLockDetails = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const passedData = location.state?.safeLockData as SafeLock;

  // Fallback mock data if no data is passed (for direct URL access)
  const mockSafeLockData: SafeLock = {
    id: id || "1",
    title: "Emergency Fund",
    targetAmount: 5000,
    savedAmount: 3500,
    daysGone: 45,
    totalDays: 90,
    interestRate: 15,
    nextPayment: "2025-06-15",
  };

  const safeLockData = passedData || mockSafeLockData;
  const progress = (safeLockData.savedAmount / safeLockData.targetAmount) * 100;
  const daysLeft = safeLockData.totalDays && safeLockData.daysGone 
    ? safeLockData.totalDays - safeLockData.daysGone 
    : 0;

  return (
    <div className="text-foreground min-h-screen">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button 
          onClick={() => navigate(-1)}
          variant="ghost"
          size="sm"
          className="p-3 bg-[#fff1de] hover:bg-[#fff1de]/80 rounded-full"
        >
          <ChevronLeft className="size-5 text-primary" />
        </Button>
        <div>
          <h1 className="text-xl font-semibold">{safeLockData.title}</h1>
          <p className="text-sm text-muted-foreground">Safe Lock Details</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Progress Card */}
        <div className="bg-card border rounded-lg p-6 shadow-sm">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h2 className="text-2xl font-bold">
                ${safeLockData.savedAmount.toLocaleString()}
              </h2>
              <p className="text-muted-foreground">
                of ${safeLockData.targetAmount.toLocaleString()} target
              </p>
            </div>
            <div className="text-right">
              <span className="text-2xl font-bold text-primary">{Math.round(progress)}%</span>
              <p className="text-sm text-muted-foreground">Progress</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-[#fff1de] h-4 p-1 flex items-center rounded-full mb-4">
            <div
              className="bg-primary h-3 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>

          {/* Days Info */}
          {safeLockData.daysGone && safeLockData.totalDays && (
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">
                {safeLockData.daysGone} days gone
              </span>
              <span className="font-medium">
                {daysLeft} days left
              </span>
            </div>
          )}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-card border rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-full">
                <TrendingUp className="size-4 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Interest Rate</p>
                <p className="font-semibold">{safeLockData.interestRate}% p.a.</p>
              </div>
            </div>
          </div>

          <div className="bg-card border rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-full">
                <DollarSign className="size-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Projected Earnings</p>
                <p className="font-semibold text-green-600">
                  +${Math.round(safeLockData.savedAmount * (safeLockData.interestRate / 100)).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Next Payment Info */}
        {safeLockData.nextPayment && (
          <div className="bg-card border rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <Calendar className="size-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Next Payment Due</p>
                <p className="font-semibold">
                  {new Date(safeLockData.nextPayment).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button className="w-full bg-primary text-white rounded-full py-3">
            Add More Funds
          </Button>
          <Button variant="outline" className="w-full rounded-full py-3">
            View Transaction History
          </Button>
        </div>

        {/* Transaction History Preview */}
        <div className="bg-card border rounded-lg p-4">
          <h3 className="font-semibold mb-4">Recent Transactions</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <div>
                <p className="font-medium">Initial Deposit</p>
                <p className="text-sm text-muted-foreground">March 1, 2025</p>
              </div>
              <span className="font-semibold text-green-600">+$1,000</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <div>
                <p className="font-medium">Monthly Contribution</p>
                <p className="text-sm text-muted-foreground">April 1, 2025</p>
              </div>
              <span className="font-semibold text-green-600">+$500</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <div>
                <p className="font-medium">Monthly Contribution</p>
                <p className="text-sm text-muted-foreground">May 1, 2025</p>
              </div>
              <span className="font-semibold text-green-600">+$500</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};