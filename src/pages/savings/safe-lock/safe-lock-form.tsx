import { useF<PERSON>, Controller, FieldErrors } from "react-hook-form";
import { But<PERSON> } from "@/components/custom/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useEffect } from "react";
import { formatAmount } from "@/utils/format-amount";
import { cn } from "@/lib/utils";
import EnhancedSelect, { IOption } from "@/components/enhanced-select";
import useSafeLockStore, { SafeLockFormData } from "@/store/safe-lock-store";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";

export const SafeLockForm = () => {
	const { formData, setFormData, setCurrentStep } = useSafeLockStore();
	const { data: fiatWallets = [] } = useFiatUserWallets();
	const { data: cryptoWallets = [] } = useCryptoUserWallets();

	const { control, handleSubmit, watch, setValue, formState } =
		useForm<SafeLockFormData>({
			defaultValues: {
				title: formData?.title || "",
				startDate: formData?.startDate || "",
				endDate: formData?.endDate || "",
				currency: formData?.currency || "",
				targetAmount: formData?.targetAmount || 0,
				wallet_id: formData?.wallet_id || "",
				funding_source_type: formData?.funding_source_type || "",
			},
			mode: "onSubmit",
			reValidateMode: "onChange",
		});

	const rawFundingSource = watch("funding_source_type");
	const selectedFundingSource: "fiat" | "crypto" | undefined =
		typeof rawFundingSource === "string" &&
		(rawFundingSource === "fiat" || rawFundingSource === "crypto")
			? rawFundingSource
			: undefined;

	useEffect(() => {
		setValue("wallet_id", "");
	}, [selectedFundingSource, setValue]);

	const onSubmit = (data: SafeLockFormData) => {
		const payloadForApi = {
			...data,
			targetAmount: Number(data.targetAmount),
		};

		// Store form data and proceed to preview
		setFormData(payloadForApi);
		setCurrentStep("preview");
		notify("Safe Lock form completed successfully", ResponseStatus.SUCCESS);
	};

	const onInvalid = (errors: FieldErrors<SafeLockFormData>) => {
		console.error("Form validation errors:", errors);
		notify(
			"Please fill all required fields correctly",
			ResponseStatus.ERROR,
		);
	};

	const fundingSourceOptionsOriginal: IOption<string>[] = [
		{ id: "fiat", label: "Fiat Account", value: "fiat" },
		{ id: "crypto", label: "Crypto Wallet", value: "crypto" },
	];

	const enhancedFundingSourceOptions: IOption<string>[] =
		fundingSourceOptionsOriginal.map((option) => ({
			id: option.id,
			label: String(option.label),
			value: option.value,
		}));

	// Define wallet types for better type safety
	type FiatWallet = typeof fiatWallets extends (infer U)[] ? U : never;
	type CryptoWallet = typeof cryptoWallets extends (infer U)[] ? U : never;
	type Wallet = FiatWallet | CryptoWallet;

	const fiatAccountOptions: IOption<FiatWallet>[] = fiatWallets
		.filter(Boolean)
		.map((wallet) => ({
			id: `fiat-${wallet.id}`,
			value: wallet.id,
			label: wallet.currency_name,
			icon: wallet.image,
			raw: wallet,
		}));

	const cryptoAccountOptions: IOption<CryptoWallet>[] = cryptoWallets
		.filter(Boolean)
		.map((wallet) => ({
			id: `crypto-${wallet.id}`,
			value: wallet.id,
			label: wallet.currency + " Wallet",
			icon: wallet.image,
			raw: wallet,
		}));

	const accountOptions: IOption<Wallet>[] =
		selectedFundingSource === "fiat"
			? (fiatAccountOptions as IOption<Wallet>[])
			: selectedFundingSource === "crypto"
			? (cryptoAccountOptions as IOption<Wallet>[])
			: [];

	const fiatCurrencyOptions = fiatWallets.map((wallet) => ({
		id: wallet.currency,
		label: wallet.currency,
		value: wallet.currency,
		icon: wallet.image || undefined,
	}));
	const cryptoCurrencyOptions = cryptoWallets.map((wallet) => ({
		id: wallet.currency,
		label: wallet.currency,
		value: wallet.currency,
		icon: wallet.image || undefined,
	}));
	const dynamicCurrencyOptions =
		selectedFundingSource === "fiat"
			? fiatCurrencyOptions
			: selectedFundingSource === "crypto"
			? cryptoCurrencyOptions
			: [];

	return (
		<div className="w-full max-w-md bg-background dark:bg-card rounded-2xl px-8 py-8 flex flex-col items-center max-h-screen overflow-hidden">
			<h2 className="text-2xl font-bold text-center mb-1 text-foreground">
				Create Safe Lock
			</h2>
			<p className="text-muted-foreground text-center mb-6 text-base">
				Lock your funds safely for a specific period to earn better
				returns.
			</p>
			<form
				onSubmit={handleSubmit(onSubmit, onInvalid)}
				className="w-full flex flex-col gap-4 overflow-y-auto flex-1 pr-2 hide-scrollbar"
			>
				<Controller
					name="title"
					control={control}
					rules={{
						required: "Please enter a title for your Safe Lock",
						minLength: {
							value: 3,
							message: "Title must be at least 3 characters long",
						},
					}}
					render={({ field, fieldState: { error } }) => (
						<FloatingLabelInput
							label="Title of Safe Lock"
							placeholder=""
							{...field}
							error={error?.message}
							className="w-full h-[48px] rounded-full bg-background dark:bg-input border border-border px-4 text-base text-foreground"
						/>
					)}
				/>

				<Controller
					name="startDate"
					control={control}
					rules={{ required: "Start date is required" }}
					render={({ field }) => (
						<DatePicker
							selected={
								field.value ? new Date(field.value) : null
							}
							onChange={(date: Date) =>
								field.onChange(date?.toISOString())
							}
							minDate={new Date()}
							placeholderText="Select start date"
							className="w-full h-[48px] rounded-full border border-border px-4 bg-background dark:bg-input text-base text-foreground"
							dateFormat="yyyy-MM-dd"
						/>
					)}
				/>

				<Controller
					name="endDate"
					control={control}
					rules={{ required: "End date is required" }}
					render={({ field }) => (
						<DatePicker
							selected={
								field.value ? new Date(field.value) : null
							}
							onChange={(date: Date) =>
								field.onChange(date?.toISOString())
							}
							minDate={
								watch("startDate")
									? new Date(watch("startDate"))
									: new Date()
							}
							placeholderText="Select end date"
							className="w-full h-[48px] rounded-full border border-border px-4 bg-background dark:bg-input text-base text-foreground"
							dateFormat="yyyy-MM-dd"
						/>
					)}
				/>

				<Controller
					name="currency"
					control={control}
					rules={{ required: "Currency is required" }}
					render={({ field }) => (
						<EnhancedSelect
							{...field}
							value={
								dynamicCurrencyOptions.find(
									(option) => option.value === field.value,
								) || null
							}
							onChange={(selectedOption) =>
								field.onChange(selectedOption?.value)
							}
							options={dynamicCurrencyOptions}
							placeholder="Select Currency"
							className="w-full h-full rounded-full border border-border px-4 bg-background dark:bg-input text-base text-foreground"
						/>
					)}
				/>

				<Controller
					name="targetAmount"
					control={control}
					rules={{
						required: "Amount is required",
						validate: {
							positive: (value) =>
								Number(value) > 0 || "Amount must be positive",
						},
					}}
					render={({ field, fieldState: { error } }) => (
						<FloatingLabelInput
							label="Enter Target Amount"
							type="number"
							min="1"
							step="0.01"
							inputMode="decimal"
							placeholder=" "
							{...field}
							className={cn(
								"border border-border focus:outline-none w-full h-[48px] rounded-full bg-background dark:bg-input px-4 text-base text-foreground",
								error &&
									"border-red-500 focus:ring-red-200 focus:border-red-500",
							)}
							error={error?.message}
						/>
					)}
				/>

				<Controller
					name="funding_source_type"
					control={control}
					rules={{ required: "Please select a funding source" }}
					render={({ field, fieldState: { error } }) => (
						<div>
							<EnhancedSelect
								options={enhancedFundingSourceOptions}
								value={
									enhancedFundingSourceOptions.find(
										(option) =>
											option.value === field.value,
									) || null
								}
								onChange={(selectedOption) =>
									field.onChange(
										selectedOption
											? selectedOption.value
											: "",
									)
								}
								placeholder="Choose Funding Source"
								className="w-full"
								displayClassName="h-[48px] rounded-full bg-background dark:bg-input text-base text-foreground border border-border"
								isSearchable={false}
								isClearable={false}
							/>
							{error && (
								<p className="text-sm text-red-500 mt-1 pl-4">
									{error.message}
								</p>
							)}
						</div>
					)}
				/>

				{selectedFundingSource && (
					<Controller
						name="wallet_id"
						control={control}
						rules={{ required: "Please select an account" }}
						render={({ field }) => {
							if (
								accountOptions.length === 0 &&
								selectedFundingSource
							) {
								return (
									<p className="text-sm text-muted-foreground mt-1 pl-4">
										No{" "}
										{selectedFundingSource === "fiat"
											? "fiat accounts"
											: "crypto wallets"}{" "}
										available for the selected funding
										source.
									</p>
								);
							}
							return (
								<EnhancedSelect
									options={accountOptions}
									value={
										accountOptions.find(
											(option) =>
												option.value === field.value,
										) || null
									}
									onChange={(option) =>
										field.onChange(
											option ? option.value : "",
										)
									}
									placeholder={
										selectedFundingSource === "fiat"
											? "Choose Fiat Account"
											: "Choose Crypto Wallet"
									}
									className="w-full"
									displayClassName="h-[48px] rounded-full border border-border bg-background dark:bg-input text-base text-foreground"
									isSearchable={false}
									isClearable={false}
									renderSelected={(option) => (
										<div className="flex items-center gap-2">
											{option.icon && (
												<img
													src={String(option.icon)}
													alt={String(option.label)}
													className="w-6 h-6 rounded-full"
												/>
											)}
											<span>{option.label}</span>
										</div>
									)}
									renderOption={(option) => (
										<div className="flex items-center gap-2 w-full">
											{option.icon && (
												<img
													src={String(option.icon)}
													alt={String(option.label)}
													className="w-6 h-6 rounded-full"
												/>
											)}
											<span>{option.label}</span>
											{option.raw &&
												"available_balance" in
													option.raw && (
													<span className="ml-auto text-base font-medium text-foreground">
														{formatAmount(
															option.raw
																.available_balance,
															option.raw.currency,
														)}
													</span>
												)}
										</div>
									)}
								/>
							);
						}}
					/>
				)}

				<Button
					type="submit"
					disabled={!formState.isValid}
					className="w-full h-[60px] p-3 mt-2 rounded-full bg-primary text-primary-foreground font-semibold text-base shadow-sm hover:bg-primary/90 transition-colors"
				>
					Continue to Preview
				</Button>
			</form>
		</div>
	);
};
