import { IOption } from "@/types/general";
import { Button } from "@/components/ui/button";
import useSafeLockStore from "@/store/safe-lock-store";
import { useState } from "react";

const durations: IOption<string>[] = [
	{ id: "1", label: "30", value: "30 days (up to 10% per annum)" },
	{ id: "2", label: "45", value: "45 days (up to 11% per annum)" },
	{ id: "3", label: "60", value: "60 days (up to 12% per annum)" },
	{ id: "4", label: "90", value: "90 days (up to 13% per annum)" },
	{ id: "5", label: "120", value: "120 days (up to 14% per annum)" },
	{ id: "6", label: "180", value: "180 days (up to 15% per annum)" },
	{ id: "7", label: "365", value: "365 days (up to 17% per annum)" },
];

export const DurationOptions = () => {
	const { selectedDuration, setSelectedDuration, setCurrentStep } =
		useSafeLockStore();
	const [localSelection, setLocalSelection] = useState(
		selectedDuration,
	);

	const handleDurationSelect = (duration: IOption<string>) => {
		setLocalSelection(duration);
		setSelectedDuration(duration);
	};

	const handleContinue = () => {
		if (localSelection) {
			setCurrentStep("form", true);
		}
	};

	return (
		<div className="p-6 space-y-6">
			<div className="text-center space-y-2">
				<p className="text-lg font-medium">Choose Duration</p>
				<p className="text-sm text-muted-foreground">
					Choose the duration of the time you wish to lock your fund
					for
				</p>
			</div>

			<div className="grid grid-cols-1 gap-3 overflow-y-auto max-h-88">
				{durations.map((duration) => (
					<div
						key={duration.id}
						onClick={() => handleDurationSelect(duration)}
						className={`
							p-4 rounded-lg border cursor-pointer transition-all
							${
								localSelection === duration
									? "border-primary bg-primary/5"
									: "border-border hover:border-primary/50"
							}
						`}
					>
						<div className="flex justify-between items-center">
							<span className="font-medium">
								{duration.label} days
							</span>
							<span className="text-sm text-muted-foreground">
								{duration.value.split("(")[1]?.replace(")", "")}
							</span>
						</div>
					</div>
				))}
			</div>

			{localSelection && (
				<Button onClick={handleContinue} className="w-full rounded-full cursor-pointer mt-6">
					Continue
				</Button>
			)}
		</div>
	);
};

