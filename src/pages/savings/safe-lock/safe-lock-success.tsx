import { Button } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import useSafeLockStore from "@/store/safe-lock-store";
import { CheckCircle } from "lucide-react";

export const SafeLockSuccess = () => {
	const { closeDrawer } = useDrawer();
	const {  selectedDuration, reset } = useSafeLockStore();

	const handleClose = () => {
		reset();
		closeDrawer();
	};

	return (
		<div className="p-6 space-y-6 text-center">
			<div className="flex justify-center">
				<CheckCircle className="size-16 text-green-500" />
			</div>

			<div className="space-y-2">
				<h2 className="text-2xl font-bold text-green-600">
					Safe Lock Created!
				</h2>
				<p className="text-muted-foreground">
					Your funds have been successfully locked for{" "}
					{selectedDuration.label} days
				</p>
			</div>

		

			<div className="text-sm text-muted-foreground">
				You can view your safe lock details in your savings dashboard.
			</div>

			<Button onClick={handleClose} className="w-full">
				View Safe Lock
			</Button>
		</div>
	);
};
