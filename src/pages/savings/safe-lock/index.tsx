import { ChevronLeft } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { SafeLockFlow } from "./safe-lock-flow";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";

type TabType = "ongoing" | "paidBack";

const SafeLock = () => {
	const { openDrawer } = useDrawer();
	const [showBalance, setShowBalance] = useState(false);
	const [activeTab, setActiveTab] = useState<TabType>("ongoing");
	const navigate = useNavigate();

	const handleCreateSafeLock = () => {
		openDrawer({
			view: <SafeLockFlow />,
			customSize: "440px",
		});
	};

	// Mock data for demonstration
	const mockOngoingSafeLocks = [
		{
			id: "1",
			title: "Emergency Fund",
			targetAmount: 5000,
			savedAmount: 3500,
			daysGone: 45,
			totalDays: 90,
			interestRate: 15,
			nextPayment: "2025-06-15",
		},
		{
			id: "2",
			title: "Vacation Fund",
			targetAmount: 2000,
			savedAmount: 800,
			daysGone: 20,
			totalDays: 60,
			interestRate: 12,
			nextPayment: "2025-06-10",
		},
	];

	const mockCompletedSafeLocks = [
		{
			id: "3",
			title: "Car Purchase Fund",
			targetAmount: 8000,
			savedAmount: 8000,
			interestRate: 17,
			completedDate: "2025-01-15",
			totalEarned: 8680,
		},
	];

	const totalBalance =
		mockOngoingSafeLocks.reduce((sum, lock) => sum + lock.savedAmount, 0) +
		mockCompletedSafeLocks.reduce((sum, lock) => sum + lock.totalEarned, 0);

	return (
		<div className="text-foreground ">
			<div className="flex items-center gap-4 mb-4">
				<button
				onClick={()=>navigate(-1)}
				className="p-3 cursor-pointer bg-[#fff1de] transition-all hover:bg-[#fff1de]/80 rounded-full">
					<ChevronLeft className="size-5 text-primary font-bold" />
				</button>
				<h1 className="text-xl font-semibold">Safe Lock</h1>
			</div>
			<p>Lock cash for a certain period of time and get 17% per annum</p>

			<div className="rounded-lg w-md bg-card shadow-lg space-y-3 p-6 border mt-6">
				<div className="bg-[#fff1de] flex justify-center items-center w-[200px] rounded-full p-3">
					<p className="text-primary">10% - 17% per annum</p>
				</div>
				<div>
					<p className="text-muted-foreground">
						Total safe lock balance
					</p>

					<p className="text-3xl font-bold flex justify-between items-center gap-2">
						{showBalance
							? `$${totalBalance.toLocaleString()}`
							: "***"}
						<button
							type="button"
							onClick={() => setShowBalance((prev) => !prev)}
							className="ml-2 p-1 rounded cursor-pointer  transition"
							aria-label={
								showBalance ? "Hide balance" : "Show balance"
							}
						>
							{showBalance ? (
								<EyeOff className="size-5" />
							) : (
								<Eye className="size-5" />
							)}
						</button>
					</p>

					{showBalance && (
						<div className="flex gap-4 mt-3 text-sm text-muted-foreground">
							<span>{mockOngoingSafeLocks.length} Active</span>
							<span>
								{mockCompletedSafeLocks.length} Completed
							</span>
							<span>
								+$
								{mockCompletedSafeLocks
									.reduce(
										(sum, lock) =>
											sum +
											(lock.totalEarned -
												lock.savedAmount),
										0,
									)
									.toLocaleString()}{" "}
								Interest Earned
							</span>
						</div>
					)}
				</div>
				<Button
					onClick={handleCreateSafeLock}
					className="bg-primary text-white rounded-full px-8 py-3 md:text-lg shadow-md mt-4"
				>
					Create a Safe Lock
				</Button>
			</div>

			{/* Tabs section */}
			<div className="flex items-center justify-center text-primary gap-8 mt-20">
				<button
					onClick={() => setActiveTab("ongoing")}
					className={`pb-2 px-4 font-medium border-b-2 transition ${
						activeTab === "ongoing"
							? "border-primary text-primary"
							: "border-transparent text-muted-foreground"
					}`}
				>
					Ongoing
				</button>
				<button
					onClick={() => setActiveTab("paidBack")}
					className={`pb-2 px-4 font-medium border-b-2 transition ${
						activeTab === "paidBack"
							? "border-primary text-primary"
							: "border-transparent text-muted-foreground"
					}`}
				>
					Paid Back
				</button>
			</div>

			{/* Tab Content */}
			<div className="mt-8">
				{activeTab === "ongoing" && (
					<div className="space-y-4">
						{mockOngoingSafeLocks.length === 0 ? (
							<div className="text-center py-12 text-muted-foreground">
								<p>No ongoing safe locks</p>
								<p className="text-sm mt-2">
									Create your first safe lock to start saving
									with guaranteed returns!
								</p>
							</div>
						) : (
							mockOngoingSafeLocks.map((safeLock) => (
								<div
									key={safeLock.id}
									className="bg-card border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
								>
									<div className="flex justify-between items-start mb-3">
										<h3 className="font-semibold text-lg">
											{safeLock.title}
										</h3>
										<span className="text-sm text-primary font-medium bg-primary/10 px-2 py-1 rounded-full">
											{safeLock.interestRate}% p.a.
										</span>
									</div>

									<div className="space-y-2 mb-4">
										<div className="flex justify-between text-sm">
											<span className="text-muted-foreground">
												days gone
											</span>
											<span className="font-medium">
												days left
											</span>
										</div>
										<div className="w-full bg-[#fff1de] h-4 p-1 flex  items-center rounded-full ">
											<div
												className="bg-primary h-3 rounded-full transition-all duration-300"
												style={{
													width: `${
														(safeLock.savedAmount /
															safeLock.targetAmount) *
														100
													}%`,
												}}
											></div>
										</div>
									</div>


									<div className="flex justify-between items-center pt-2  border-gray-100">
									
										<Button 
									
										className="text-md bg-gray-900 dark:bg-primary text-white  rounded-full hover:text-primary/80 font-light">
											<Link 
											key={safeLock.id}
											state={{safeLockData:safeLock}}
											to={`safe-details/${safeLock.id}`}
											><span>View Details</span>
											</Link>
											
										</Button>
									</div>
								</div>
							))
						)}
					</div>
				)}

				{activeTab === "paidBack" && (
					<div className="space-y-4">
						{mockCompletedSafeLocks.length === 0 ? (
							<div className="text-center py-12 text-muted-foreground">
								<p>No completed safe locks</p>
								<p className="text-sm mt-2">
									Your completed safe locks will appear here.
								</p>
							</div>
						) : (
							mockCompletedSafeLocks.map((safeLock) => (
								<div
									key={safeLock.id}
									className="bg-card border rounded-lg p-4 shadow-sm"
								>
									<div className="flex justify-between items-start mb-3">
										<h3 className="font-semibold text-lg">
											{safeLock.title}
										</h3>
										<span className="text-sm text-green-600 font-medium bg-green-100 px-2 py-1 rounded-full">
											✓ Completed
										</span>
									</div>

									<div className="space-y-2 mb-4">
										<div className="w-full bg-green-100 rounded-full h-2">
											<div className="bg-green-500 h-2 rounded-full w-full"></div>
										</div>
									</div>

									<div className="space-y-2 text-sm">
										<div className="flex justify-between">
											<span className="text-muted-foreground">
												Amount Saved:
											</span>
											<span className="font-medium">
												$
												{safeLock.savedAmount.toLocaleString()}
											</span>
										</div>
										<div className="flex justify-between">
											<span className="text-muted-foreground">
												Total Earned:
											</span>
											<span className="font-medium text-green-600">
												$
												{safeLock.totalEarned.toLocaleString()}
											</span>
										</div>
										<div className="flex justify-between">
											<span className="text-muted-foreground">
												Interest Earned:
											</span>
											<span className="font-medium text-green-600">
												+$
												{(
													safeLock.totalEarned -
													safeLock.savedAmount
												).toLocaleString()}
											</span>
										</div>
									</div>

									<div className="flex justify-between items-center pt-2  mt-3">
										
										<Button className="text-md text-gray-900 rounded-full hover:text-primary/80 font-medium">
											View Details
										</Button>
									</div>
								</div>
							))
						)}
					</div>
				)}
			</div>
		</div>
	);
};
export default SafeLock;
