"use client";

import { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>er<PERSON>rigger,
} from "@/components/ui/drawer";
import { SendCryptoForm } from "@/components/send-crypto-form";
import { But<PERSON> } from "@/components/ui/button";
import { BuyCryptoFlow } from "@/shared/home/<USER>/buy";


export default function DrawerPage() {
	const [isDrawerOpen, setIsDrawerOpen] = useState(false);

	const [isSettingsOpen, setIsSettingsOpen] = useState(false);

	return (
		<div className="p-8 max-w-4xl mx-auto">
			<h1 className="text-3xl font-bold mb-8">
				Drawer Component Examples
			</h1>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
				{/* Example 1: Controlled drawer with Send Crypto form */}
				<div className="p-6 border rounded-lg">
					<h2 className="text-xl font-semibold mb-4">
						Controlled Drawer
					</h2>
					<p className="mb-4">
						This drawer uses controlled state from the parent
						component.
					</p>

					<Button
						onClick={() => setIsDrawerOpen(true)}
						className="bg-[#CD853F] hover:bg-[#B8732F]"
					>
						Open Send Crypto
					</Button>

					<Drawer
						isOpen={isDrawerOpen}
						onClose={() => setIsDrawerOpen(false)}
						position="right"
						width="480px"
					>
						<DrawerHeader
							title="Send Crypto"
							onClose={() => setIsDrawerOpen(false)}
						/>
						<DrawerContent>
							<SendCryptoForm />
						</DrawerContent>
					</Drawer>
				</div>

				{/* Example 2: Another controlled drawer with different content */}
				<div className="p-6 border rounded-lg">
					<h2 className="text-xl font-semibold mb-4">
						Left-side Drawer
					</h2>
					<p className="mb-4">
						This drawer opens from the left side with different
						content.
					</p>

					<Button
						onClick={() => setIsSettingsOpen(true)}
						variant="outline"
					>
						Open Settings
					</Button>

					<Drawer
						isOpen={isSettingsOpen}
						onClose={() => setIsSettingsOpen(false)}
						position="left"
						width="350px"
					>
						<DrawerHeader title="Settings" />
						<DrawerContent>
							<div className="space-y-4">
								<h3 className="font-medium">App Preferences</h3>
								<p>
									This is an example of different content in a
									drawer.
								</p>
								<div className="h-40 bg-slate-100 rounded-md flex items-center justify-center">
									Settings Content
								</div>
							</div>
						</DrawerContent>
						<DrawerFooter>
							<Button
								onClick={() => setIsSettingsOpen(false)}
								className="w-full"
							>
								Save Changes
							</Button>
						</DrawerFooter>
					</Drawer>
				</div>
			</div>

			{/* Example 3: Using DrawerProvider for uncontrolled state */}
			<div className="p-6 border rounded-lg">
				<h2 className="text-xl font-semibold mb-4">
					Context-based Drawer
				</h2>
				<p className="mb-4">
					This drawer uses the DrawerProvider for state management.
				</p>

				<DrawerProvider>
					<DrawerTrigger>
						<Button variant="secondary">Open Help</Button>
					</DrawerTrigger>

					<Drawer>
						<DrawerHeader title="Help & Support" />
						<DrawerContent>
							<div className="space-y-4">
								<p>
									This drawer uses the DrawerProvider context
									for state management.
								</p>
								<p>
									It&apos;s useful when you need to trigger
									the drawer from deeply nested components.
								</p>
							</div>
						</DrawerContent>
					</Drawer>
				</DrawerProvider>
			</div>
			<div>
				<BuyCryptoFlow />
			</div>
		</div>
	);
}
