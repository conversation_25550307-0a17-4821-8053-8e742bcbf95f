import { useCoinData } from "@/hooks/api/crypto-portfolio";


export default function CoinDetailsDrawer({
  coin_id,
  currency,
}: {
  coin_id: string;
  currency: string;
}) {
  const { data, isLoading, isError } = useCoinData({ coin_id, currency });
  // console.log("Coin data:", data);

  if (isLoading) return <div className="p-4">Loading details...</div>;
  if (isError || !data) return <div className="p-4">Error loading data</div>;

  const coin = data.data?.coin_data;

  if (!coin) return (<div className="p-4">No coin data found</div>)

  return (
    <div className="p-4 w-full max-w-md">
      <div className="flex items-center gap-4">
        <img src={coin.image} alt={coin.name} className="w-10 h-10" />
        <div>
          <h2 className="text-xl text-foreground font-bold">{coin.name}</h2>
          <p className="text-sm text-gray-600 uppercase">{coin.currency}</p>
        </div>
      </div>
      <p
        className="mt-4 text-sm text-muted-foreground"
        dangerouslySetInnerHTML={{ __html: coin.description }}
      />
      <div className="mt-4">
        <p>Current Price: ${coin.current_price}</p>
        {/* Only show percentage if it's available */}
        {coin.price_change_percentage_24h !== undefined && (
          <p>
            24h Change:{" "}
            <span
              className={
                coin.price_change_percentage_24h >= 0
                  ? "text-green-600"
                  : "text-red-600"
              }
            >
              {coin.price_change_percentage_24h.toFixed(2)}%
            </span>
          </p>
        )}
      </div>
    </div>
  );
}
