import { useState } from "react";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import CoinDetailsDrawer from "./coin-details-drawer";
import { useCoinsMarketData } from "@/hooks/api/crypto-portfolio";

export default function CoinStatistics() {
	const [page, setPage] = useState(1);
	const [searchQuery, setSearchQuery] = useState("");
	const pageSize = 10;

	const { data, isLoading, isError } = useCoinsMarketData({
		currency: "usd",
		page: 1,
		size: 100,
	});

	const coins = data?.data?.coins || [];
	const filteredCoins = coins.filter(
		(coin) =>
			coin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			coin.currency.toLowerCase().includes(searchQuery.toLowerCase()),
	);

	const totalPages = Math.ceil(filteredCoins.length / pageSize);
	const startIndex = (page - 1) * pageSize;
	const coinsToDisplay = filteredCoins.slice(
		startIndex,
		startIndex + pageSize,
	);

	const { openDrawer } = useDrawer();

	return (
		<div className="">
			{/* Search bar */}
			<div className="flex items-center border p-3 rounded-full gap-2 mt-4 shadow-sm">
				<img
					src="src/assets/images/search.png"
					alt="search icon"
					className="w-5 h-5"
				/>
				<input
					type="text"
					className="w-full outline-none"
					placeholder="Search"
					value={searchQuery}
					onChange={(e) => {
						setSearchQuery(e.target.value);
						setPage(1); // Optional: go to first page when user searches
					}}
				/>
			</div>

			{isLoading && <p className="mt-4 text-center">Loading coins...</p>}
			{isError && (
				<p className="mt-4 text-center text-red-500">
					Error loading coins.
				</p>
			)}

			{/* Coin list */}
			<div className="mt-6 space-y-4">
				{coinsToDisplay.length === 0 && !isLoading && (
					<p>No coins found.</p>
				)}
				{coinsToDisplay.map((coin) => {
					// console.log("Clicked coin:", coin);
					return (
						<div
							key={coin.id}
							className="flex items-center justify-between border p-4 rounded-lg shadow-sm hover:shadow-md transition cursor-pointer"
							onClick={() =>
								openDrawer({
									view: (
										<CoinDetailsDrawer
											coin_id={coin.currency}
											currency={coin.currency}
										/>
									),
									placement: "right",
									customSize: "400px",
								})
							}

						>
							<div className="flex items-center gap-3">
								<img
									src={coin.image}
									alt={coin.name}
									className="w-8 h-8"
								/>
								<div className="flex flex-col">
									<span className="font-semibold">
										{coin.name}
									</span>
									<span className="text-sm text-gray-500">
										{coin.currency}
									</span>
								</div>
							</div>
							<div className="text-right">
								<p className="font-semibold">
									${coin.current_price.toFixed(2)}
								</p>
								<p
									className={`text-xs font-medium ${coin.price_change_percentage_24h >= 0
										? "text-green-600"
										: "text-red-600"
										}`}
								>
									{coin.price_change_percentage_24h.toFixed(2)}%
								</p>
							</div>
						</div>
					)
				})}
			</div>

			{/* Pagination controls */}
			<div className="flex justify-center items-center gap-2 mt-8">
				<button
					onClick={() => setPage((p) => Math.max(p - 1, 1))}
					disabled={page === 1}
					className="w-8 h-8 flex items-center justify-center rounded-full border disabled:opacity-50 bg-gray-400 cursor-pointer"
				>
					<img src="src/assets/images/prev.png" alt="previous" />
				</button>

				{[...Array(totalPages)].map((_, index) => (
					<button
						key={index + 1}
						onClick={() => setPage(index + 1)}
						className={`w-8 h-8 rounded-full flex items-center justify-center ${page === index + 1
							? "bg-orange-400 text-white"
							: "border"
							}`}
					>
						{index + 1}
					</button>
				))}

				<button
					onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
					disabled={page === totalPages}
					className="w-8 h-8 flex items-center justify-center rounded-full border disabled:opacity-50 bg-gray-400 cursor-pointer"
				>
					<img src="src/assets/images/next.png" alt="next" />
				</button>
			</div>
		</div>
	);
}
