// import { <PERSON> } from "react-router-dom"
import { useState } from "react";
import { AnimatedHomeTabs } from "@/shared/home/<USER>";
import CoinStatistics from "./coin-statistics";
import TransactionLogs from "./transaction-logs"
import Web3 from "./web3";

export interface Tab {
  id: string;
  title: string;
  screen: React.ReactNode;
}

const reportsTabs: Tab[] = [
  {
    id: "coinstatistics",
    title: "Coin Statistics",
    screen: <CoinStatistics />,
  },
  {
    id: "transactionlogs",
    title: "Transaction Logs",
    screen: <TransactionLogs />
  },
  {
    id: " web 3",
    title: "web 3",
    screen: <Web3 />
  }
];

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState(reportsTabs[0]);

  return (
    <div className="container mx-auto py-8 sm:px-4">
      <div className="">
        <AnimatedHomeTabs
          tabs={reportsTabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        {activeTab.screen}
      </div>
    </div>
  );
}
