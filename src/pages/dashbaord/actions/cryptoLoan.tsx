import { Eye, EyeClosed } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";


export default function CryptoLoan() {
    
    const [showBalance, setShowBalance] = useState(true)
    
    return (
        <div>
            <div className="pt-10">
                <p className="text-gray-400">Total Loan Balance</p>
                <div className="flex items-center justify-between max-w-100">
                <p className={`text-amber-500 text-3xl md:text-5xl font-bold mt-2`}> {showBalance ? "$0" : "****************"}   </p>
        <div
        className="cursor-pointer"
        onClick={() => setShowBalance(!showBalance)}
      >
        {showBalance ? (
          <Eye className={`h-6 w-6 text-gray-700`} />
        ) : (
          <EyeClosed className={`h-6 w-6 text-gray-700`} />
        )}
      </div>

                </div>
            </div>

        <div className="pt-10 flex gap-10">
           <Link to={'/request-crypto-loan'}> <button className="bg-primary text-white p-4 pl-10 pr-10 rounded-full cursor-pointer">Request</button></Link>
            <button className="bg-[#25292D] text-white p-4 pl-10 pr-10 rounded-full cursor-pointer">Payback</button>
        </div>

        <p className="pt-10">Loan Transaction History</p>
        <div className="pt-10 text-center space-y-2">
            <p>You have not requested any loans yet</p>
            <Link to={'/request-crypto-loan'}><button className="bg-primary text-white p-4 pl-10 pr-10 rounded-full cursor-pointer">Request Loan</button></Link>
        </div>
        </div>
    )
}