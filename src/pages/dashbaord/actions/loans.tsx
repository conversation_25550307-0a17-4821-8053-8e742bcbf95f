import { Link } from "react-router-dom";
import CryptoLoan from "./cryptoLoan";
import FiatLoan from "./fiatLoan";
import { useState } from "react";
import { AnimatedHomeTabs } from "@/shared/home/<USER>";

export interface Tab {
	id: string;
	title: string;
	screen: React.ReactNode;
}

const loanTabs: Tab[] = [
	{
		id: "cryptoLoan",
		title: "Crypto Loans",
		screen: <CryptoLoan />,
	},
	{
		id: "fiatLoan",
		title: "Fiat Loans",
		screen: <FiatLoan />,
	},
];

export default function Loans() {
	const [activeTab, setActiveTab] = useState(loanTabs[0]);

	return (
		<div className="sm:px-4">
			<div className="flex gap-2 items-center">
				<Link to="/clyphub">
					{" "}
					<img
						src="src/assets/images/backbutton.png"
						alt="back"
						className="cursor-pointer"
					/>
				</Link>
				<p className="text-3xl font-bold">Loans</p>
			</div>

			<div className="pt-10">
				<AnimatedHomeTabs
					tabs={loanTabs}
					activeTab={activeTab}
					onTabChange={setActiveTab}
				/>
				{activeTab.screen}
			</div>
		</div>
	);
}
