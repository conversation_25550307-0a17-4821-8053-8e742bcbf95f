import { Link } from "react-router-dom";


const Bills = [
	{
		id: "1",
		label: "Airtime",
		link: "/airtime",
	},

	{
		id: "2",
		label: "Data",
		link:"/data"
	},

	{
		id: "3",
		label: "Electricity",
		link:"/electricity"
	},

	{
		id: "4",
		label: "Cable Tv",
	},

	{
		id: "5",
		label: "Water",
	},
];

const Giftcards = [
	{
		id: "1",
		label: "Buy",
	},

	{
		id: "2",
		label: "Redeem",
	},
];

const Travel = [
	{
		id: "1",
		label: "Book Flight",
	},
];

export default function Lifestyle() {
	return (
		<div className="text-foreground dark:text-foreground sm:px-4">
			<div className="flex gap-2 items-center">
				<Link to="/clyphub">
					{" "}
					<img
						src="src/assets/images/backbutton.png"
						alt="back"
						className="cursor-pointer"
					/>
				</Link>
				<p className="text-3xl font-bold">Lifestyle</p>
			</div>

			<p className="text-2xl pt-20 font-bold">Bills</p>
			<div className="flex flex-wrap gap-10 pt-6">
				{Bills.map((bill) => (
					<Link to={bill.link} key={bill.id}>
						{" "}
						<div className="bg-background dark:bg-background border border-border p-4 w-30 text-center rounded-lg cursor-pointer hover:bg-muted dark:hover:bg-muted transition-colors">
							{bill.label}
						</div>
					</Link>
				))}
			</div>

			<p className="text-2xl pt-20 font-bold">Giftcards</p>
			<div className="flex gap-10 pt-6">
				{Giftcards.map((giftcard) => (
					<div key={giftcard.id} className="bg-background dark:bg-background border border-border p-4 w-30 text-center rounded-lg cursor-pointer hover:bg-muted dark:hover:bg-muted transition-colors">
						{giftcard.label}
					</div>
				))}
			</div>

			<p className="text-2xl pt-20 font-bold">Travel</p>
			<div className="flex gap-10 pt-6">
				{Travel.map((travel) => (
					<div key={travel.id} className="bg-background dark:bg-background border border-border p-4 w-30 text-center rounded-lg cursor-pointer hover:bg-muted dark:hover:bg-muted transition-colors">
						{travel.label}
					</div>
				))}
			</div>
		</div>
	);
}
