// import { Link } from "react-router-dom";
// import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
// import { useState, useEffect } from "react";
// import { APP_CONFIG } from "@/config";

// export default function Airtime() {
// 	const { data: fiatWallets } = useFiatUserWallets();
// 	const [selectedWalletId, setSelectedWalletId] = useState("");
// 	const [countries, setCountries] = useState([]);
// 	const [selectedCountryCode, setSelectedCountryCode] = useState("");
// 	const [networks, setNetworks] = useState([]);
// 	const [selectedNetwork, setSelectedNetwork] = useState("");
// 	const [loadingCountries, setLoadingCountries] = useState(true);
// 	const [loadingNetworks, setLoadingNetworks] = useState(false);
// 	const [countriesError, setCountriesError] = useState(null);
// 	const [networksError, setNetworksError] = useState(null);
// 	const [phoneNumber, setPhoneNumber] = useState("");
// 	const [amount, setAmount] = useState("");

// 	const getUserId = () => {
// 		return localStorage.getItem("user_id") || "";
// 	};

// 	// Fetch countries from API
// 	useEffect(() => {
// 		const fetchCountries = async () => {
// 			try {
// 				const token = localStorage.getItem("auth_token");
// 				setLoadingCountries(true);
// 				setCountriesError(null);
				
// 				const response = await fetch(
// 					`${APP_CONFIG.API_URLS.CURRENCY}/bills/countries`,
// 					{
// 						method: "GET",
// 						headers: {
// 							"Content-Type": "application/json",
// 							Authorization: `Bearer ${token}`,
// 						},
// 					},
// 				);

// 				if (!response.ok) {
// 					throw new Error("Failed to fetch countries");
// 				}

// 				const result = await response.json();

// 				if (result.success && result.data) {
// 					setCountries(result.data);
// 				} else {
// 					throw new Error(result.message || "Invalid response format");
// 				}
// 			} catch (err) {
// 				setCountriesError(err.message);
// 				console.error("Error fetching countries:", err);
// 			} finally {
// 				setLoadingCountries(false);
// 			}
// 		};

// 		fetchCountries();
// 	}, []);

// 	// Fetch networks based on selected country
// 	useEffect(() => {
// 		if (!selectedCountryCode) {
// 			setNetworks([]);
// 			setSelectedNetwork("");
// 			return;
// 		}

// 		const fetchNetworks = async () => {
// 			try {
// 				const token = localStorage.getItem("auth_token");
// 				const userId = getUserId();
				
// 				setLoadingNetworks(true);
// 				setNetworksError(null);
// 				setSelectedNetwork(""); // Reset selected network when country changes
				
// 				const response = await fetch(
// 					`${APP_CONFIG.API_URLS.CURRENCY}/bills/get-bill-from-category`,
// 					{
// 						method: "POST",
// 						headers: {
// 							"Content-Type": "application/json",
// 							Authorization: `Bearer ${token}`,
// 						},
// 						body: JSON.stringify({
// 							biller_type: "AIRTIME",
// 							country_code: selectedCountryCode,
// 							user_id: userId,
// 						}),
// 					},
// 				);

// 				if (!response.ok) {
// 					throw new Error("Failed to fetch networks");
// 				}

// 				const result = await response.json();

// 				if (result.success && result.data) {
// 					setNetworks(result.data);
// 				} else {
// 					throw new Error(result.message || "Invalid response format");
// 				}
// 			} catch (err) {
// 				setNetworksError(err.message);
// 				console.error("Error fetching networks:", err);
// 			} finally {
// 				setLoadingNetworks(false);
// 			}
// 		};

// 		fetchNetworks();
// 	}, [selectedCountryCode]);

// 	// Handle country selection
// 	const handleCountryChange = (e) => {
// 		setSelectedCountryCode(e.target.value);
// 	};

// 	// Handle preset amount selection
// 	const handleAmountSelect = (selectedAmount) => {
// 		setAmount(selectedAmount.toString());
// 	};

// 	return (
// 		<div className="space-y-6 text-foreground dark:text-foreground">
// 			<div className="flex gap-2 items-center">
// 				<Link to="/lifestyle">
// 					{" "}
// 					<img
// 						src="src/assets/images/backbutton.png"
// 						alt="back"
// 						className="cursor-pointer"
// 					/>
// 				</Link>
// 				<p className="text-3xl font-bold">Airtime</p>
// 			</div>

// 			<div>
// 				<div className="space-y-4">
// 					<p className="text-xl">Select Country</p>
// 					<select 
// 						className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
// 						value={selectedCountryCode}
// 						onChange={handleCountryChange}
// 						disabled={loadingCountries}
// 					>
// 						<option value="">
// 							{loadingCountries ? "Loading countries..." : "Select Country"}
// 						</option>
// 						{countriesError ? (
// 							<option disabled>Error loading countries</option>
// 						) : (
// 							countries.map((country) => (
// 								<option key={country.code || country.id} value={country.code || country.id}>
// 									{country.name}
// 								</option>
// 							))
// 						)}
// 					</select>
// 					{countriesError && (
// 						<p className="text-red-500 text-sm">
// 							Failed to load countries: {countriesError}
// 						</p>
// 					)}
// 				</div>
// 			</div>

// 			<div className="space-y-4">
// 				<p className="text-xl">Choose Network</p>
// 				<div className="flex gap-4">
// 					<select
// 						className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
// 						value={selectedNetwork}
// 						onChange={(e) => setSelectedNetwork(e.target.value)}
// 						disabled={loadingNetworks || !selectedCountryCode}
// 					>
// 						<option value="">
// 							{!selectedCountryCode 
// 								? "Select a country first" 
// 								: loadingNetworks 
// 								? "Loading networks..." 
// 								: "Select Network"
// 							}
// 						</option>
// 						{networksError ? (
// 							<option disabled>Error loading networks</option>
// 						) : (
// 							networks.map((network) => (
// 								<option key={network.id} value={network.id}>
// 									{network.name}
// 								</option>
// 							))
// 						)}
// 					</select>
// 				</div>
// 				{networksError && (
// 					<p className="text-red-500 text-sm">
// 						Failed to load networks: {networksError}
// 					</p>
// 				)}
// 			</div>

// 			<div className="grid md:grid-cols-2">
// 				<div className="space-y-4">
// 					<p className="text-xl">Enter Phone Number</p>
// 					<input
// 						type="number"
// 						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
// 						placeholder="Phone Number"
// 						value={phoneNumber}
// 						onChange={(e) => setPhoneNumber(e.target.value)}
// 					/>
// 					<p>the number checks out with {`selected`} operator</p>
// 				</div>

// 				<div className="space-y-4">
// 					<p className="text-xl">Enter Amount</p>
// 					<input
// 						type="number"
// 						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
// 						placeholder="Enter Amount"
// 						value={amount}
// 						onChange={(e) => setAmount(e.target.value)}
// 					/>
// 					<div className="flex gap-10">
// 						<button
// 							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
// 							onClick={() => handleAmountSelect(200)}
// 							type="button"
// 						>
// 							200
// 						</button>
// 						<button
// 							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
// 							onClick={() => handleAmountSelect(500)}
// 							type="button"
// 						>
// 							500
// 						</button>
// 						<button
// 							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
// 							onClick={() => handleAmountSelect(1000)}
// 							type="button"
// 						>
// 							1000
// 						</button>
// 						<button
// 							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
// 							onClick={() => handleAmountSelect(2000)}
// 							type="button"
// 						>
// 							2000
// 						</button>
// 					</div>
// 				</div>
// 			</div>

// 			<div>
// 				<p className="text-xl">Choose Fiat Account</p>
// 				<select
// 					className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
// 					value={selectedWalletId}
// 					onChange={(e) => setSelectedWalletId(e.target.value)}
// 				>
// 					<option value="" disabled>
// 						Select Account
// 					</option>
// 					{fiatWallets && fiatWallets.length > 0 ? (
// 						fiatWallets.map((wallet) => (
// 							<option key={wallet.id} value={wallet.id}>
// 								{wallet.currency} <div>Balance:</div> {wallet.available_balance}
// 							</option>
// 						))
// 					) : (
// 						<option disabled>No fiat accounts available</option>
// 					)}
// 				</select>
// 			</div>

// 			<div>
// 				<button
// 					className="bg-primary text-white p-4 pr-8 pl-8 rounded-full cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
// 					disabled={
// 						!selectedCountryCode ||
// 						!selectedNetwork ||
// 						!selectedWalletId ||
// 						!amount ||
// 						!phoneNumber
// 					}
// 				>
// 					Buy Airtime
// 				</button>
// 			</div>
// 		</div>
// 	);
// }


// import { Link } from "react-router-dom";
// import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
// import { usePurchaseAirtime } from "@/hooks/api/bills";
// import { useState, useEffect } from "react";
// import { APP_CONFIG } from "@/config";
// import { PurchaseAirtimePayload } from "@/types/bills";
// import { toast } from "sonner";

// export default function Airtime() {
// 	const { data: fiatWallets } = useFiatUserWallets();
// 	const purchaseAirtimeMutation = usePurchaseAirtime();
	
// 	const [selectedWalletId, setSelectedWalletId] = useState("");
// 	const [countries, setCountries] = useState([]);
// 	const [selectedCountryCode, setSelectedCountryCode] = useState("");
// 	const [networks, setNetworks] = useState([]);
// 	const [selectedNetwork, setSelectedNetwork] = useState("");
// 	const [loadingCountries, setLoadingCountries] = useState(true);
// 	const [loadingNetworks, setLoadingNetworks] = useState(false);
// 	const [countriesError, setCountriesError] = useState(null);
// 	const [networksError, setNetworksError] = useState(null);
// 	const [phoneNumber, setPhoneNumber] = useState("");
// 	const [amount, setAmount] = useState("");
	
// 	// New states for phone number validation
// 	const [detectedOperator, setDetectedOperator] = useState("");
// 	const [loadingOperator, setLoadingOperator] = useState(false);
// 	const [operatorError, setOperatorError] = useState(null);

// 	// Success and error states for purchase
// 	const [purchaseSuccess, setPurchaseSuccess] = useState(false);
// 	const [purchaseError, setPurchaseError] = useState(null);

// 	const getUserId = () => {
// 		return localStorage.getItem("user_id") || "";
// 	};

// 	// Helper function to get the selected network name
// 	const getSelectedNetworkName = () => {
// 		const network = networks.find(n => n.id === parseInt(selectedNetwork));
// 		return network ? network.name : "";
// 	};

// 	// Helper function to get the detected operator ID
// 	const getDetectedOperatorId = () => {
// 		// Find the network that matches the detected operator name
// 		const network = networks.find(n => n.name.toLowerCase() === detectedOperator.toLowerCase());
// 		return network ? network.id : null;
// 	};

// 	// Check if the number matches the selected network
// 	const isNetworkValid = () => {
// 		if (!detectedOperator || !selectedNetwork || loadingOperator) {
// 			return false; // Not valid if still loading or no data
// 		}
		
// 		const detectedOperatorId = getDetectedOperatorId();
// 		return detectedOperatorId && parseInt(selectedNetwork) === detectedOperatorId;
// 	};

// 	// Handle airtime purchase
// 	const handleBuyAirtime = async () => {
// 		const selectedWallet = fiatWallets?.find(wallet => wallet.id === selectedWalletId);
// 		const currency = selectedWallet?.currency || "";
		
// 		const payload: PurchaseAirtimePayload = {
// 			purchase_type: "AIRTIME",
// 			operator_id: selectedNetwork,
// 			phone_number: phoneNumber,
// 			amount: parseFloat(amount),
// 			country_code: selectedCountryCode,
// 			currency: currency,
// 			use_local_amount: true,
// 			// user_id: getUserId()
// 		};
		
// 		try {
// 			setPurchaseError(null);
// 			const result = await purchaseAirtimeMutation.mutateAsync(payload);
// 			console.log("Airtime purchase successful:", result);
// 			setPurchaseSuccess(true);
			
// 			// Reset form after successful purchase
// 			setTimeout(() => {
// 				setPhoneNumber("");
// 				setAmount("");
// 				setSelectedNetwork("");
// 				setSelectedWalletId("");
// 				setPurchaseSuccess(false);
// 			}, 3000);
			
// 		} catch (error: any) {
// 			console.error("Error purchasing airtime:", error);
// 			// setPurchaseError(error.message || "Failed to purchase airtime");
// 			toast.error(error.message || "Failed to purchase airtime")
// 		}
// 	};

// 	// Fetch countries from API
// 	useEffect(() => {
// 		const fetchCountries = async () => {
// 			try {
// 				const token = localStorage.getItem("auth_token");
// 				setLoadingCountries(true);
// 				setCountriesError(null);
				
// 				const response = await fetch(
// 					`${APP_CONFIG.API_URLS.CURRENCY}/bills/countries`,
// 					{
// 						method: "GET",
// 						headers: {
// 							"Content-Type": "application/json",
// 							Authorization: `Bearer ${token}`,
// 						},
// 					},
// 				);

// 				if (!response.ok) {
// 					throw new Error("Failed to fetch countries");
// 				}

// 				const result = await response.json();

// 				if (result.success && result.data) {
// 					setCountries(result.data);
// 				} else {
// 					throw new Error(result.message || "Invalid response format");
// 				}
// 			} catch (err: any) {
// 				setCountriesError(err.message);
// 				console.error("Error fetching countries:", err);
// 			} finally {
// 				setLoadingCountries(false);
// 			}
// 		};

// 		fetchCountries();
// 	}, []);

// 	// Fetch networks based on selected country
// 	useEffect(() => {
// 		if (!selectedCountryCode) {
// 			setNetworks([]);
// 			setSelectedNetwork("");
// 			return;
// 		}

// 		const fetchNetworks = async () => {
// 			try {
// 				const token = localStorage.getItem("auth_token");
// 				const userId = getUserId();
				
// 				setLoadingNetworks(true);
// 				setNetworksError(null);
// 				setSelectedNetwork(""); // Reset selected network when country changes
				
// 				const response = await fetch(
// 					`${APP_CONFIG.API_URLS.CURRENCY}/bills/get-bill-from-category`,
// 					{
// 						method: "POST",
// 						headers: {
// 							"Content-Type": "application/json",
// 							Authorization: `Bearer ${token}`,
// 						},
// 						body: JSON.stringify({
// 							biller_type: "AIRTIME",
// 							country_code: selectedCountryCode,
// 							user_id: userId,
// 						}),
// 					},
// 				);

// 				if (!response.ok) {
// 					throw new Error("Failed to fetch networks");
// 				}

// 				const result = await response.json();

// 				if (result.success && result.data) {
// 					setNetworks(result.data);
// 				} else {
// 					throw new Error(result.message || "Invalid response format");
// 				}
// 			} catch (err: any) {
// 				setNetworksError(err.message);
// 				console.error("Error fetching networks:", err);
// 			} finally {
// 				setLoadingNetworks(false);
// 			}
// 		};

// 		fetchNetworks();
// 	}, [selectedCountryCode]);

// 	// Auto-detect operator when phone number changes
// 	useEffect(() => {
// 		if (!phoneNumber || !selectedCountryCode || phoneNumber.length < 10) {
// 			setDetectedOperator("");
// 			setOperatorError(null);
// 			return;
// 		}

// 		const detectOperator = async () => {
// 			try {
// 				const token = localStorage.getItem("auth_token");
// 				const userId = getUserId();
				
// 				setLoadingOperator(true);
// 				setOperatorError(null);
				
// 				const response = await fetch(
// 					`${APP_CONFIG.API_URLS.CURRENCY}/bills/auto-detect-operator`,
// 					{
// 						method: "POST",
// 						headers: {
// 							"Content-Type": "application/json",
// 							Authorization: `Bearer ${token}`,
// 						},
// 						body: JSON.stringify({
// 							phone_number: phoneNumber,
// 							country_code: selectedCountryCode,
// 							user_id: userId,
// 						}),
// 					},
// 				);

// 				if (!response.ok) {
// 					throw new Error("Failed to detect operator");
// 				}

// 				const result = await response.json();

// 				if (result.success && result.data && result.data.length > 0) {
// 					setDetectedOperator(result.data[0].name);
// 				} else {
// 					setDetectedOperator("");
// 					setOperatorError("Could not detect operator for this number");
// 				}
// 			} catch (err: any) {
// 				setOperatorError(err.message);
// 				setDetectedOperator("");
// 				console.error("Error detecting operator:", err);
// 			} finally {
// 				setLoadingOperator(false);
// 			}
// 		};

// 		// Debounce the API call
// 		const timeoutId = setTimeout(detectOperator, 500);
// 		return () => clearTimeout(timeoutId);
// 	}, [phoneNumber, selectedCountryCode]);

// 	// Handle country selection
// 	const handleCountryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
// 		setSelectedCountryCode(e.target.value);
// 		// Reset phone number validation when country changes
// 		setDetectedOperator("");
// 		setOperatorError(null);
// 		setPurchaseError(null);
// 	};

// 	// Handle preset amount selection
// 	const handleAmountSelect = (selectedAmount: number) => {
// 		setAmount(selectedAmount.toString());
// 	};

// 	// Handle phone number change
// 	const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
// 		setPhoneNumber(e.target.value);
// 		setPurchaseError(null);
// 	};

// 	// Get validation message based on detected operator and selected network
// 	const getValidationMessage = () => {
// 		if (loadingOperator) {
// 			return <p className="text-blue-500">Detecting operator...</p>;
// 		}

// 		if (operatorError) {
// 			return <p className="text-red-500">{operatorError}</p>;
// 		}

// 		if (!detectedOperator) {
// 			if (phoneNumber) {
// 				return <p className="text-gray-500">Enter a valid phone number to detect operator</p>;
// 			}
// 			return null;
// 		}

// 		const selectedNetworkName = getSelectedNetworkName();
// 		const detectedOperatorId = getDetectedOperatorId();
		
// 		// If no network is selected yet, just show the detected operator
// 		if (!selectedNetworkName) {
// 			return (
// 				<p className="text-blue-500">
// 					Number is registered on {detectedOperator} network
// 				</p>
// 			);
// 		}

// 		// Compare using IDs instead of names
// 		const isMatchingNetwork = detectedOperatorId && parseInt(selectedNetwork) === detectedOperatorId;

// 		if (isMatchingNetwork) {
// 			return (
// 				<p className="text-green-600">
// 					The number checks out with {detectedOperator} operator
// 				</p>
// 			);
// 		} else {
// 			return (
// 				<p className="text-red-500">
// 					Number is registered on {detectedOperator} network, but you selected {selectedNetworkName}. Please select the correct network or use a different number.
// 				</p>
// 			);
// 		}
// 	};

// 	return (
// 		<div className="space-y-6 text-foreground dark:text-foreground">
// 			<div className="flex gap-2 items-center">
// 				<Link to="/lifestyle">
// 					<img
// 						src="src/assets/images/backbutton.png"
// 						alt="back"
// 						className="cursor-pointer"
// 					/>
// 				</Link>
// 				<p className="text-3xl font-bold">Airtime</p>
// 			</div>

// 			{/* Success Message */}
// 			{purchaseSuccess && (
// 				<div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
// 					<strong>Success!</strong> Your airtime purchase was completed successfully.
// 				</div>
// 			)}

// 			{/* Error Message */}
// 			{purchaseError && (
// 				<div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
// 					<strong>Error!</strong> {purchaseError}
// 				</div>
// 			)}

// 			<div>
// 				<div className="space-y-4">
// 					<p className="text-xl">Select Country</p>
// 					<select 
// 						className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
// 						value={selectedCountryCode}
// 						onChange={handleCountryChange}
// 						disabled={loadingCountries}
// 					>
// 						<option value="">
// 							{loadingCountries ? "Loading countries..." : "Select Country"}
// 						</option>
// 						{countriesError ? (
// 							<option disabled>Error loading countries</option>
// 						) : (
// 							countries.map((country: any) => (
// 								<option key={country.code || country.id} value={country.code || country.id}>
// 									{country.name}
// 								</option>
// 							))
// 						)}
// 					</select>
// 					{countriesError && (
// 						<p className="text-red-500 text-sm">
// 							Failed to load countries: {countriesError}
// 						</p>
// 					)}
// 				</div>
// 			</div>

// 			<div className="space-y-4">
// 				<p className="text-xl">Choose Network</p>
// 				<div className="flex gap-4">
// 					<select
// 						className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
// 						value={selectedNetwork}
// 						onChange={(e) => setSelectedNetwork(e.target.value)}
// 						disabled={loadingNetworks || !selectedCountryCode}
// 					>
// 						<option value="">
// 							{!selectedCountryCode 
// 								? "Select a country first" 
// 								: loadingNetworks 
// 								? "Loading networks..." 
// 								: "Select Network"
// 							}
// 						</option>
// 						{networksError ? (
// 							<option disabled>Error loading networks</option>
// 						) : (
// 							networks.map((network: any) => (
// 								<option key={network.id} value={network.id}>
// 									{network.name}
// 								</option>
// 							))
// 						)}
// 					</select>
// 				</div>
// 				{networksError && (
// 					<p className="text-red-500 text-sm">
// 						Failed to load networks: {networksError}
// 					</p>
// 				)}
// 			</div>

// 			<div className="grid md:grid-cols-2">
// 				<div className="space-y-4">
// 					<p className="text-xl">Enter Phone Number</p>
// 					<input
// 						type="number"
// 						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
// 						placeholder="Phone Number"
// 						value={phoneNumber}
// 						onChange={handlePhoneNumberChange}
// 					/>
// 					<div className="text-sm">
// 						{getValidationMessage()}
// 					</div>
// 				</div>

// 				<div className="space-y-4">
// 					<p className="text-xl">Enter Amount</p>
// 					<input
// 						type="number"
// 						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
// 						placeholder="Enter Amount"
// 						value={amount}
// 						onChange={(e) => setAmount(e.target.value)}
// 					/>
// 					<div className="flex gap-10">
// 						<button
// 							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
// 							onClick={() => handleAmountSelect(500)}
// 							type="button"
// 						>
// 							500
// 						</button>
// 						<button
// 							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
// 							onClick={() => handleAmountSelect(1000)}
// 							type="button"
// 						>
// 							1000
// 						</button>
// 						<button
// 							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
// 							onClick={() => handleAmountSelect(2000)}
// 							type="button"
// 						>
// 							2000
// 						</button>
// 					</div>
// 				</div>
// 			</div>

// 			<div>
// 				<p className="text-xl">Choose Fiat Account</p>
// 				<select
// 					className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
// 					value={selectedWalletId}
// 					onChange={(e) => setSelectedWalletId(e.target.value)}
// 				>
// 					<option value="" disabled>
// 						Select Account
// 					</option>
// 					{fiatWallets && fiatWallets.length > 0 ? (
// 						fiatWallets.map((wallet: any) => (
// 							<option key={wallet.id} value={wallet.id}>
// 								{wallet.currency} - Balance: {wallet.available_balance}
// 							</option>
// 						))
// 					) : (
// 						<option disabled>No fiat accounts available</option>
// 					)}
// 				</select>
// 			</div>

// 			<div>
// 				<button
// 					className="bg-primary text-white p-4 pr-8 pl-8 rounded-full cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
// 					disabled={
// 						!selectedCountryCode ||
// 						!selectedNetwork ||
// 						!selectedWalletId ||
// 						!amount ||
// 						!phoneNumber ||
// 						!isNetworkValid() ||
// 						purchaseAirtimeMutation.isPending
// 					}
// 					onClick={handleBuyAirtime}
// 				>
// 					{purchaseAirtimeMutation.isPending ? "Processing..." : "Buy Airtime"}
// 				</button>
// 			</div>
// 		</div>
// 	);
// }


///////////////////////////////////////////////////////////////////////////////////////////
import { Link } from "react-router-dom";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { usePurchaseAirtime } from "@/hooks/api/bills";
import { useState, useEffect } from "react";
import { APP_CONFIG } from "@/config";
import { PurchaseAirtimePayload } from "@/types/bills";
import { toast } from "sonner";

export default function Airtime() {
	const { data: fiatWallets } = useFiatUserWallets();
	const purchaseAirtimeMutation = usePurchaseAirtime();
	
	const [selectedWalletId, setSelectedWalletId] = useState("");
	const [countries, setCountries] = useState([]);
	const [selectedCountryCode, setSelectedCountryCode] = useState("");
	const [networks, setNetworks] = useState([]);
	const [selectedNetwork, setSelectedNetwork] = useState("");
	const [loadingCountries, setLoadingCountries] = useState(true);
	const [loadingNetworks, setLoadingNetworks] = useState(false);
	const [countriesError, setCountriesError] = useState(null);
	const [networksError, setNetworksError] = useState(null);
	const [phoneNumber, setPhoneNumber] = useState("");
	const [amount, setAmount] = useState("");
	
	// New states for phone number validation
	const [detectedOperator, setDetectedOperator] = useState("");
	const [loadingOperator, setLoadingOperator] = useState(false);
	const [operatorError, setOperatorError] = useState(null);

	// Success and error states for purchase
	const [purchaseSuccess, setPurchaseSuccess] = useState(false);
	const [purchaseError, setPurchaseError] = useState(null);

	const getUserId = () => {
		return localStorage.getItem("user_id") || "";
	};

	// Helper function to validate amount
	const isAmountValid = () => {
		const numAmount = parseFloat(amount);
		return !isNaN(numAmount) && numAmount >= 500;
	};

	// Helper function to get amount validation message
	const getAmountValidationMessage = () => {
		if (!amount) return null;
		
		const numAmount = parseFloat(amount);
		if (isNaN(numAmount)) {
			return <p className="text-red-500 text-sm">Please enter a valid amount</p>;
		}
		
		if (numAmount < 500) {
			return <p className="text-red-500 text-sm">Minimum amount is 500</p>;
		}
		
		return null;
	};

	// Helper function to get the selected network name
	const getSelectedNetworkName = () => {
		const network = networks.find(n => n.id === parseInt(selectedNetwork));
		return network ? network.name : "";
	};

	// Helper function to get the detected operator ID
	const getDetectedOperatorId = () => {
		// Find the network that matches the detected operator name
		const network = networks.find(n => n.name.toLowerCase() === detectedOperator.toLowerCase());
		return network ? network.id : null;
	};

	// Check if the number matches the selected network
	const isNetworkValid = () => {
		if (!detectedOperator || !selectedNetwork || loadingOperator) {
			return false; // Not valid if still loading or no data
		}
		
		const detectedOperatorId = getDetectedOperatorId();
		return detectedOperatorId && parseInt(selectedNetwork) === detectedOperatorId;
	};

	// Handle airtime purchase
	const handleBuyAirtime = async () => {
		// Additional client-side validation
		if (!isAmountValid()) {
			toast.error("Amount must be at least 500");
			return;
		}

		const selectedWallet = fiatWallets?.find(wallet => wallet.id === selectedWalletId);
		const currency = selectedWallet?.currency || "";
		
		const payload: PurchaseAirtimePayload = {
			purchase_type: "AIRTIME",
			operator_id: selectedNetwork,
			phone_number: phoneNumber,
			amount: parseFloat(amount),
			country_code: selectedCountryCode,
			currency: currency,
			use_local_amount: true,
			// user_id: getUserId()
		};
		
		try {
			setPurchaseError(null);
			const result = await purchaseAirtimeMutation.mutateAsync(payload);
			console.log("Airtime purchase successful:", result);
			setPurchaseSuccess(true);
			
			// Reset form after successful purchase
			setTimeout(() => {
				setPhoneNumber("");
				setAmount("");
				setSelectedNetwork("");
				setSelectedWalletId("");
				setPurchaseSuccess(false);
			}, 3000);
			
		} catch (error: any) {
			console.error("Error purchasing airtime:", error);
			// setPurchaseError(error.message || "Failed to purchase airtime");
			toast.error(error.message || "Failed to purchase airtime")
		}
	};

	// Fetch countries from API
	useEffect(() => {
		const fetchCountries = async () => {
			try {
				const token = localStorage.getItem("auth_token");
				setLoadingCountries(true);
				setCountriesError(null);
				
				const response = await fetch(
					`${APP_CONFIG.API_URLS.CURRENCY}/bills/countries`,
					{
						method: "GET",
						headers: {
							"Content-Type": "application/json",
							Authorization: `Bearer ${token}`,
						},
					},
				);

				if (!response.ok) {
					throw new Error("Failed to fetch countries");
				}

				const result = await response.json();

				if (result.success && result.data) {
					setCountries(result.data);
				} else {
					throw new Error(result.message || "Invalid response format");
				}
			} catch (err: any) {
				setCountriesError(err.message);
				console.error("Error fetching countries:", err);
			} finally {
				setLoadingCountries(false);
			}
		};

		fetchCountries();
	}, []);

	// Fetch networks based on selected country
	useEffect(() => {
		if (!selectedCountryCode) {
			setNetworks([]);
			setSelectedNetwork("");
			return;
		}

		const fetchNetworks = async () => {
			try {
				const token = localStorage.getItem("auth_token");
				const userId = getUserId();
				
				setLoadingNetworks(true);
				setNetworksError(null);
				setSelectedNetwork(""); // Reset selected network when country changes
				
				const response = await fetch(
					`${APP_CONFIG.API_URLS.CURRENCY}/bills/get-bill-from-category`,
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							Authorization: `Bearer ${token}`,
						},
						body: JSON.stringify({
							biller_type: "AIRTIME",
							country_code: selectedCountryCode,
							user_id: userId,
						}),
					},
				);

				if (!response.ok) {
					throw new Error("Failed to fetch networks");
				}

				const result = await response.json();

				if (result.success && result.data) {
					setNetworks(result.data);
				} else {
					throw new Error(result.message || "Invalid response format");
				}
			} catch (err: any) {
				setNetworksError(err.message);
				console.error("Error fetching networks:", err);
			} finally {
				setLoadingNetworks(false);
			}
		};

		fetchNetworks();
	}, [selectedCountryCode]);

	// Auto-detect operator when phone number changes
	useEffect(() => {
		if (!phoneNumber || !selectedCountryCode || phoneNumber.length < 10) {
			setDetectedOperator("");
			setOperatorError(null);
			return;
		}

		const detectOperator = async () => {
			try {
				const token = localStorage.getItem("auth_token");
				const userId = getUserId();
				
				setLoadingOperator(true);
				setOperatorError(null);
				
				const response = await fetch(
					`${APP_CONFIG.API_URLS.CURRENCY}/bills/auto-detect-operator`,
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							Authorization: `Bearer ${token}`,
						},
						body: JSON.stringify({
							phone_number: phoneNumber,
							country_code: selectedCountryCode,
							user_id: userId,
						}),
					},
				);

				if (!response.ok) {
					throw new Error("Failed to detect operator");
				}

				const result = await response.json();

				if (result.success && result.data && result.data.length > 0) {
					setDetectedOperator(result.data[0].name);
				} else {
					setDetectedOperator("");
					setOperatorError("Could not detect operator for this number");
				}
			} catch (err: any) {
				setOperatorError(err.message);
				setDetectedOperator("");
				console.error("Error detecting operator:", err);
			} finally {
				setLoadingOperator(false);
			}
		};

		// Debounce the API call
		const timeoutId = setTimeout(detectOperator, 500);
		return () => clearTimeout(timeoutId);
	}, [phoneNumber, selectedCountryCode]);

	// Handle country selection
	const handleCountryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
		setSelectedCountryCode(e.target.value);
		// Reset phone number validation when country changes
		setDetectedOperator("");
		setOperatorError(null);
		setPurchaseError(null);
	};

	// Handle preset amount selection - only allow amounts >= 500
	const handleAmountSelect = (selectedAmount: number) => {
		setAmount(selectedAmount.toString());
	};

	// Handle phone number change
	const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setPhoneNumber(e.target.value);
		setPurchaseError(null);
	};

	// Handle amount change with validation
	const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setAmount(e.target.value);
		setPurchaseError(null);
	};

	// Get validation message based on detected operator and selected network
	const getValidationMessage = () => {
		if (loadingOperator) {
			return <p className="text-blue-500">Detecting operator...</p>;
		}

		if (operatorError) {
			return <p className="text-red-500">{operatorError}</p>;
		}

		if (!detectedOperator) {
			if (phoneNumber) {
				return <p className="text-gray-500">Enter a valid phone number to detect operator</p>;
			}
			return null;
		}

		const selectedNetworkName = getSelectedNetworkName();
		const detectedOperatorId = getDetectedOperatorId();
		
		// If no network is selected yet, just show the detected operator
		if (!selectedNetworkName) {
			return (
				<p className="text-blue-500">
					Number is registered on {detectedOperator} network
				</p>
			);
		}

		// Compare using IDs instead of names
		const isMatchingNetwork = detectedOperatorId && parseInt(selectedNetwork) === detectedOperatorId;

		if (isMatchingNetwork) {
			return (
				<p className="text-green-600">
					The number checks out with {detectedOperator} operator
				</p>
			);
		} else {
			return (
				<p className="text-red-500">
					Number is registered on {detectedOperator} network, but you selected {selectedNetworkName}. Please select the correct network or use a different number.
				</p>
			);
		}
	};

	return (
		<div className="space-y-6 text-foreground dark:text-foreground">
			<div className="flex gap-2 items-center">
				<Link to="/lifestyle">
					<img
						src="src/assets/images/backbutton.png"
						alt="back"
						className="cursor-pointer"
					/>
				</Link>
				<p className="text-3xl font-bold">Airtime</p>
			</div>

			{/* Success Message */}
			{purchaseSuccess && (
				<div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
					<strong>Success!</strong> Your airtime purchase was completed successfully.
				</div>
			)}

			{/* Error Message */}
			{purchaseError && (
				<div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
					<strong>Error!</strong> {purchaseError}
				</div>
			)}

			<div>
				<div className="space-y-4">
					<p className="text-xl">Select Country</p>
					<select 
						className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
						value={selectedCountryCode}
						onChange={handleCountryChange}
						disabled={loadingCountries}
					>
						<option value="">
							{loadingCountries ? "Loading countries..." : "Select Country"}
						</option>
						{countriesError ? (
							<option disabled>Error loading countries</option>
						) : (
							countries.map((country: any) => (
								<option key={country.code || country.id} value={country.code || country.id}>
									{country.name}
								</option>
							))
						)}
					</select>
					{countriesError && (
						<p className="text-red-500 text-sm">
							Failed to load countries: {countriesError}
						</p>
					)}
				</div>
			</div>

			<div className="space-y-4">
				<p className="text-xl">Choose Network</p>
				<div className="flex gap-4">
					<select
						className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
						value={selectedNetwork}
						onChange={(e) => setSelectedNetwork(e.target.value)}
						disabled={loadingNetworks || !selectedCountryCode}
					>
						<option value="">
							{!selectedCountryCode 
								? "Select a country first" 
								: loadingNetworks 
								? "Loading networks..." 
								: "Select Network"
							}
						</option>
						{networksError ? (
							<option disabled>Error loading networks</option>
						) : (
							networks.map((network: any) => (
								<option key={network.id} value={network.id}>
									{network.name}
								</option>
							))
						)}
					</select>
				</div>
				{networksError && (
					<p className="text-red-500 text-sm">
						Failed to load networks: {networksError}
					</p>
				)}
			</div>

			<div className="grid md:grid-cols-2">
				<div className="space-y-4">
					<p className="text-xl">Enter Phone Number</p>
					<input
						type="number"
						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
						placeholder="Phone Number"
						value={phoneNumber}
						onChange={handlePhoneNumberChange}
					/>
					<div className="text-sm">
						{getValidationMessage()}
					</div>
				</div>

				<div className="space-y-4">
					<p className="text-xl">Enter Amount</p>
					<input
						type="number"
						min="500"
						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
						placeholder="Enter Amount"
						value={amount}
						onChange={handleAmountChange}
					/>
					{getAmountValidationMessage()}
					<div className="flex gap-10">
						<button
							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
							onClick={() => handleAmountSelect(500)}
							type="button"
						>
							500
						</button>
						<button
							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
							onClick={() => handleAmountSelect(1000)}
							type="button"
						>
							1000
						</button>
						<button
							className="bg-gray-300 p-2 rounded-full pr-4 pl-4 cursor-pointer hover:bg-primary"
							onClick={() => handleAmountSelect(2000)}
							type="button"
						>
							2000
						</button>
					</div>
				</div>
			</div>

			<div>
				<p className="text-xl">Choose Fiat Account</p>
				<select
					className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
					value={selectedWalletId}
					onChange={(e) => setSelectedWalletId(e.target.value)}
				>
					<option value="" disabled>
						Select Account
					</option>
					{fiatWallets && fiatWallets.length > 0 ? (
						fiatWallets.map((wallet: any) => (
							<option key={wallet.id} value={wallet.id}>
								{wallet.currency} - Balance: {wallet.available_balance}
							</option>
						))
					) : (
						<option disabled>No fiat accounts available</option>
					)}
				</select>
			</div>

			<div>
				<button
					className="bg-primary text-white p-4 pr-8 pl-8 rounded-full cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
					disabled={
						!selectedCountryCode ||
						!selectedNetwork ||
						!selectedWalletId ||
						!amount ||
						!phoneNumber ||
						!isNetworkValid() ||
						!isAmountValid() ||
						purchaseAirtimeMutation.isPending
					}
					onClick={handleBuyAirtime}
				>
					{purchaseAirtimeMutation.isPending ? "Processing..." : "Buy Airtime"}
				</button>
			</div>
		</div>
	);
}


