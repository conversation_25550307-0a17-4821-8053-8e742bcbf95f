import { Link } from "react-router-dom";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useState } from "react";

export default function Electricity() {
	const { data: fiatWallets } = useFiatUserWallets();
	const [selectedWalletId, setSelectedWalletId] = useState("");
	return (
		<div className="space-y-6 text-foreground dark:text-foreground">
			<div className="flex gap-2 items-center">
				<Link to="/lifestyle">
					{" "}
					<img
						src="src/assets/images/backbutton.png"
						alt="back"
						className="cursor-pointer"
					/>
				</Link>
				<p className="text-3xl font-bold">Electricity</p>
			</div>

            <div>
                <p>Choose Bouquet</p>
                <div className="flex gap-3">
                    <button className="p-3 rounded-full bg-gray-300 cursor-pointer">Prepaid</button>
                    <button className="p-3 rounded-full bg-gray-300 cursor-pointer">Postpaid</button>
                </div>
            </div>

			<div className="grid md:grid-cols-2">
                <div className="space-y-4">
                <p className="text-xl">Choose Biller</p>
				<div className="">
					<select className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground">
						<option>Select Biller</option>
					</select>
				</div>
                </div>

				<div className="space-y-4">
					<p className="text-xl">Enter Amount</p>
					<input
						type="number"
						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
						placeholder="Enter Amount"
					/>
				</div>
			</div>

			<div className="grid md:grid-cols-2">
				<div className="space-y-4">
					<p className="text-xl">Enter Meter Number</p>
					<input
						type="number"
						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
						placeholder="Meter Number"
					/>
				</div>

				<div className="space-y-4">
					<p className="text-xl">Enter Phone Number</p>
					<input
						type="number"
						className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
						placeholder="Enter Phone number"
					/>
				</div>
			</div>
            		<div>
					<p className="text-xl">Choose Fiat Account</p>
					<select
						className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
						value={selectedWalletId}
						onChange={(e) => setSelectedWalletId(e.target.value)}
					>
						<option value="" disabled>
							Select Account
						</option>
						{fiatWallets && fiatWallets.length > 0 ? (
							fiatWallets.map((wallet) => (
								<option key={wallet.id} value={wallet.id}>
									{wallet.currency}
								</option>
							))
						) : (
							<option disabled>No fiat accounts available</option>
						)}
					</select>
				</div>


                <div>
                    <button className="bg-primary text-white p-4 pr-8 pl-8 rounded-full cursor-pointer">Confirm</button>
                </div>
		</div>
	);
}
