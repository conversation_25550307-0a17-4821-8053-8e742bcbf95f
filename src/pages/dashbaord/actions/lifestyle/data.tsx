import { Link } from "react-router-dom";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useState } from "react";


export default function Data() {
    const { data: fiatWallets } = useFiatUserWallets();
    const [selectedWalletId, setSelectedWalletId] = useState("");
    return (
        <div className="space-y-6 text-foreground dark:text-foreground">
            <div className="flex gap-2 items-center">
                <Link to="/lifestyle">
                    {" "}
                    <img
                        src="src/assets/images/backbutton.png"
                        alt="back"
                        className="cursor-pointer"
                    />
                </Link>
                <p className="text-3xl font-bold">Data</p>
            </div>

            <div className="space-y-4">
                <p className="text-xl">Choose Network</p>
                <div className="flex gap-4">
                    <select className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground">
                        <option>Select Network</option>
                        <option>MTN</option>
                        <option>GLO</option>
                        <option>Airtel</option>
                        <option>9MOBILE</option>
                    </select>
                </div>
            </div>

            <div className="grid md:grid-cols-2">
                <div className="space-y-4">
                    <p className="text-xl">Enter Phone Number</p>
                    <input
                        type="number"
                        className="border border-border dark:border-border rounded-full p-3 w-120 bg-background dark:bg-background"
                        placeholder="Phone Number"
                    />
                </div>

                <div className="space-y-4">
                    <p className="text-xl">Choose Plan</p>
                        <select className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground">
                        <option>Select Plan</option>
                        {/* <option>MTN</option>
                        <option>GLO</option>
                        <option>Airtel</option>
                        <option>9MOBILE</option> */}
                    </select>
                </div>
            </div>
                    <div>
                    <p className="text-xl">Choose Fiat Account</p>
                    <select
                        className="mt-2 p-3 border border-border dark:border-border rounded-full w-120 bg-background dark:bg-background text-foreground dark:text-foreground"
                        value={selectedWalletId}
                        onChange={(e) => setSelectedWalletId(e.target.value)}
                    >
                        <option value="" disabled>
                            Select Account
                        </option>
                        {fiatWallets && fiatWallets.length > 0 ? (
                            fiatWallets.map((wallet) => (
                                <option key={wallet.id} value={wallet.id}>
                                    {wallet.currency}
                                </option>
                            ))
                        ) : (
                            <option disabled>No fiat accounts available</option>
                        )}
                    </select>
                </div>


                <div>
                    <button className="bg-primary text-white p-4 pr-8 pl-8 rounded-full cursor-pointer">Buy Data</button>
                </div>
        </div>
    );
}
