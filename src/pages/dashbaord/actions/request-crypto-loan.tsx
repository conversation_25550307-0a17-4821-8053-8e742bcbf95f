import { Link } from "react-router-dom";
import { useState } from "react";

export default function RequestCryptoLoan() {
	const [fileName, setFileName] = useState("");

	const handleFileChange = (e) => {
		const file = e.target.files[0];
		if (file) {
			setFileName(file.name);
		}
	};
	return (
		<div>
			<div className="flex gap-2 items-center">
				<Link to="/loans">
					<img
						src="src/assets/images/backbutton.png"
						alt="back"
						className="cursor-pointer"
					/>
				</Link>
				<p className="text-3xl font-bold">Request Loan</p>
			</div>

			<p className="pt-5 pb-5">
				Complete the required verification to ascertain you are eligible
				to request for loans
			</p>

			<form className="verification-form space-y-4">
				<div className="flex items-center justify-between md:w-138 border border-[#E0E6ED] p-2 rounded-full">
					<p>BVN Verification</p>
					<div className="text-[#F00000] bg-[#FFEBEB] w-fit p-3 rounded-full">
						Not Verified
					</div>
				</div>

				<div className="flex items-center justify-between md:w-138 border border-[#E0E6ED] p-2 rounded-full">
					<p>Level 2 Verification</p>
					<div className="text-[#F00000] bg-[#FFEBEB] w-fit p-3 rounded-full">
						Not Verified
					</div>
				</div>

				<div className="md:w-138">
					<label
						htmlFor="bank-statement-upload"
						className="flex items-center justify-between border border-[#E0E6ED] p-4 rounded-full cursor-pointer w-full"
					>
						<input
							type="file"
							id="bank-statement-upload"
							className="hidden"
							onChange={handleFileChange}
						/>
						<div className="flex items-center gap-2 w-full">
							<span className="text-gray-700 flex-1">
								{fileName ? (
									<strong>{fileName}</strong>
								) : (
									"Upload Bank Statement"
								)}
							</span>
							<img
								src="src/assets/images/upload.png"
								alt="upload"
								className="w-5 h-5"
							/>
						</div>
					</label>
				</div>
                <p className="text-gray-400">Must be your bank statement from the last 6 months</p>

                <p className="pt-5 uppercase text-primary font-bold text-2xl">We will proceed to remove the loan amount from your bank account when you fail to payback after the agreed duration.</p>

           <div className="flex items-center gap-2">
             <input type="checkbox" className="w-6 h-8" />
             <p>By ticking this, you agree to the above condition.</p>
           </div>
           <button type="submit" className="bg-primary text-white p-3 pr-12 pl-12 rounded-full">Proceed</button>
			</form>




		</div>
	);
}
