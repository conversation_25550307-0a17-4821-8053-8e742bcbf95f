import ClyphubActions from "./clyphub-actions";

const newsItem = [
  {
    id: "1",
    main: "Ethereum all time high",
    sub: "Ethereum set to reach an all time high of $32000."
  },
  
    {
    id: "2",
    main: "Ethereum all time high",
    sub: "Ethereum set to reach an all time high of $32000."
  },

    {
    id: "3",
    main: "Ethereum all time high",
    sub: "Ethereum set to reach an all time high of $32000."
  },

    {
    id: "4",
    main: "Ethereum all time high",
    sub: "Ethereum set to reach an all time high of $32000."
  },

      {
    id: "5",
    main: "Ethereum all time high",
    sub: "Ethereum set to reach an all time high of $32000."
  },

        {
    id: "6",
    main: "Ethereum all time high",
    sub: "Ethereum set to reach an all time high of $32000."
  },
]

export default function ClyphubPage() {
  return (
    <div className="container mx-auto sm:px-4 ">
      <div className="flex items-center gap-1 w-full border-2 border-gray-500 p-4 rounded-full">
        <img src="src\assets\images\search.png" alt="search icon" />
        <input type="search" className="w-full border-0" placeholder="Search" />
      </div>
      <h1 className="text-3xl font-bold mb-6 pt-4">Clyp News</h1>

            <div className="p-6">
        <div className="flex gap-6 overflow-x-auto">
          {newsItem.map((item) => (
            <div
              key={item.id}
              className="min-w-[400px] max-w-[300px] flex-shrink-0 p-4"
            >
              <img
                src="src/assets/images/news-placeholder.png"
                alt="News"
                className="w-full h-40 object-cover rounded-lg"
              />
              <div className="mt-4">
                <div className="text-lg font-semibold">{item.main}</div>
                <p className="text-sm text-gray-700 mt-2">{item.sub}</p>
              </div>
              <button className="mt-4 text-white bg-[#25292D] rounded-full py-2 px-4 cursor-pointer hover:bg-gray-700">
                Read More
              </button>
            </div>
          ))}
        </div>
      </div>

      <p className="text-primary w-fit place-self-end pt-8 pb-8 cursor-pointer">See all</p>

      <ClyphubActions />
    </div>
  );
}
