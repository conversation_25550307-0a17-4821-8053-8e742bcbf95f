import PageHeader from '@/components/PageHeader'
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { cn } from '@/lib/utils';
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { SuccessDrawer } from "@/components/SuccessDrawer";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarDays } from "lucide-react"

export const AccountStatement = () => {

  const { openDrawer, closeDrawer } = useDrawer();

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Account Statement" />

      <p className="mb-6 text-gray-500">
        Request an account statement of all transactions of your Clyppay crypto and fiat accounts here.
      </p>

      <form action="" className='space-y-6'>
        <Select>
          <SelectTrigger className="w-full rounded-full p-7 cursor-pointer" aria-label="Select a value">
            <SelectValue placeholder="Choose Account Type" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="debit-transaction">
              Fiat Account
            </SelectItem>
          </SelectContent>
        </Select>

        <Select>
          <SelectTrigger className="w-full rounded-full p-7 cursor-pointer" aria-label="Select a value">
            <SelectValue placeholder="Choose Account" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="debit-transaction">
              American Dollar
            </SelectItem>
          </SelectContent>
        </Select>

        <div className="grid grid-cols-2 gap-4">

          <Popover>

            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-between text-left rounded-full p-6 text-gray-500",
                  // !date && "text-muted-foreground"
                )}
              >
                Start Date
                <CalendarDays className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent align="start" side="bottom">
              {/* <Calendar
                    mode="single"
                    // selected={date}
                    initialFocus
                  /> */}
            </PopoverContent>
          </Popover>

          <Popover>

            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-between text-left rounded-full p-6 text-gray-500",
                  // !date && "text-muted-foreground"
                )}
              >
                End Date
                <CalendarDays className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent align="start" side="bottom">
              {/* <Calendar
                    mode="single"
                    // selected={date}
                    initialFocus
                  /> */}
            </PopoverContent>
          </Popover>
        </div>

        <Select>
          <SelectTrigger className="w-full rounded-full p-7 cursor-pointer" aria-label="Select a value">
            <SelectValue placeholder="Document Format" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="debit-transaction">
              PDf
            </SelectItem>

            <SelectItem value="debit-transaction">
              Excel Spreadsheet
            </SelectItem>
          </SelectContent>
        </Select>


        <Button
          type="button"
          role="button"
          className={cn(
            "w-[40%] text-white font-medium py-8 px-4 rounded-full transition-colors",
          )}
          onClick={() =>
            openDrawer({
              view: (
                <SuccessDrawer
                  description={" Hi Mary, your account statement has been sent to your email address successfully!"}
                  title={"Account Statement Generated Successfully"}
                  buttonText={"Go to  Settings"}
                  route="/profile"
                  closeDrawer={closeDrawer}
                />
              ),
              placement: "right",
              customSize: "400px",
            })
          }
        >
          Generate Statement
        </Button>
      </form>
    </div>
  )
}