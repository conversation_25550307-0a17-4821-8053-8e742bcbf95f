import PageHeader from "@/components/PageHeader"
import { useNavigate } from "react-router-dom";

const verifications = [
  { label: "Level 1 Verification", path: "/account-settings/account-limit", status: "Verified" },
  { label: "Level 2 Verification", path: "/account-settings/account-limit", status: "Verified" },
  { label: "Level 3 Verification", path: "/account-settings/account-limit", status: "Not Verified" },
  { label: "Level 4 Verification", path: "/account-settings/account-limit", status: "Not Verified" },
  { label: "Level 5 Verification", path: "/account-settings/account-limit", status: "Not Verified" },
];

export const AccountLimit = () => {

  const navigate = useNavigate();

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Account Limit" />

      <p className="mb-6 text-gray-500">
        Increase your account limits by completing all the verifications required.
      </p>

      <div className="space-y-4">
        {verifications.map((item, idx) => (
          <div
            key={idx}
            className="flex items-center justify-between rounded-full border px-4 py-3 bg-white dark:bg-background cursor-pointer"
            onClick={() => navigate(item.path)}
          >
            <div>
              <div className="font-bold text-base">{item.label}</div>
            </div>

            <span className={`${item.status === "Verified" ? 'bg-green-200 text-green-600': 'text-[#F00000] bg-[#FFEBEB]'} p-4 py-2  text-xs rounded-full`}>
              {item.status}
            </span>

          </div>
        ))}
      </div>
    </div>
  )
}