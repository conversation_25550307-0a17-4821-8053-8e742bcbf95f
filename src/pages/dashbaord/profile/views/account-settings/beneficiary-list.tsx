import PageHeader from "@/components/PageHeader";
import { Trash2 } from "lucide-react";

const beneficiaries = [
  {
    name: "<PERSON><PERSON><PERSON>",
    accountNumber: "**********",
    bank: "United Bank of Africa",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    accountNumber: "**********",
    bank: "United Bank of Africa",
  },
  {
    name: "Adeyemi Akitoye",
    accountNumber: "**********",
    bank: "United Bank of Africa",
  },
];

export const BeneficiaryList = () => {
  return (
    <div className="max-w-3xl mx-auto py-8">
      <PageHeader title="Beneficiary List" />
      <p className="mb-6 text-gray-500">
        Here&apos;s a list of all your beneficiaries.
      </p>

      <div className="space-y-6">
        {beneficiaries.map((b, idx) => (
          <div
            key={idx}
            className="flex items-center justify-between rounded-full border px-8 py-2 bg-white dark:bg-background"
          >
            <div>
              <div className="font-bold text-base">{b.name}</div>
              <div className="text-gray-500 text-sm">
                {b.accountNumber}
              </div>
              <div className="text-gray-400 text-xs">{b.bank}</div>
            </div>
            <button
              type="button"
              className="cursor-pointer text-primary hover:bg-orange-50 rounded-full p-2 transition"
              aria-label="Remove beneficiary"
            >
              <Trash2 size={20} />
            </button>
          </div>
        ))}
      </div>
    </div>


  )
};