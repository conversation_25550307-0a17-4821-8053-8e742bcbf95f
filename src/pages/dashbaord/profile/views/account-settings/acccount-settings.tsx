import { Link } from "react-router-dom";
import {IoChevronForward } from "react-icons/io5";
import PageHeader from "@/components/PageHeader";
import { RiMoneyDollarCircleLine } from "react-icons/ri";
import { CiViewList } from "react-icons/ci";
import { MdOutlineManageAccounts } from "react-icons/md";





const supportItems = [
  {
    label: "Account Statement",
    icon: <MdOutlineManageAccounts size={20} className="text-gray-500" />,
    to: "/account-settings/account-statement",
  },
  {
    label: "Account Limit",
    icon: <RiMoneyDollarCircleLine size={20} className="text-gray-500" />,
    to: "/account-settings/account-limit",
  },
  {
    label: "Beneficiary List",
    icon: <CiViewList size={20} className="text-gray-500" />,
    to: "/account-settings/beneficiary-list",
  },
];

export const AccountSettings = () => {
  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Account Settings" />
      <div className="bg-white dark:bg-background border rounded-xl overflow-hidden mb-6">
        {supportItems.map((item, idx) => (
          <Link
            key={item.label}
            to={item.to}
            className={`flex items-center justify-between px-6 py-5 ${
              idx !== supportItems.length - 1 ? "border-b" : ""
            }`}
          >
            <div className="flex items-center gap-3">
              {item.icon}
              <span className="font-semibold text-base">{item.label}</span>
            </div>
            <IoChevronForward />
          </Link>
        ))}
      </div>
    </div>
  );
};