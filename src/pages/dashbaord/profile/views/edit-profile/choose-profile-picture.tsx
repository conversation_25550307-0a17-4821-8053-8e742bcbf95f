import { Button } from '@/components/custom/button'
import { useAvailableProfileImages, useUploadProfileImageAvatar } from '@/hooks/api/user'
import cn from '@/utils/class-names'
import { useState } from 'react'
import { useModal } from '@/components/modal-view/use-modal'

export const ChooseProfilePicture = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const { closeModal } = useModal()

  const { data: availableAvatarImageData, isPending: isFetchingProfileImageAvailable } = useAvailableProfileImages()
  const { mutate: updateAvatar, isPending: isUpdatingAvatar } = useUploadProfileImageAvatar()

  const handleImageSelect = (imageUrl: string) => {
    setSelectedImage(imageUrl)
  }

  const handleUpdateAvatar = () => {
    if (selectedImage) {
      updateAvatar(selectedImage, {
        onSuccess: () => {
          closeModal()
        }
      })
    }
  }

  return (
    <>
      {isFetchingProfileImageAvailable ? (
        <div className="flex items-center justify-center h-96">
          <p>loading...</p>
        </div>
      ) : (
        <div className='p-6 flex items-center flex-col'>
          <h2 className="text-xl font-semibold text-start mb-4">Choose New Profile Photo</h2>

          <div className="grid grid-cols-3 gap-4">
            {availableAvatarImageData?.data?.profilePics?.map((item: any, index: number) => (
              <div
                key={index}
                className={cn(
                  'rounded-xl border cursor-pointer transition-colors p-2',
                  selectedImage === item
                    ? 'bg-primary border-primary'
                    : 'bg-primary/40 hover:bg-primary/60'
                )}
                onClick={() => handleImageSelect(item)}
              >
                <img src={item} alt={`Profile option ${index + 1}`} className='w-20 h-20 object-cover rounded-lg' />
              </div>
            ))}
          </div>


          <div className="flex items-center justify-center mt-6">
            <Button
              type="button"
              role="button"
              onClick={handleUpdateAvatar}
              disabled={!selectedImage || isUpdatingAvatar}
              className={cn(
                "w-full cursor-pointer text-white font-medium py-3 px-4 rounded-full transition-colors",
                (!selectedImage || isUpdatingAvatar) && "opacity-50 cursor-not-allowed"
              )}
            >
              {isUpdatingAvatar ? 'Updating...' : 'Choose Photo'}
            </Button>
          </div>

        </div>
      )}

    </>

  )
}