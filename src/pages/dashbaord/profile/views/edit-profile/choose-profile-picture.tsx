import { Button } from '@/components/custom/button'
import { useAvailableProfileImages, useUploadProfileImageAvatar } from '@/hooks/api/user'
import cn from '@/utils/class-names'

export const ChooseProfilePicture = () => {

  const {data: availableAvatarImageData, isPending:isFetchingProfileImageAvailable} = useAvailableProfileImages()
  const {mutate:updateAvatar, isPending:isUpdatingAvatar} = useUploadProfileImageAvatar()

  const handleUpdateAvatar = async (e:any) => {
    await updateAvatar();
  }

  return (
    <>
    {isFetchingProfileImageAvailable ? (
      <p>loading...</p>
    ) : (

    <div className='p-6 flex items-center flex-col'>
      <h2 className="text-xl font-semibold text-start mb-4">Choose New Profile Photo</h2>

      <div className="grid grid-cols-3 gap-4">
        {availableAvatarImageData?.data?.profilePics?.map((item:any, index:number)=>(
          <div key={index} className='rounded-xl border bg-primary/40 cursor-pointer hover:bg-primary'>
            <img src={item} alt={item} className='w-24'/>
          </div>
        ))}
      </div>


      <div className="flex items-center justify-center mt-6">
        <Button
          type="button"
          role="button"
          className={cn(
            "w-full cursor-pointer text-white font-medium py-3 px-4 rounded-full transition-colors",
          )}
        >
          Choose Photo
        </Button>
      </div>

    </div>
    )}

    </>

  )
}