import { But<PERSON> } from "@/components/ui/button";
import React, { useState } from "react";
import { PencilLine } from "lucide-react";
import EditProfileField from "@/components/profile-component/EditProfileField";
import useUserStore from "@/store/user-store";
import { useModal } from "@/components/modal-view/use-modal";
import { ChooseProfilePicture } from "./choose-profile-picture";

const mockProfile = {
  firstName: "Mary",
  lastName: "<PERSON>eye<PERSON>",
  email: "Mary<PERSON>deye<PERSON>@gmail.com",
  phone: "093839039393",
  avatar: "https://i.pravatar.cc/150?img=5",
  verified: true,
};

export const EditProfile = () => {
  const {user} = useUserStore();

  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [isEditingPhone, setIsEditingPhone] = useState(false);
  const [emailValue, setEmailValue] = useState(user.email);
  const [phoneValue, setPhoneValue] = useState(user.phone);

    const { openModal } = useModal();
  
    const handleOpenModal = () => {
      openModal({
        view: <ChooseProfilePicture />,
        size: "lg",
        position: "center",
        animation: "fade",
        closeOnOutsideClick: true,
      });
    };


  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-semibold">Edit Profile</h1>

      {/* Profile Card */}
      <div className=" p-6 rounded-xl space-y-6 border flex justify-center flex-col items-center">
        <div className="flex items-center gap-6">
          <img
            src={user?.picture}
            alt="Avatar"
            className="w-24 h-24 rounded-full"
          />
          <h2 className="text-xl font-semibold">
            {user?.first_name} {user?.last_name}
          </h2>
          {user.activated && (
            <span className="p-4 py-2 bg-green-200 text-green-600 text-xs rounded-full">
              Verified Account
            </span>
          )}
        </div>
        <Button onClick={handleOpenModal} variant="outline" size="lg" className="rounded-full font-light text-gray-500">
          Edit Image <PencilLine />
        </Button>
      </div>

      {/* Personal Info */}
      <div className="p-6 rounded-xl border space-y-10">
        <h3 className="text-lg font-semibold">Personal Information</h3>

        {isEditingEmail || isEditingPhone ? (
          // Only show the email edit UI
          <div>
            {isEditingEmail && (
              <EditProfileField
                label="Email"
                type="email"
                value={emailValue}
                onChange={(e: any) => setEmailValue(e.target.value)}
                onCancel={() => setIsEditingEmail(false)}
                onSave={() => {
                  // handle save logic here
                  setIsEditingEmail(false);
                }}
              />
            )}

            {isEditingPhone && (
              <EditProfileField
                label="Phone"
                type="tel"
                value={phoneValue}
                onChange={(e : any) => setPhoneValue(e.target.value)}
                onCancel={() => setIsEditingPhone(false)}
                onSave={() => {
                  // handle save logic here
                  setIsEditingPhone(false);
                }}
              />
            )}
          </div>
        ) : (
          // Show all personal info sections
          <>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-light text-gray-500">First Name</div>
                <div className="font-semibold">{user.first_name}</div>
              </div>
              <div>
                <div className="font-light text-gray-500">Last Name</div>
                <div className="font-semibold">{user.last_name}</div>
              </div>
              <Button variant="outline" size="lg" className="rounded-full font-light text-gray-500">
                Edit <PencilLine />
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-light text-gray-500">Email Address</div>
                <div className="font-semibold">{emailValue}</div>
              </div>
              <Button
                variant="outline"
                size="lg"
                className="rounded-full font-light text-gray-500"
                onClick={() => setIsEditingEmail(true)}
              >
                Edit <PencilLine />
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-light text-gray-500">Phone</div>
                <div className="font-semibold">{phoneValue}</div>
              </div>
              <Button
                variant="outline"
                size="lg"
                className="rounded-full font-light text-gray-500"
                onClick={() => setIsEditingPhone(true)}
              >
                Edit <PencilLine />
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
