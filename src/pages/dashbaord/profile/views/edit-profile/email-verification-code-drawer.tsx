import { X } from "lucide-react";
import cn from "@/utils/class-names";
import { Button } from "@/components/ui/button";
import OTPInput from "@/components/custom/input/otp-input";
import { useState } from "react";
import { useConfirmEmailChange } from "@/hooks/api/user";

interface CompleteEmailVerificationDrawerProps {
  title: string;
  description: string;
  buttonText: string;
  closeDrawer?: () => void;
  newEmail?: string;
}

export const EmailVerificationDrawer = ({
  title,
  description,
  buttonText,
  closeDrawer,
  newEmail,
}: CompleteEmailVerificationDrawerProps) => {
  const [pin, setPin] = useState<string>("");
  const { mutate: confirmEmailChange, isPending } = useConfirmEmailChange();

  const handlePinChange = (value: string) => {
    setPin(value);
  };

  const handlePinComplete = (value: string) => {
    console.log("PIN completed:", value);
  };

  const handleCompleteEmailVerification = async () => {
    if (pin.length !== 4 || !newEmail) return;

    confirmEmailChange(
      { 
        authToken: pin, 
        newEmail: newEmail 
      },
      {
        onSuccess: () => {
          if (closeDrawer) {
            closeDrawer();
          }
        }
      }
    );
  };

  return (
    <div className="mt-12 flex flex-col items-center px-6 pt-8 pb-12 max-w-md mx-auto relative">
      <button
        className="cursor-pointer absolute top-6 right-6 text-primary border-2 border-primary rounded-full p-1"
        onClick={closeDrawer}
      >
        <X size={20} />
      </button>

      <h2 className="text-xl font-semibold text-center mt-10">{title}</h2>
      <p className="text-gray-500 text-center sm:mb-16">{description}</p>

      <div className="mb-8">
        <OTPInput
          length={4}
          value={pin}
          onChange={handlePinChange}
          onComplete={handlePinComplete}
          autoFocus
        />
      </div>

      <Button
        className={cn(
          "w-[50%] text-white font-medium py-6 px-4 rounded-full transition-colors",
        )}
        onClick={handleCompleteEmailVerification}
        disabled={pin.length !== 4 || isPending}
      >
        {isPending ? "Verifying..." : buttonText}
      </Button>
    </div>
  );
};
