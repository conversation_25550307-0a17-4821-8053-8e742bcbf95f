import { Upload } from "lucide-react";
import { useRef } from "react";
import PageHeader from '@/components/PageHeader'
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { SuccessDrawer } from "@/components/SuccessDrawer";

export const ChangeName = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { openDrawer, closeDrawer } = useDrawer();

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Change Name" />

      <p className="mb-6 text-gray-500">
        Change your Clyppay official account name here. <br />
        Note that you are to provide an official document stating your change of name, either a court affidavit or a newspaper publication.
      </p>

      <form action="" className='space-y-6'>
        <div className="w-full flex flex-col items-center">
          <label className="text-gray-500 mb-1 w-full text-left">Current Name</label>
          <Input
            type={"text"}
            placeholder='Enter current name'
            // value={""}
            // onChange={onChange}
            className="rounded-full p-6 focus-visible:ring-0"
          />
        </div>

        <div className="w-full flex flex-col items-center">
          <label className="text-gray-500 mb-1 w-full text-left">New Name</label>
          <Input
            type={"text"}
            placeholder='Enter new name'
            // value={""}
            // onChange={onChange}
            className="rounded-full p-6 focus-visible:ring-0"
          />
        </div>

        {/* Custom File Upload */}
        <div
          className="w-full rounded-full border flex items-center justify-between px-8 py-4 cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
        >
          <span className="text-gray-500 text-sm">
            Upload file or screenshot (optional)
          </span>
          <Upload className="text-gray-400" />
          <Input
            ref={fileInputRef}
            type="file"
            className="hidden"
          />
        </div>

        <textarea
          placeholder='Enter details explaining your issue'
          className="w-full px-3 sm:px-4 py-3 pr-10 sm:pr-12 rounded-2xl border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent resize-none text-sm sm:text-base"
          rows={6}
        />

        <Button
          type="button"
          role="button"
          className={cn(
            "w-[40%] text-white font-medium py-8 px-4 rounded-full transition-colors",
          )}
          onClick={() =>
            openDrawer({
              view: (
                <SuccessDrawer
                  description={"Hi Mary, you successfully change your name!"}
                  title={"Name Changed Successfully!"}
                  buttonText={"Go to  Settings"}
                  route="/profile"
                  closeDrawer={closeDrawer}
                />
              ),
              placement: "right",
              customSize: "400px",
            })
          }
        >
          Change Name
        </Button>
      </form>
    </div>
  )
}