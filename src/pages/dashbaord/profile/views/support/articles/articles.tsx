import PageHeader from "@/components/PageHeader";
import { IoChevronForward } from "react-icons/io5";
import { useNavigate } from "react-router-dom";

const articleLinks = [
	{ label: "Crypto Articles", path: "/support/articles/sub-articles" },
	{ label: "Currency Articles", path: "/support/articles/sub-articles" },
	{ label: "Clyppay Articles", path: "/support/articles/sub-articles" },
	{ label: "Change Account Name", path: "/support/articles/sub-articles" },
];

export const Articles = () => {
	const navigate = useNavigate();

	return (
		<div className="max-w-xl mx-auto">
			<PageHeader title="Useful Articles" />

			<p className="mb-6 text-gray-500">
				Read value packed articles ranging from crypto, to currency, to exchange
				and all you need to know about Clyppay.
			</p>

			<div className="space-y-4">
				{articleLinks.map((item, idx) => (
					<div
						key={item.label}
						className="flex items-center justify-between rounded-full border px-4 py-4 bg-white dark:bg-background cursor-pointer"
						onClick={() => navigate(item.path)}
					>
						<div>
							<div className="font-bold text-base">{item.label}</div>
						</div>
						<IoChevronForward />
					</div>
				))}
			</div>
		</div>
	);
};