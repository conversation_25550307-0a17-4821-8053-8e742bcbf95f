import { useParams } from "react-router-dom";

// You can import articles from a shared file or define them here
import { articles } from "./sub-articles"; // adjust import as needed
import PageHeader from "@/components/PageHeader";
import { Button } from "@/components/custom/button";

export const ArticleDetail = () => {
  const { slug } = useParams<{ slug: string }>();

  const article = articles.find(
    (a) => a.title.toLowerCase().replace(/\s+/g, "-") === slug
  );

  if (!article) {
    return (
      <div className="p-8">
      <PageHeader />

        <div className="text-xl font-semibold text-center">Article not found.</div>
      </div>
    );
  }

  return (
    <div className="px-4">
      <PageHeader title={article.title} />

      <div className="flex flex-wrap gap-2 mb-4">
        {article.tags.map((tag) => (
          <span
            key={tag}
            className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-white px-3 py-1 rounded-full text-xs font-medium"
          >
            {tag}
          </span>
        ))}
      </div>
      <div className="flex gap-8 mb-2 text-gray-500 text-sm">
        <span>{article.readTime}</span>
        <span>{article.date}</span>
      </div>
      <div className="mb-6">
        <img
          src={article.image}
          alt={article.title}
          className="rounded-t-2xl w-full max-h-[350px] object-cover"
        />
      </div>
      <div className="text-gray-700 dark:text-gray-400 mb-8 whitespace-pre-line">
        {article.content}
      </div>
      <Button className="!bg-[#25292D] w-60 cursor-pointer">
        Share Article
      </Button>
    </div>
  );
};