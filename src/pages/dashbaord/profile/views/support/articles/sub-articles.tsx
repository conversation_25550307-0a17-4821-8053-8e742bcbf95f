import { IoChevronBack } from "react-icons/io5";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import PageHeader from "@/components/PageHeader";
import { Button } from "@/components/custom/button";

// Export articles for use in ArticleDetail
export const articles = [
	{
		id: 1,
		title: "The Future of Cryptocurrency",
		summary:
			"Cryptocurrency was not really looked at when it started, however, times are changing",
		tags: ["Cryptocurrency", "Bitcoin", "Finance", "Payments"],
		readTime: "7 mins read",
		date: "12/05/2024",
		image:
			"https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80",
		content: `Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.

Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.`,
	},
	{
		id: 2,
		title: "The Future of Cryptocurrency",
		summary:
			"Cryptocurrency was not really looked at when it started, however, times are changing",
		tags: ["Cryptocurrency", "Bitcoin", "Finance", "Payments"],
		readTime: "7 mins read",
		date: "12/05/2024",
		image:
			"https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80",
		content: `Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.

Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.`,
	},
	{
		id: 3,
		title: "The Future of Cryptocurrency",
		summary:
			"Cryptocurrency was not really looked at when it started, however, times are changing",
		tags: ["Cryptocurrency", "Bitcoin", "Finance", "Payments"],
		readTime: "7 mins read",
		date: "12/05/2024",
		image:
			"https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80",
		content: `Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.

Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.`,
	},
	{
		id: 4,
		title: "The Future of Cryptocurrency",
		summary:
			"Cryptocurrency was not really looked at when it started, however, times are changing",
		tags: ["Cryptocurrency", "Bitcoin", "Finance", "Payments"],
		readTime: "7 mins read",
		date: "12/05/2024",
		image:
			"https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80",
		content: `Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.

Cryptocurrency was not really looked at when it started, however, times are changing
Analysts estimate that the global cryptocurrency market will more than triple. Whether they want to buy into it or not, investors, businesses, and brands can't ignore the rising tide of crypto for long. But crypto can't seem to escape paradoxes anywhere.`,
	},
];

export const SubArticles = () => {
	const [search, setSearch] = useState("");
	const navigate = useNavigate();

	const filteredArticles = articles.filter((article) =>
		article.title.toLowerCase().includes(search.toLowerCase()) ||
		article.summary.toLowerCase().includes(search.toLowerCase())
	);

	return (
		<div className="sm:px-6">
			<PageHeader title="Crypto Articles" />

			<div className="mb-8">
				<input
					type="text"
					placeholder="Search"
					value={search}
					onChange={(e) => setSearch(e.target.value)}
					className="w-full rounded-full border px-6 py-3 focus:outline-none focus:ring-1 focus:ring-primary placeholder:text-sm text-sm"
				/>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
				{filteredArticles.map((article) => (
					<div
						key={article.id}
						className="bg-white dark:bg-background rounded-2xl overflow-hidden shadow-sm flex flex-col border"
					>
						<img
							src={article.image}
							alt={article.title}
							className="w-full h-58 object-cover rounded-t-2xl"
						/>
						<div className="p-5 flex flex-col flex-1">
							<h3 className="font-bold text-lg mb-1">{article.title}</h3>
							<p className="text-gray-600 mb-2 text-sm">{article.summary}</p>
							<div className="flex flex-wrap gap-2 mb-2">
								{article.tags.map((tag) => (
									<span
										key={tag}
										className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-white px-3 py-1 rounded-full text-xs font-thin"
									>
										{tag}
									</span>
								))}
							</div>
							<div className="flex gap-8 mb-4 text-gray-500 text-xs">
								<span>{article.readTime}</span>
								<span>{article.date}</span>
							</div>
							<Button
								className="cursor-pointer"
								variant="secondary"
								onClick={() =>
									navigate(
										`/support/articles/sub-articles/${article.title
											.toLowerCase()
											.replace(/\s+/g, "-")}`
									)
								}
							>
								Read Article
							</Button>
						</div>
					</div>
				))}
			</div>
		</div>
	);
};