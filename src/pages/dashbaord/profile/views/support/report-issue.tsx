import { Upload } from "lucide-react";
import { useRef } from "react";
import PageHeader from '@/components/PageHeader'
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { cn } from '@/lib/utils';
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { SuccessDrawer } from "@/components/SuccessDrawer";

export const ReportIssue = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);

    const { openDrawer, closeDrawer } = useDrawer();

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Report an issue" />

      <form action="" className='space-y-6'>
        <Select>
          <SelectTrigger className="w-full rounded-full p-7 " aria-label="Select a value">
            <SelectValue placeholder="Choose Issue" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="debit-transaction">
              Debit Transaction
            </SelectItem>
          </SelectContent>
        </Select>

        {/* Custom File Upload */}
        <div
          className="w-full rounded-full border flex items-center justify-between px-8 py-4 cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
        >
          <span className="text-gray-500 text-sm">
            Upload file or screenshot (optional)
          </span>
          <Upload className="text-gray-400" />
          <Input
            ref={fileInputRef}
            type="file"
            className="hidden"
          />
        </div>

        <textarea
          placeholder='Enter details explaining your issue'
          className="w-full px-3 sm:px-4 py-3 pr-10 sm:pr-12 rounded-2xl border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent resize-none text-sm sm:text-base"
          rows={6}
        />

        <Button
          type="button"
          role="button"
          className={cn(
            "w-[40%] text-white font-medium py-8 px-4 rounded-full transition-colors",
          )}
          onClick={() =>
            openDrawer({
              view: (
                <SuccessDrawer
                  description={"Hi Mary, your issue has been reported successfully!"}
                  title={"Issue Reported Successfully!"}
                  buttonText={"Go to  Settings"}
                  route="/profile"
                  closeDrawer={closeDrawer}
                />
              ),
              placement: "right",
              customSize: "400px",
            })
          }
        >
          Report Issue
        </Button>
      </form>
    </div>
  )
}