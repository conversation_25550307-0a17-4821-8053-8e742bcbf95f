import PageHeader from "@/components/PageHeader";
import React, { useState } from "react";
import { IoChe<PERSON><PERSON>Forward, IoChevronDown } from "react-icons/io5";

const faqs = [
  {
    question: "How do I create my Clyppay account?",
    answer: "Easily download the Clyppay app on Playstore and Appstore and create your Clyppay account.",
  },
  {
    question: "How do I create my Clyppay account?",
    answer: "Easily download the Clyppay app on Playstore and Appstore and create your Clyppay account.",
  },
  {
    question: "How do I create my Clyppay account?",
    answer: "Easily download the Clyppay app on Playstore and Appstore and create your Clyppay account.",
  },
  {
    question: "How do I create my Clyppay account?",
    answer: "Easily download the Clyppay app on Playstore and Appstore and create your Clyppay account.",
  },
];

const Faq = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleToggle = (idx: number) => {
    setOpenIndex(openIndex === idx ? null : idx);
  };

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="FAQs" />
      <div className="max-w-2xl mx-auto space-y-4">
        {faqs.map((faq, idx) => (
          <div
            key={idx}
            className={`border rounded-2xl px-6 py-5 transition-all duration-200`}
          >
            <button
              className="cursor-pointer w-full flex items-center justify-between focus:outline-none"
              onClick={() => handleToggle(idx)}
            >
              <span
                className={`text-lg font-medium text-left ${openIndex === idx ? "text-primary font-semibold" : "text-gray-500"
                  }`}
              >
                {faq.question}
              </span>
              {openIndex === idx ? (
                <IoChevronDown className="text-primary" />
              ) : (
                <IoChevronForward className="text-gray-500" />
              )}
            </button>
            {openIndex === idx && (
              <div className="mt-3 text-gray-500 text-base">
                {faq.answer}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>

  );
};

export default Faq;