import PageHeader from "@/components/PageHeader";
import { useState } from "react";
import { IoChevronForward, IoChevronDown } from "react-icons/io5";

const faqs = [
  {
    question: "How do i signup for Clyp Pay ?",
    answer: `Signing up is simple. Download our app, click on "Sign Up," and follow the instructions to create your account`,
  },
  {
    question: "What type of cryptocurrencies can i trade on Clyp Pay ?",
    answer: "You can trade popular cryptocurrencies like Bitcoin, Ethereum, and more. We regularly update our list of supported currencies.",
  },
  {
    question: "Can i use Clyp Pay for everyday transactions ?",
    answer: "Yes! With our daily transaction feature, you can pay for goods and services using your crypto balance directly.",
  },
  {
    question: `What is Crypto "Bank Statement"?`,
    answer: `Our Crypto "Bank Statement" provides a detailed record of your transactions, helping you keep track of your crypto activities.`,
  },
  {
    question: `How do i covert my crypto to cash ?`,
    answer: `You can easily convert your crypto to cash within the app. Just select the "Convert to Cash" option and follow the prompts.`,
  },
  {
    question: `Is my account secure ?`,
    answer: `Absolutely. We use advanced security protocols, including two-factor authentication, to protect your account and assets.`,
  },
  {
    question: `How can i join Clyp communities ?`,
    answer: `Join our exclusive Clyp Traders and Gamers Hubs on Telegram for insider tips, resources, and networking opportunities by simply clicking the "Join the Community" button`,
  },
  {
    question: `What are Unlimited Rewards ?`,
    answer: `Our rewards program offers you bonuses for transactions, referrals, and more. Check the "Rewards" section in the app for details.`,
  },
  {
    question: `How do i contact customer support ?`,
    answer: `You can reach us via <NAME_EMAIL> or by phone at ***********.

`,
  },
];

const Faq = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleToggle = (idx: number) => {
    setOpenIndex(openIndex === idx ? null : idx);
  };

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="FAQs" />

      <p className="mb-6 text-gray-600">
        We've compiled answers to the most frequently asked questions about Clyp Pay. Whether you're curious about getting started, navigating our platform, or specific features like Crypto Loans and Daily Transactions, we've got you covered. Explore our FAQs to get the help you need, fast.
      </p>

      <div className="max-w-2xl mx-auto space-y-4">
        {faqs.map((faq, idx) => (
          <div
            key={idx}
            className={`border rounded-2xl px-6 py-5 transition-all duration-200`}
          >
            <button
              className="cursor-pointer w-full flex items-center justify-between focus:outline-none"
              onClick={() => handleToggle(idx)}
            >
              <span
                className={`text-lg font-medium text-left ${openIndex === idx ? "text-primary font-semibold" : "text-gray-500"
                  }`}
              >
                {faq.question}
              </span>
              {openIndex === idx ? (
                <IoChevronDown className="text-primary" />
              ) : (
                <IoChevronForward className="text-gray-500" />
              )}
            </button>
            {openIndex === idx && (
              <div className="mt-3 text-gray-500 text-base">
                {faq.answer}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>

  );
};

export default Faq;