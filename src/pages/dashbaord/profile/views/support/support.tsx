import { Link } from "react-router-dom";
import { GoQuestion } from "react-icons/go";
import { IoChatbubbleEllipsesOutline, IoReceiptOutline, IoChevronBack, IoChevronForward } from "react-icons/io5";
import { PiPencilSimpleLineLight } from "react-icons/pi";
import PageHeader from "@/components/PageHeader";


const supportItems = [
	{
		label: "FAQs",
		icon: <GoQuestion size={20} className="text-gray-500" />,
		to: "/support/faq",
	},
	{
		label: "Report an Issue",
		icon: <IoChatbubbleEllipsesOutline size={20} className="text-gray-500" />,
		to: "/support/report-issue",
	},
	{
		label: "Useful Articles",
		icon: <IoReceiptOutline size={20} className="text-gray-500" />,
		to: "/support/articles",
	},
	{
		label: "Change Account Name",
		icon: <PiPencilSimpleLineLight size={20} className="text-gray-500" />,
		to: "/support/change-account-name",
	},
];

export const Support = () => {
	return (
		<div className="max-w-xl mx-auto">
			<PageHeader title="Support" />
			<div className="bg-white dark:bg-background border rounded-xl overflow-hidden mb-6">
				{supportItems.map((item, idx) => (
					<Link
						key={item.label}
						to={item.to}
						className={`flex items-center justify-between px-6 py-5 ${
							idx !== supportItems.length - 1 ? "border-b" : ""
						}`}
					>
						<div className="flex items-center gap-3">
							{item.icon}
							<span className="font-semibold text-base">{item.label}</span>
						</div>
						<IoChevronForward />
					</Link>
				))}
			</div>
		</div>
	);
};