import { Button } from '@/components/custom/button'
import PageHeader from '@/components/PageHeader'
import useUserStore from '@/store/user-store';
import cn from '@/utils/class-names'
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>he<PERSON>, Share2 } from 'lucide-react';
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";

export const InviteFriend = () => {
  const { user } = useUserStore();
  const [isCopied, setIsCopied] = useState(false);
  
  const referralCode = user?.clyp_id || '';

  const copyReferralCode = () => {
    if (!referralCode) {
      notify("No referral code to copy.", ResponseStatus.ERROR);
      return;
    }

    navigator.clipboard
      .writeText(referralCode)
      .then(() => {
        notify(
          "Referral code copied to clipboard",
          ResponseStatus.SUCCESS,
        );
        
        setIsCopied(true);
        
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch(() => {
        notify(
          "An error occurred while copying referral code.",
          ResponseStatus.ERROR,
        );
      });
  };

  const shareReferralCode = async () => {
    if (!referralCode) {
      notify("No referral code to share.", ResponseStatus.ERROR);
      return;
    }

    const shareData = {
      title: 'Join me on Clyp',
      text: `Use my referral code ${referralCode} to sign up on Clyp and get special benefits!`,
      url: `https://clyp.com/signup?ref=${referralCode}`
    };

    try {
      if (navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        notify("Referral code shared successfully!", ResponseStatus.SUCCESS);
      } else {
        // Fallback for browsers that don't support Web Share API
        copyReferralCode();
        notify("Share not supported on this device. Referral code copied instead.", ResponseStatus.INFO);
      }
    } catch (error) {
      console.error("Error sharing:", error);
      // User likely canceled the share operation, so we don't show an error
    }
  };

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Invite a friend" />
      <div className="space-y-6">
        <div
          className="flex items-center justify-between rounded-full border px-8 py-2 bg-white dark:bg-background"
        >
          <div>
            <div className="font-bold text-sm">Your Referral Code</div>
            <div className="text-primary text-lg">
              {referralCode}
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-4 mt-10">
        <Button
          type="button"
          role="button"
          onClick={copyReferralCode}
          className={cn(
            "w-full cursor-pointer text-white font-medium py-4 px-4 rounded-full transition-colors",
            "flex items-center justify-center gap-2"
          )}
        >
          {isCopied ? (
            <>
              <CheckCheck size={20} />
              Copied!
            </>
          ) : (
            <>
              <Copy size={20} />
              Copy Referral Code
            </>
          )}
        </Button>

        <Button
          type="button"
          role="button"
          onClick={shareReferralCode}
          variant='secondary'
          className={cn(
            "w-full cursor-pointer text-primary font-medium py-4 px-4 rounded-full transition-colors",
            "flex items-center justify-center gap-2"
          )}
        >
          <Share2 size={20} />
          Share Referral Code
        </Button>
      </div>
    </div>
  )
}
