import PageHeader from '@/components/PageHeader'

export const PrivacyPolicy = () => {
  return (
    <div className="max-w-3xl mx-auto">
      <PageHeader title="Privacy Policy" />
      <div className="p-8 text-gray-700 dark:text-white space-y-8">

        <div>
          <h2 className="font-bold text-lg mb-2">1. Introduction</h2>
          <p>
            Clyppay ("we," "us," or "our") is committed to protecting your privacy.
            This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website
            <a href="https://www.clyppay.com" className="text-blue-600 underline ml-1" target="_blank" rel="noopener noreferrer">
              www.clyppay.com
            </a>
            and use our services. Please read this policy carefully to understand our views and practices regarding your personal data.
          </p>
        </div>

        <div>
          <h2 className="font-bold text-lg mb-2">2. Information We Collect</h2>
          <p>We may collect and process the following types of information:</p>
          <ul className="list-disc ml-6 mt-2 space-y-1">
            <li>
              <span className="font-medium">Personal Identification Information:</span> Name, email address, phone number, and other contact details.
            </li>
            <li>
              <span className="font-medium">Financial Information:</span> Transaction details, wallet addresses, and other financial data necessary for providing our services.
            </li>
            <li>
              <span className="font-medium">Technical Information:</span> IP address, browser type, operating system, and other technical data collected through cookies and similar technologies.
            </li>
          </ul>
        </div>

        <div>
          <h2 className="font-bold text-lg mb-2">3. How We Use Your Information</h2>
          <p>We use the information we collect to:</p>
          <ul className="list-disc ml-6 mt-2 space-y-1">
            <li>Provide, operate, and maintain our services.</li>
            <li>Improve, personalize, and expand our services.</li>
            <li>Communicate with you, including customer service and support.</li>
            <li>Process transactions and send related information.</li>
            <li>Prevent fraudulent transactions and enhance security.</li>
          </ul>
        </div>
      </div>
    </div>

  )
};