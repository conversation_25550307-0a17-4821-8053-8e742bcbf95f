import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import cn from "@/utils/class-names";
import PageHeader from '@/components/PageHeader';
import { useDrawer } from '@/components/drawer-view/use-drawer';
import { SuccessDrawer } from '@/components/SuccessDrawer';


export const ChangeTransactionPin = () => {
  const { openDrawer, closeDrawer } = useDrawer();

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Change Transaction Pin" />
      <p className="mb-6 text-gray-500">
        Input current transaction pin to change pin.
      </p>

      <div className='space-y-6'>

        <div className="w-full flex flex-col items-center">
          <label className="text-gray-500 mb-1 w-full text-left">Current Transaction Pin</label>
          <Input
            type={"password"}
            placeholder='Enter current transaction pin'
            // value={""}
            // onChange={onChange}
            className="rounded-full p-6 focus-visible:ring-0"
          />
        </div>

        <div className="w-full flex flex-col items-center">
          <label className="text-gray-500 mb-1 w-full text-left">New Transaction Pin</label>
          <Input
            type={"password"}
            // value={""}
            placeholder='Enter new transaction pin'
            // onChange={onChange}
            className="rounded-full p-6 focus-visible:ring-0"
          />
        </div>

        <div className="w-full flex flex-col items-center">
          <label className="text-gray-500 mb-1 w-full text-left">Confirm New Transaction Pin</label>
          <Input
            type={"password"}
            placeholder='Confirm transaction pin'
            // value={""}
            // onChange={onChange}
            className="rounded-full p-6 focus-visible:ring-0"
          />
        </div>

        <Button
          type="button"
          // disabled={isPending}
          className={cn(
            "w-[40%] text-white font-medium py-8 px-4 rounded-full transition-colors",
          )}
          onClick={() =>
            openDrawer({
              view: (
                <SuccessDrawer
                  description={"Hi Mary, your transaction pin has been changed successfully!"}
                  title={"Transaction Pin Changed Successfully!"}
                  buttonText={"View Profile"}
                  route="/profile"
                  closeDrawer={closeDrawer}
                />
              ),
              placement: "right",
              customSize: "400px",
            })
          }
        >
          Change Pin
        </Button>

      </div>
    </div>
  )
}