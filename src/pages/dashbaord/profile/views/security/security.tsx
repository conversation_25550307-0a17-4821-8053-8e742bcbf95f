import { Switch } from "@/components/ui/switch"
import { useState } from "react";
import { IoChevronForward } from "react-icons/io5";
import { Link } from "react-router-dom";
import { BsPinAngle } from "react-icons/bs";
import { PiUserFocus } from "react-icons/pi";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { BiometricDrawer } from "./BiometricDrawer";
import PageHeader from "@/components/PageHeader";


export const Security = () => {
  const [biometric, setBiometric] = useState(false);
  const { openDrawer } = useDrawer();


  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Security" />
      <div className="bg-white dark:bg-background border rounded-xl overflow-hidden mb-6">

        <Link to={"/security-settings/change-transaction-pin"} className="flex items-center justify-between px-6 py-5 border-b">
          <div className="flex items-center gap-3">
            <BsPinAngle size="20" className="text-gray-500" />
            <span className="font-semibold text-base">Change Transaction Pin</span>
          </div>
          <IoChevronForward />
        </Link>

        <div className="cursor-pointer flex items-center justify-between px-6 py-5"
          onClick={() =>
            openDrawer({
              view: (
                <BiometricDrawer
                />
              ),
              placement: "right",
              customSize: "400px",
            })
          }
        >
          <div className="flex items-center gap-3">
            <PiUserFocus size="20" className="text-gray-500" />
            <span className="font-semibold text-base">Biometric Login</span>
          </div>
          <Switch checked={biometric} onCheckedChange={setBiometric} />
        </div>

      </div>
    </div>
  );
}