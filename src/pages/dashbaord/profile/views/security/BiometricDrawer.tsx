import { useDrawer } from '@/components/drawer-view/use-drawer'
import { SuccessDrawer } from '@/components/SuccessDrawer'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import cn from '@/utils/class-names'

export const BiometricDrawer = () => {

  const { openDrawer, closeDrawer } = useDrawer();

  return (
    <>
      <div className="p-4 w-full max-w-md md:pt-[30%]">
        <div className="w-full flex flex-col items-center">
          <label className="text-gray-500 mb-1 w-full text-left">Password</label>
          <Input
            type={"password"}
            value={""}
            placeholder='Enter Password'
            // onChange={onChange}
            className="rounded-full p-6 focus-visible:ring-0"
          />
          <span className='text-[12px] text-start text-gray-500 mt-1'>Must be have  upper case, lower case , number and symbol.</span>
        </div>

        <div className="flex items-center justify-center mt-10">
          <Button
            type="button"
            size='sm'
            // disabled={isPending}
            className={cn(
              "w-[80%] text-white font-medium py-8 px-4 rounded-full transition-colors",
            )}

            onClick={() =>
              openDrawer({
                view: (
                  <SuccessDrawer
                    description={"Hi Mary, your biometric has been updated successfully!"}
                    title={"Biometric Login Activated Successfully!"}
                    buttonText={"View Profile"}
                    route="/profile"
                    closeDrawer={closeDrawer}
                  />
                ),
                placement: "right",
                customSize: "400px",
              })
            }
          >
            Activate Biometric Login
          </Button>
        </div>

      </div>
    </>
  )
}