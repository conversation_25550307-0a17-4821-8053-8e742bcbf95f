import PageHeader from "@/components/PageHeader";
import { Switch } from "@/components/ui/switch"
import { useNotificationSettings } from "@/hooks/api/notification";
import { Mail, Bell, MessageCircle, Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import useUserStore from "@/store/user-store";
import { NotificationSettings as NotificationSettingsType } from "@/types/notification";

// Define notification settings configuration
interface NotificationSetting {
  id: string;
  label: string;
  icon: React.ReactNode;
  stateKey: keyof NotificationSettingsType;
}

export default function NotificationSettings() {
  const { user } = useUserStore();
  const [settings, setSettings] = useState<NotificationSettingsType>({
    emailNotifSetting: false,
    pushNotificationSettings: false,
    priceAlertSettings: false
  });
  const [pendingSettings, setPendingSettings] = useState<string[]>([]);

  const { mutate: changeNotificationSettings } = useNotificationSettings();

  // Settings configuration
  const notificationSettings: NotificationSetting[] = [
    {
      id: 'email',
      label: 'Email Notifications',
      icon: <Mail size="20" className="text-gray-500" />,
      stateKey: 'emailNotifSetting'
    },
    {
      id: 'push',
      label: 'Push Notifications',
      icon: <Bell size="20" className="text-gray-500" />,
      stateKey: 'pushNotificationSettings'
    },
    {
      id: 'priceAlert',
      label: 'Price Alert Notifications',
      icon: <MessageCircle size="20" className="text-gray-500" />,
      stateKey: 'priceAlertSettings'
    }
  ];

  // Initialize state from user data
  useEffect(() => {
    if (user) {
      setSettings({
        emailNotifSetting: !!user.emailNotifSetting,
        pushNotificationSettings: !!user.pushNotificationSettings,
        priceAlertSettings: !!user.priceAlertSettings
      });
    }
  }, [user]);

  // Handle setting change
  const handleSettingChange = (id: string, stateKey: keyof NotificationSettingsType, checked: boolean) => {
    // Update local state
    setSettings(prev => ({
      ...prev,
      [stateKey]: checked
    }));
    
    // Add to pending settings
    setPendingSettings(prev => [...prev, id]);
    
    // Prepare payload
    const payload: NotificationSettingsType = {
      ...settings,
      [stateKey]: checked
    };

    // Call API
    changeNotificationSettings(payload, {
      onSettled: () => {
        setPendingSettings(prev => prev.filter(item => item !== id));
      }
    });
  };

  return (
    <div className="max-w-xl mx-auto">
      <PageHeader title="Notification Settings" />
      <p className="mb-6 text-gray-600">
        Choose what medium you prefer getting your notifications.
      </p>
      <div className="bg-white dark:bg-background border rounded-xl overflow-hidden mb-6">
        {notificationSettings.map((setting, index) => (
          <div 
            key={setting.id}
            className={`flex items-center justify-between px-6 py-5 ${
              index < notificationSettings.length - 1 ? 'border-b' : ''
            }`}
          >
            <div className="flex items-center gap-3">
              {setting.icon}
              <span className="font-semibold text-base">{setting.label}</span>
            </div>
            <div className="flex items-center">
              {pendingSettings.includes(setting.id) && (
                <Loader2 size="16" className="text-primary mr-2 animate-spin" />
              )}
              <Switch 
                checked={settings[setting.stateKey]}
                onCheckedChange={(checked) => handleSettingChange(setting.id, setting.stateKey, checked)}
                disabled={pendingSettings.includes(setting.id)}
                className="cursor-pointer"
              />
            </div>
          </div>
        ))}
      </div>
      <p className="text-gray-500 text-sm mb-6">
        <span className="font-semibold">Note:</span> Price Alerts are SMS alerts you get when turned on for a coin and you are charged 0.5 USD per SMS.
      </p>
    </div>
  );
}
