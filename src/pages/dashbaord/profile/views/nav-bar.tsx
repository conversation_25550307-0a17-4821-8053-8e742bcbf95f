import { useLocation, useNavigate } from "react-router-dom";
import { FaRegUser } from "react-icons/fa";
import { LiaUserEditSolid } from "react-icons/lia";
import { IoAlertCircleOutline } from "react-icons/io5";
import { GrBook } from "react-icons/gr";
import { PiShieldCheckBold, PiBell } from "react-icons/pi";
import { HiOutlineUserPlus } from "react-icons/hi2";
import { BiBookmarkAlt } from "react-icons/bi";
import { PiFiles } from "react-icons/pi";
import { VscColorMode } from "react-icons/vsc";
import useThemeStore from "@/store/theme-store";
import useUserStore from "@/store/user-store";
import { useToggleProMode } from "@/hooks/api/user";
import cn from "@/utils/class-names";

const navItems = [
  { name: "Pro Mode", type: "toggle", icon: <FaRegUser /> },
  { name: "Edit Profile", path: "/profile", icon: <LiaUserEditSolid /> },
  { name: "Support", path: "/support", icon: <IoAlertCircleOutline /> },
  { name: "Account Settings", path: "/account-settings", icon: <GrBook /> },
  { name: "Security", path: "/security-settings", icon: <PiShieldCheckBold /> },
  { name: "Notifications", path: "/notifications-settings", icon: <PiBell /> },
  { name: "Invite a Friend", path: "/invite", icon: <HiOutlineUserPlus /> },
  { name: "Privacy Policy", path: "/privacy-policy", icon: <BiBookmarkAlt /> },
  { name: "Terms and Conditions", path: "/terms-and-conditions", icon: <PiFiles /> },
  { name: "Dark Mode", icon: <VscColorMode />, type: "toggle" },
];

interface SideNavbarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

const SideNavbar = ({ isOpen = true, onClose }: SideNavbarProps) => {
  const { theme, setTheme } = useThemeStore();
  const { user } = useUserStore();
  const location = useLocation();
  const navigate = useNavigate();

  const {mutate:toggleProMode, isPending:isToggling} = useToggleProMode();

  const handleToggle = (itemName: string) => {
    if (itemName === "Pro Mode") {
      const newProModeValue = !user?.proMode;
      toggleProMode(newProModeValue);
    } else if (itemName === "Dark Mode") {
      setTheme(theme === "dark" ? "light" : "dark");
    }
  };

  const handleNavClick = (item: any) => {
    if (item.type === "toggle") {
      // Prevent toggle if Pro Mode is currently being updated
      if (item.name === "Pro Mode" && isToggling) {
        return;
      }
      handleToggle(item.name);
    } else if (item.path) {
      navigate(item.path);
      // Close mobile sidebar after navigation
      if (onClose && window.innerWidth < 768) {
        onClose();
      }
    }
  };

  return (
    <div className="w-64 bg-white dark:bg-background border-r p-4">
      <nav className="space-y-2">
        {navItems.map((item, index) => {
          // Use includes for active state
          const isActive = item.path && location.pathname.includes(item.path);
          return (
            <button
              key={index}
              onClick={() => handleNavClick(item)}
              className={`w-full cursor-pointer font-medium flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-200
                ${item.type === "toggle"
                  ? "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-muted/70 justify-between"
                  : isActive
                    ? "bg-primary/20 text-primary dark:bg-primary/20 !font-semibold"
                    : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-muted/70"
                }`}
            >
              {item.icon}
              <span className="text-left items-start">{item.name}</span>
              {item.type === "toggle" && item.name === "Dark Mode" && (
                <div
                  className={`w-10 h-6 rounded-full p-1 transition-colors duration-200 ${theme === "dark" ? "bg-blue-600" : "bg-gray-300"}`}
                  onClick={e => {
                    e.stopPropagation();
                    handleToggle(item.name);
                  }}
                >
                  <div className={`w-4 h-4 rounded-full bg-white transition-transform duration-200 ${theme === "dark" ? "translate-x-4" : "translate-x-0"}`} />
                </div>
              )}
              {item.type === "toggle" && item.name === "Pro Mode" && (
                <div
                  className={`w-10 h-6 rounded-full p-1 transition-colors duration-200 ${
                    user?.proMode ? "bg-blue-600" : "bg-gray-300"
                  } ${isToggling ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
                  onClick={e => {
                    e.stopPropagation();
                    if (!isToggling) {
                      handleToggle(item.name);
                    }
                  }}
                >
                  <div className={`w-4 h-4 rounded-full bg-white transition-transform duration-200 ${
                    user?.proMode ? "translate-x-4" : "translate-x-0"
                  }`} />
                  {isToggling && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    </div>
                  )}
                </div>
              )}
            </button>
          );
        })}
      </nav>
    </div>
  );
};

export default SideNavbar;
