import SideNavbar from "./views/nav-bar";
import { useState, useEffect } from "react";
import { Outlet } from "react-router-dom";
import { Menu } from "lucide-react";

const ProfileLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setSidebarOpen(false);
      }
    };

    // Add initial check
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div className="flex-col overflow-hidden bg-background text-foreground dark:bg-background">
      {/* Header with mobile menu button */}
      <div className="flex items-center justify-between mb-4 px-6">
        <h1 className="text-xl sm:text-2xl font-semibold">Profile Settings</h1>
        {/* Mobile menu button */}
        <button
          className="md:hidden rounded-lg p-2 text-muted-foreground hover:bg-muted hover:text-foreground transition-colors"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          <Menu size={20} />
          <span className="sr-only">Toggle profile menu</span>
        </button>
      </div>

      <div className="flex flex-1 overflow-hidden">
        <SideNavbar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        />
        <div className="flex-1 overflow-y-auto dark:bg-background">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default ProfileLayout;
