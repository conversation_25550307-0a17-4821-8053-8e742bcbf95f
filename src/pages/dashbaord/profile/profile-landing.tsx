import SideNavbar from "./views/nav-bar";
import { useState, useEffect } from "react";
// import AccountSettings from "./views/acccount-settings";
// import { EditProfile } from "./views/edit-profile";
// import NotificationSettings from "./views/notifications";
import { Outlet } from "react-router-dom";
// import { Sidebar } from "@/components/sidebar";
// import { Outlet } from "react-router-dom";
// import { useFullUserData } from "@/hooks/api/user";

const ProfileLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  // const { isLoading, error } = useFullUserData();
  // const [selectedTab, setSelectedTab] = useState<string>("profile"); // Default to profile tab

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768 && sidebarOpen) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [sidebarOpen]);

  // const renderActiveTab = () => {
  //   switch (selectedTab) {
  //     case "profile":
  //       return <EditProfile />;
  //     case "account-settings":
  //       return <AccountSettings />;
  //     case "support":
  //       return <div className="p-6"><h2 className="text-2xl font-bold">Support</h2><p>Support content coming soon...</p></div>;
  //     case "security":
  //       return <div className="p-6"><h2 className="text-2xl font-bold">Security</h2><p>Security settings coming soon...</p></div>;
  //     case "notifications":
  //       return <NotificationSettings />;
  //     case "invite":
  //       return <div className="p-6"><h2 className="text-2xl font-bold">Invite a Friend</h2><p>Invite feature coming soon...</p></div>;
  //     case "privacy-policy":
  //       return <div className="p-6"><h2 className="text-2xl font-bold">Privacy Policy</h2><p>Privacy policy content coming soon...</p></div>;
  //     case "terms":
  //       return <div className="p-6"><h2 className="text-2xl font-bold">Terms and Conditions</h2><p>Terms and conditions coming soon...</p></div>;
  //     default:
  //       return <EditProfile />; // Default fallback
  //   }
  // };

  return (
    <div className="flex-col overflow-hidden bg-background text-foreground dark:bg-background">
      <h1 className="text-3xl font-bold mb-6 pl-6">Profile Settings</h1>
      <div className="flex flex-1 overflow-hidden">
        <SideNavbar
          // selectedTab={selectedTab}
          // onTabSelect={setSelectedTab}
        />
        <div className="flex-1 overflow-y-auto dark:bg-background">
          {/* {renderActiveTab()} */}
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default ProfileLayout;
