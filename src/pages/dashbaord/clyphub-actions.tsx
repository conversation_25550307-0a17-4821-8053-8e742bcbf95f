const actions = [
	{
		id: "1",
		title: "Lifestyle",
		image: "src/assets/images/lifestyle.png",
        link: "/lifestyle"
	},
	{
		id: "2",
		title: "Loans",
		image: "src/assets/images/loans.png",
        link: "/loans"
	},
	{
		id: "3",
		title: "Learn",
		image: "src/assets/images/learn.png",
        link: "/learn"
	},
	{
		id: "4",
		title: "AI",
		image: "src/assets/images/ai.png",
        link: "/ai"
	},
	{
		id: "5",
		title: "OTC Payments",
		image: "src/assets/images/otc.png",
        link: "/otc-payments"
	},
	{
		id: "6",
		title: "Rewards Hub",
		image: "src/assets/images/rewards.png",
        link: "/rewards-hub"
	},
	{
		id: "7",
		title: "Invest",
		image: "src/assets/images/invest.png",
        link: "/invest"
	},
	{
		id: "8",
		title: "Export",
		image: "src/assets/images/export.png",
        link: "/export"
	},
	{
		id: "9",
		title: "P2Pro",
		image: "src/assets/images/p2pro.png",
        link: "/p2pro"
	},
	{
		id: "10",
		title: "Web 3",
		image: "src/assets/images/web3.png",
        link: "/web3"
	},
	{
		id: "11",
		title: "Price Alerts",
		image: "src/assets/images/pricealert.png",
        link: "/pricealerts"
	},
];

import { Link } from "react-router-dom";

export default function ClyphubActions() {
	return (
		<div className="pt-4">
			<div className="grid md:grid-cols-3 grid-cols-2 space-y-6">
				{actions.map((action) => (
                    <Link key={action.id} to={action.link}>
                    	<div
						className="bg-background dark:bg-background border border-border w-40 mx-auto rounded-lg cursor-pointer hover:bg-muted dark:hover:bg-muted transition-colors"
					>
						<img
							src={action.image}
							alt={action.title}
							className="mx-auto"
						/>
						<p className="text-center text-foreground dark:text-foreground">{action.title}</p>
					</div></Link>
				))}
			</div>
		</div>
	);
}
