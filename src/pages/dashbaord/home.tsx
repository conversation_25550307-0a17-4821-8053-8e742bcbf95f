import { useState } from "react";
import { AnimatedHomeTabs } from "@/shared/home/<USER>";
import AccountView from "@/shared/home/<USER>/account-view";
import CardsView from "@/shared/home/<USER>/cards-view";
import SavingsView from "@/shared/home/<USER>/savings-view";
import useUserStore from "@/store/user-store";

export interface Tab {
	id: string;
	title: string;
	screen: React.ReactNode;
}

const tabs: Tab[] = [
	{
		id: "account",
		title: "Account",
		screen: <AccountView />,
	},
	{
		id: "cards",
		title: "Cards",
		screen: <CardsView />,
	},
	{
		id: "savings",
		title: "Savings",
		screen: <SavingsView />,
	},
];

const Home = () => {
	const { user } = useUserStore();
	const [activeTab, setActiveTab] = useState(tabs[0]);
	const firstname = localStorage.getItem("firstName")
	// const lastname= localStorage.getItem("lastName")

	return (
		<div className="sm:px-4">
			<h1 className="text-3xl font-bold mb-6">Welcome {user?.username || firstname}</h1>
			<div className="space-y-2">
				{/* <HomeTabs
					tabs={tabs}
					activeTab={activeTab}
					onTabChange={setActiveTab}
				/> */}
				<AnimatedHomeTabs
					tabs={tabs}
					activeTab={activeTab}
					onTabChange={setActiveTab}
				/>
				{activeTab.screen}
			</div>
		</div>
	);
};

export default Home;
