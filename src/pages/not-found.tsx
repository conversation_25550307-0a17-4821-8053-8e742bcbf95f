import { useNavigate } from "react-router-dom";
import NotFoundErrImg from "@/assets/images/404-error.svg"

export default function NotFoundPage() {
	const router = useNavigate();

	return (
		<div className="flex min-h-screen w-full flex-col items-center justify-center bg-white px-4 py-12">
			<div className="relative flex w-full max-w-2xl flex-col items-center">

				{/* 404 with astronaut illustration */}
				<div className="relative w-full max-w-lg">
					<img
						src={NotFoundErrImg}
						alt="404 - Astronaut floating in space"
						width={600}
						height={400}
						className="h-auto w-full"
					/>
					
				</div>

				{/* Oops text */}
				<h1 className="mt-8 text-center text-5xl font-bold text-primary">
					Oops!
				</h1>

				<p className="mt-6 text-center text-slate-700">
					Looks like you&apos;ve hit a blockchain bump!
					<br />
					Don&apos;t worry—this isn&apos;t a lost transaction or a
					missing wallet. It&apos;s just a little hiccup!
				</p>
				<button
					onClick={() => router("/")}
					className="mt-8 w-full  rounded-full bg-primary py-4 text-center font-medium text-white transition-colors hover:bg-primary/80"
				>
					Go back Home
				</button>
			</div>
		</div>
	);
}
