import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ease from "@/assets/images/ease.png";
import assets from "@/assets/images/assets.png";
import tutorial from "@/assets/images/tutorial.png";
import market from "@/assets/images/market.png";
import savings from "@/assets/images/savings.png";
import typing from "@/assets/images/typing.png";
import { ChevronLeft, ChevronRight } from "lucide-react";

const steps = [
	{
		image: typing,
		title: "AI powered financial services",
		text: "Receive cash payments for your crypto from POS point",
	},
	{
		image: assets,
		title: "Trade and swap crypto assets",
		text: "Dive into the world of crypto with our seamless platform for buying, selling, and swapping digital assets.",
	},
	{
		image: savings,
		title: "Cryptocurrency Savings",
		text: "Secure your financial future with our cryptocurrency savings platform",
	},
	{
		image: market,
		title: "Secure Marketplace",
		text: "We prioritize security, transparency, and user privacy for a safe and seamless trading experience.",
	},
	{
		image: ease,
		title: "Easy To Use",
		text: "Our platform is designed to be intuitive and user-friendly, making it easy for you to navigate",
	},
	{
		image: tutorial,
		title: "Watch Tutorial",
		text: "Explore our platform quickly and easily with our tutorial. Start now!",
	},
];

const OnboardingFlow = () => {
	const [step, setStep] = useState<0 | 1 | 2 | 3 | 4 | 5>(0);
	const [direction, setDirection] = useState<"foreward" | "backward">(
		"foreward",
	);

	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "ArrowRight") {
				setStep((prev) =>
					prev < 5 ? ((prev + 1) as 0 | 1 | 2 | 3 | 4 | 5) : prev,
				);
				setDirection("foreward");
			} else if (event.key === "ArrowLeft") {
				setStep((prev) =>
					prev > 0 ? ((prev - 1) as 0 | 1 | 2 | 3 | 4 | 5) : prev,
				);
				setDirection("backward");
			}
		};
		window.addEventListener("keydown", handleKeyDown);
		return () => {
			window.removeEventListener("keydown", handleKeyDown);
		};
	}, []);

	const current = steps[step];

	return (
		<div className="min-h-screen h-screen w-full flex flex-col overflow-hidden bg-gradient-to-b from-[#f0cb94] via-[#e9ba77] to-[#fce4c2]">
			<div className="flex-1 flex items-center justify-center px-4 py-8 min-h-0">
				<div className="relative w-full max-w-lg flex justify-center items-center">
					<AnimatePresence
						mode="wait"
						custom={direction}
						initial={false}
					>
						<motion.img
							key={current.image}
							src={current.image}
							alt={current.title}
							className="w-full max-w-[280px] m-6 sm:max-w-[320px] md:max-w-[400px] lg:max-w-[520px] h-auto object-contain"
							initial={{ opacity: 0, scale: 0.92, y: 30 }}
							animate={{ opacity: 1, scale: 1, y: 0 }}
							exit={{ opacity: 0, scale: 0.96, y: -30 }}
							transition={{
								type: "tween",
								duration: 0.5,
								ease: "easeInOut",
							}}
						/>
					</AnimatePresence>
				</div>
			</div>

			{/* Content section with fixed min-height */}
			<div className="bg-white/90 backdrop-blur-sm px-6 py-8">
				{/* Animated text */}
				<div className="w-full flex flex-col items-center min-h-[120px] justify-center max-w-2xl mx-auto">
					<AnimatePresence mode="wait" initial={false}>
						<motion.div
							key={current.title + current.text}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -20 }}
							transition={{ duration: 0.35, ease: "easeOut" }}
							className="w-full"
						>
							<h1 className="font-bold text-center text-2xl sm:text-3xl text-gray-900 mb-3 px-4">
								{current.title}
							</h1>
							<p className="text-base sm:text-lg text-center text-muted-foreground px-4 leading-relaxed">
								{current.text}
							</p>
						</motion.div>
					</AnimatePresence>
				</div>

				{/* Progress indicator */}
				<div className="flex justify-center items-center gap-3 my-6">
					{steps.map((_, idx) => (
						<div
							key={idx}
							className={`transition-all duration-300 rounded-full ${
								idx === step
									? "bg-primary w-8 h-3 sm:w-10 sm:h-4"
									: "bg-gray-300 w-3 h-3 sm:w-4 sm:h-4"
							}`}
						/>
					))}
				</div>

				{/* Navigation buttons */}
				<div className="flex justify-between items-center gap-4 max-w-md mx-auto">
					<div
						className={`flex items-center justify-center bg-white/80 hover:bg-white border-2 border-primary/20 hover:border-primary/40 text-primary rounded-full px-4 py-2 h-12 transition-all duration-200 cursor-pointer select-none ${
							step === 0
								? "opacity-40 cursor-not-allowed pointer-events-none"
								: "hover:shadow-md active:scale-95"
						}`}
						onClick={() => {
							if (step > 0) {
								setStep((prev) =>
									prev > 0
										? ((prev - 1) as 0 | 1 | 2 | 3 | 4 | 5)
										: prev,
								);
								setDirection("backward");
							}
						}}
					>
						<ChevronLeft className="w-5 h-5" />
						<span className="hidden sm:inline ml-1">Back</span>
					</div>

					<div
						className="text-muted-foreground hover:text-primary px-4 bg-transparent hover:bg-primary/5 rounded-md transition-colors cursor-pointer select-none underline underline-offset-4 py-2"
						onClick={() => {
							window.location.href = "/";
						}}
					>
						Skip
					</div>

					<div
						className={`flex items-center justify-center bg-primary hover:bg-primary/90 text-white rounded-full px-4 py-2 h-12 transition-all duration-200 cursor-pointer select-none hover:shadow-md active:scale-95`}
						onClick={() => {
							if (step < steps.length - 1) {
								setStep((prev) =>
									prev < steps.length - 1
										? ((prev + 1) as 0 | 1 | 2 | 3 | 4 | 5)
										: prev,
								);
								setDirection("foreward");
							} else {
								window.location.href = "/";
							}
						}}
					>
						{step === steps.length - 1 ? (
							<span className="hidden sm:inline mr-1">
								Get Started
							</span>
						) : (
							<span className="hidden sm:inline mr-1">Next</span>
						)}
						<ChevronRight className="w-5 h-5" />
					</div>
				</div>
			</div>
		</div>
	);
};

export default OnboardingFlow;
