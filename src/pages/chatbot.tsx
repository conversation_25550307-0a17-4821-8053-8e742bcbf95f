import { useState, useRef, useEffect } from "react";
import { X, Send, Bo<PERSON>, User, Loader2, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/custom/button";
import { useSendChat, useChatHistory } from "@/hooks/api/user";
import { useNavigate } from "react-router-dom";
import { Skeleton } from "@/components/ui/skeleton";

interface Message {
	id: string;
	sender: "user" | "bot";
	text: string;
	timestamp: Date;
}

export default function ChatbotPage() {
	const [messages, setMessages] = useState<Message[]>([]);
	const [input, setInput] = useState("");
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const navigate = useNavigate();

	const { data: chatHistoryData, isLoading: historyLoading } =
		useChatHistory();
	const sendChatMutation = useSendChat();

	useEffect(() => {
		if (
			chatHistoryData?.message &&
			Array.isArray(chatHistoryData.message)
		) {
			const historyMessages: Message[] = [];

			chatHistoryData.message.forEach(
				(chatItem: {
					id: string;
					message: string;
					response: string;
					createdAt: string;
					updatedAt: string;
				}) => {
					historyMessages.push({
						id: `user-${chatItem.id}`,
						sender: "user",
						text: chatItem.message,
						timestamp: new Date(chatItem.createdAt),
					});

					if (chatItem.response) {
						historyMessages.push({
							id: `bot-${chatItem.id}`,
							sender: "bot",
							text: chatItem.response,
							timestamp: new Date(
								chatItem.updatedAt || chatItem.createdAt,
							),
						});
					}
				},
			);

			setMessages(historyMessages);
		} else if (
			!historyLoading &&
			(!chatHistoryData?.message || chatHistoryData.message.length === 0)
		) {
			setMessages([
				{
					id: "welcome",
					sender: "bot",
					text: "Hi! I'm your Finance Buddy. How can I help you with your financial needs today?",
					timestamp: new Date(),
				},
			]);
		}
	}, [chatHistoryData, historyLoading]);

	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, [messages]);

	const handleSend = async (e?: React.FormEvent) => {
		if (e) e.preventDefault();
		if (!input.trim() || sendChatMutation.isPending) return;

		const userMessage: Message = {
			id: `user-${Date.now()}`,
			sender: "user",
			text: input.trim(),
			timestamp: new Date(),
		};

		setMessages((prev) => [...prev, userMessage]);
		const currentInput = input.trim();
		setInput("");

		try {
			const response = await sendChatMutation.mutateAsync(currentInput);

			const botMessage: Message = {
				id: `bot-${Date.now()}`,
				sender: "bot",
				text:
					response.message?.response ||
					response.data?.response ||
					"I received your message, but I'm having trouble responding right now.",
				timestamp: new Date(),
			};

			setMessages((prev) => [...prev, botMessage]);
		} catch {
			const errorMessage: Message = {
				id: `error-${Date.now()}`,
				sender: "bot",
				text: "I'm sorry, I'm having trouble connecting right now. Please try again in a moment.",
				timestamp: new Date(),
			};

			setMessages((prev) => [...prev, errorMessage]);
		}
	};

	const formatTime = (timestamp: Date) => {
		return timestamp.toLocaleTimeString([], {
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	const renderChatBody = () => {
		if (historyLoading) {
			return (
				<div className="flex-1 overflow-y-auto px-2 sm:px-4 py-4 space-y-4 bg-gradient-to-b from-background to-muted/20">
					{/* Bot Message Skeleton */}
					<div className="flex items-start gap-2 sm:gap-3">
						<Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />
						<div className="flex flex-col max-w-[85vw] sm:max-w-[75%]">
							<div className="bg-card border border-border rounded-2xl rounded-bl-md px-3 sm:px-4 py-2">
								<Skeleton className="w-40 sm:w-64 h-4 mb-2" />
								<Skeleton className="w-28 sm:w-48 h-4" />
							</div>
							<Skeleton className="w-10 sm:w-12 h-3 mt-1 ml-1" />
						</div>
					</div>

					{/* User Message Skeleton */}
					<div className="flex items-start gap-2 sm:gap-3 flex-row-reverse">
						<Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />
						<div className="flex flex-col max-w-[85vw] sm:max-w-[75%] items-end">
							<div className="bg-primary/10 border border-primary/20 rounded-2xl rounded-br-md px-3 sm:px-4 py-2">
								<Skeleton className="w-24 sm:w-40 h-4" />
							</div>
							<Skeleton className="w-10 sm:w-12 h-3 mt-1 mr-1" />
						</div>
					</div>

					{/* Bot Message Skeleton */}
					<div className="flex items-start gap-2 sm:gap-3">
						<Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />
						<div className="flex flex-col max-w-[85vw] sm:max-w-[75%]">
							<div className="bg-card border border-border rounded-2xl rounded-bl-md px-3 sm:px-4 py-2">
								<Skeleton className="w-44 sm:w-72 h-4 mb-2" />
								<Skeleton className="w-32 sm:w-56 h-4 mb-2" />
								<Skeleton className="w-20 sm:w-32 h-4" />
							</div>
							<Skeleton className="w-10 sm:w-12 h-3 mt-1 ml-1" />
						</div>
					</div>

					{/* User Message Skeleton */}
					<div className="flex items-start gap-2 sm:gap-3 flex-row-reverse">
						<Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />
						<div className="flex flex-col max-w-[85vw] sm:max-w-[75%] items-end">
							<div className="bg-primary/10 border border-primary/20 rounded-2xl rounded-br-md px-3 sm:px-4 py-2">
								<Skeleton className="w-16 sm:w-28 h-4" />
							</div>
							<Skeleton className="w-10 sm:w-12 h-3 mt-1 mr-1" />
						</div>
					</div>
				</div>
			);
		}

		if (messages.length === 0) {
			return (
				<div className="flex-1 overflow-y-auto px-4 py-4 space-y-4 bg-gradient-to-b from-background to-muted/20">
					<div className="flex flex-col items-center justify-center h-full text-center space-y-4">
						<div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
							<Bot className="w-8 h-8 text-primary" />
						</div>
						<div>
							<h3 className="text-lg font-medium text-foreground">
								Welcome to Finance Buddy
							</h3>
							<p className="text-sm text-muted-foreground">
								Ask me anything about your finances, crypto, or
								financial services!
							</p>
						</div>
					</div>
				</div>
			);
		}

		return (
			<div className="flex-1 overflow-y-auto px-4 py-4 space-y-4 bg-gradient-to-b from-background to-muted/20">
				{messages.map((msg) => (
					<div
						key={msg.id}
						className={`flex items-start gap-3 ${
							msg.sender === "user"
								? "flex-row-reverse"
								: "flex-row"
						}`}
					>
						{/* Avatar */}
						<div
							className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
								msg.sender === "user"
									? "bg-primary text-primary-foreground"
									: "bg-muted text-muted-foreground"
							}`}
						>
							{msg.sender === "user" ? (
								<User className="w-4 h-4" />
							) : (
								<Bot className="w-4 h-4" />
							)}
						</div>

						{/* Message */}
						<div
							className={`flex flex-col max-w-[75%] ${
								msg.sender === "user"
									? "items-end"
									: "items-start"
							}`}
						>
							<div
								className={`px-4 py-2 rounded-2xl text-sm shadow-sm border ${
									msg.sender === "user"
										? "bg-primary text-primary-foreground border-primary/20 rounded-br-md"
										: "bg-card text-foreground border-border rounded-bl-md"
								}`}
							>
								{msg.text}
							</div>
							<span className="text-xs text-muted-foreground mt-1 px-1">
								{formatTime(msg.timestamp)}
							</span>
						</div>
					</div>
				))}

				{/* Typing indicator */}
				{sendChatMutation.isPending && (
					<div className="flex items-start gap-3">
						<div className="flex-shrink-0 w-8 h-8 rounded-full bg-muted text-muted-foreground flex items-center justify-center">
							<Bot className="w-4 h-4" />
						</div>
						<div className="bg-card border border-border rounded-2xl rounded-bl-md px-4 py-2">
							<div className="flex items-center gap-1">
								<div className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
								<div className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
								<div className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-bounce"></div>
							</div>
						</div>
					</div>
				)}

				<div ref={messagesEndRef} />
			</div>
		);
	};

	return (
		<div className="flex flex-col h-screen bg-background">
			{/* Header */}
			<div className="flex items-center justify-between px-4 py-3 border-b border-border bg-background/95 backdrop-blur-sm shadow-sm">
				<div className="flex items-center gap-3">
					<button
						onClick={() => navigate(-1)}
						className="p-2 rounded-full hover:bg-muted transition-colors flex items-center justify-center flex-shrink-0"
					>
						<ArrowLeft className="w-5 h-5 text-muted-foreground" />
					</button>
					<div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center border border-primary/20">
						<Bot className="w-5 h-5 text-primary" />
					</div>
					<div>
						<h2 className="text-lg font-semibold text-foreground">
							Finance Buddy
						</h2>
						<p className="text-xs text-muted-foreground">
							AI-powered financial assistant
						</p>
					</div>
				</div>
				<div className="flex items-center gap-2">
					<div className="hidden sm:flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 text-green-700 text-xs font-medium">
						<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
						Online
					</div>
					<button
						onClick={() => navigate("/")}
						className="p-2 rounded-full hover:bg-muted transition-colors flex items-center justify-center"
					>
						<X className="w-5 h-5 text-muted-foreground" />
					</button>
				</div>
			</div>

			{/* Chat Body */}
			{renderChatBody()}

			{/* Input */}
			<div className="border-t border-border bg-background/95 backdrop-blur-sm p-2 sm:p-4">
				<form
					onSubmit={handleSend}
					className="flex items-end gap-2 max-w-full sm:max-w-4xl mx-auto"
				>
					<div className="flex-1 relative">
						<textarea
							value={input}
							onChange={(e) => setInput(e.target.value)}
							onKeyDown={(e) => {
								if (e.key === "Enter" && !e.shiftKey) {
									e.preventDefault();
									handleSend();
								}
							}}
							placeholder="Ask me anything"
							className="w-full max-h-32 min-h-[44px] px-3 sm:px-4 py-3 pr-10 sm:pr-12 rounded-2xl border border-border bg-input text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none text-sm sm:text-base"
							rows={1}
							style={{
								height: "auto",
								minHeight: "44px",
							}}
							onInput={(e) => {
								const target = e.target as HTMLTextAreaElement;
								target.style.height = "auto";
								target.style.height =
									Math.min(target.scrollHeight, 128) + "px";
							}}
						/>
					</div>
					<Button
						type="submit"
						className="rounded-full flex items-center justify-center cursor-pointer w-10 h-10 sm:w-11 sm:h-11 p-0 flex-shrink-0"
						disabled={!input.trim() || sendChatMutation.isPending}
					>
						{sendChatMutation.isPending ? (
							<Loader2 className="w-4 h-4 animate-spin" />
						) : (
							<Send className="w-4 h-4" />
						)}
					</Button>
				</form>
				<p className="text-xs text-muted-foreground text-center mt-2">
					Finance Buddy can make mistakes. Please verify important
					information.
				</p>
			</div>
		</div>
	);
}
