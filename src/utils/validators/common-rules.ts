import { z } from "zod";

export const fileSchema = z.object({
	name: z.string(),
	url: z.string(),
	size: z.number(),
});

export type FileSchema = z.infer<typeof fileSchema>;

export const validateEmail = z
	.string()
	.email("Please enter a valid email address")
	.min(1, "Email is required");

export const validatePassword = z
	.string()
	.min(8, { message: "Password must be at least 8 characters" })
	.regex(/[A-Z]/, {
		message: "Password must contain at least one uppercase letter",
	})
	.regex(/[0-9]/, {
		message: "Password must contain at least one number",
	})
	.regex(/[^A-Za-z0-9]/, {
		message: "Password must contain at least one special character",
	});