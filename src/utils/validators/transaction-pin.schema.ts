import { z } from "zod";

const pinValidator = z
	.string()
	.min(4, "PIN must be exactly 4 digits")
	.max(4, "PIN must be exactly 4 digits")
	.regex(/^\d+$/, "PIN must contain only digits");

export const createPinSchema = z
	.object({
		pin: pinValidator,
		confirm_pin: pinValidator,
	})
	.refine((data) => data.pin === data.confirm_pin, {
		message: "PINs do not match",
		path: ["confirm_pin"],
	});

export const verifyPinSchema = z.object({
	pin: pinValidator,
});

export type TCreatePinSchema = z.infer<typeof createPinSchema>;
export type TVerifyPinSchema = z.infer<typeof verifyPinSchema>;
