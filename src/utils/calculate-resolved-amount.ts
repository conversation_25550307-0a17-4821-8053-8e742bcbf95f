import { IFiatDepositMethod } from "@/types/fiat-banking";
import { IOption } from "@/types/general";

interface IResolved {
	fee: number;
	amount: number;
}

export function calcResolvedAmount(
	amount: number,
	selectedDepositMethod: IOption<IFiatDepositMethod>,
	resolveCalculation: "add" | "minus" = "minus",
): IResolved {
	if (!selectedDepositMethod?.raw?.fee) {
		return {
			fee: 0,
			amount: 0,
		};
	}

	const feeAmount = parseFloat(selectedDepositMethod.raw.fee.toString());
	const feeType = selectedDepositMethod.raw.fee_type;

	if (isNaN(amount) || isNaN(feeAmount)) {
		return {
			fee: 0,
			amount: 0,
		};
	}

	const fee =
		feeType === "PERCENTAGE" ? (amount * feeAmount) / 100 : feeAmount;

	const resolvedAmount =
		resolveCalculation === "add" ? amount + fee : amount - fee;

	return {
		fee,
		amount: Math.max(resolvedAmount, 0),
	};
}
