export const formatCurrency = (
	amount: number,

	locale: string = "en-NG",
): string => {
	if (amount === 0) {
		return "0.00";
	}

	try {
		const formatter = new Intl.NumberFormat(locale, {
			style: "decimal",
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});

		return formatter.format(amount);
	} catch (error) {
		console.error("Error formatting currency:", error);

		return amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
	}
};

export const formatCryptoBalance = (
	amount: number,
	symbol: string = "BTC",
): string => {
	let decimalPlaces = 8;

	switch (symbol.toUpperCase()) {
		case "BTC":
			decimalPlaces = 8;
			break;
		case "ETH":
			decimalPlaces = 6;
			break;
		default:
			decimalPlaces = 4;
	}

	if (amount > 0 && amount < 0.000001) {
		return amount.toFixed(10).replace(/0+$/, "");
	}

	return amount.toFixed(decimalPlaces).replace(/\.?0+$/, "");
};
