import { isAxiosError } from "axios";

export const handleError = (error: unknown): never => {
	if (isAxiosError(error)) {
		const message =
			error?.response?.data?.errors?.[0] ||
			error?.response?.data?.details ||
			error?.response?.data?.error ||
			error?.response?.data?.message ||
			"An error occurred with the request.";
		error.message = message;
		throw error;
	} else if (error instanceof Error) {
		throw new Error(error.message);
	} else {
		throw new Error("An unknown error occurred");
	}
};
