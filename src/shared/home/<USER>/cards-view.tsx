import { useState, useRef } from "react";
import cardsAnimation from "@/assets/animations/cards.json";
import <PERSON>tie, { LottieRefCurrentProps } from "lottie-react";
import { useGetUserCards } from "@/hooks/api/cards";
import { Button } from "@/components/custom/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { CreateVirtualCardFlow } from "../cards";
import { Card } from "../cards/card-item";
import { ManageCardSection } from "../cards/manage-card-section";
import { ChangeDesignSection } from "../cards/change-design-section";
import { ViewTransactionSection } from "../cards/view-transaction-section";
import { ChevronRight, PencilLine, Palette, Menu } from "lucide-react";
import { ICard } from "@/types/cards";

const LottieAnimation = () => {
	const animationRef = useRef<LottieRefCurrentProps>(null);

	return (
		<Lottie
			animationData={cardsAnimation}
			loop={true}
			autoplay={true}
			lottieRef={animationRef}
			style={{
				width: 400,
				height: 400,
			}}
		/>
	);
};

// export interface ICard {
// 	id: string;
// 	masked_pan: string;
// 	status: "DISABLED" | "ACTIVE";
// 	issuer: "MASTERCARD";
// 	currency: string;
// 	balance: number;
// 	response: string;
// }

const CardsView = () => {
	const { openDrawer } = useDrawer();
	const { data: cards = [], isLoading } = useGetUserCards();
	const [activeTab, setActiveTab] = useState<"virtual" | "physical">(
		"virtual",
	);
	const [currentSection, setCurrentSection] = useState<
		"cards" | "manage" | "change-design" | "view-transaction"
	>("cards");

	const [selectedCard, setSelectedCard] = useState<ICard | null>(null);

	const openCreateVirtualCardDrawer = () => {
		openDrawer({
			view: <CreateVirtualCardFlow />,
			placement: "right",
			customSize: "480px",
		});
	};

	const handleManageCard = () => {
		setCurrentSection("manage");
	};

	const handleChangeDesign = () => {
		setCurrentSection("change-design");
	};

	const handleViewTransactions = () => {
		setCurrentSection("view-transaction");
	};

	const handleBackToCards = () => {
		setCurrentSection("cards");
	};

	if (currentSection === "manage") {
		return (
			<ManageCardSection onBack={handleBackToCards} card={selectedCard} />
		);
	}

	if (currentSection === "change-design") {
		return <ChangeDesignSection onBack={handleBackToCards} />;
	}

	if (currentSection === "view-transaction") {
		return <ViewTransactionSection onBack={handleBackToCards} card={selectedCard}/>;
	}

	return (
		<div className="container mx-auto p-6">
			<div className="mb-8">
				<h3 className="text-2xl font-bold mb-2 text-foreground">
					Clyp Cards
				</h3>
				<p className="text-muted-foreground">
					Track and manage your cards.
				</p>
				{/* Modern pill/underline style tabs */}
				<div className="flex gap-2 mt-4 border-b border-border w-full max-w-xs">
					<button
						onClick={() => setActiveTab("virtual")}
						className={`flex-1 px-1 pb-2 text-center font-semibold transition-colors relative
						${activeTab === "virtual" ? "text-primary" : "text-muted-foreground"}
						`}
						style={{
							borderBottom:
								activeTab === "virtual"
									? "2.5px solid #ea9924"
									: "2.5px solid transparent",
						}}
					>
						Virtual Cards
					</button>
					<button
						onClick={() => setActiveTab("physical")}
						className={`flex-1 px-1 pb-2 text-center font-semibold transition-colors relative
						${activeTab === "physical" ? "text-primary" : "text-muted-foreground"}
						`}
						style={{
							borderBottom:
								activeTab === "physical"
									? "2.5px solid #ea9924"
									: "2.5px solid transparent",
						}}
					>
						Physical Cards
					</button>
				</div>
			</div>

			{activeTab === "virtual" &&
				(isLoading ? (
					<div className="flex items-center justify-center py-12">
						<p className="text-muted-foreground">Loading...</p>
					</div>
				) : cards.length > 0 ? (
					<div className="w-full">
						{/* {cards.map((card, index) => (
							<CardItem key={card.id} item={card} index={index} />
						))} */}
						<div className="flex flex-col  gap-6 xl:gap-12 ">
							<div className="overflow-auto  mx-auto xl:mx-0">
								{cards.map((card) => (
									<div className="flex flex-col gap-10 lg:flex-row">
										<Card
											key={card.id}
											item={card}
											className="max-w-[370px] overflow-x-auto h-48"
										/>
										<div className="flex flex-col gap-3 w-full max-w-md xl:max-w-sm mx-auto xl:mx-0">
											<div
												className="bg-card border w-full border-gray-300 dark:border-gray-700 cursor-pointer flex justify-between items-center rounded-full p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
												onClick={handleManageCard}
											>
												<div className="flex gap-2 items-center">
													<PencilLine className="w-4 h-4 text-foreground" />
													<p className="text-foreground text-sm sm:text-base">
														Manage card
													</p>
												</div>
												<ChevronRight className="w-4 h-4 text-muted-foreground" />
											</div>
											<div
												className="bg-card border w-full border-gray-300 dark:border-gray-700 cursor-pointer flex justify-between items-center rounded-full p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
												onClick={handleChangeDesign}
											>
												<div className="flex gap-2 items-center">
													<Palette className="w-4 h-4 text-foreground" />
													<p className="text-foreground text-sm sm:text-base">
														Change Card design
													</p>
												</div>
												<ChevronRight className="w-4 h-4 text-muted-foreground" />
											</div>
											<div
												className="bg-card border w-full border-gray-300 dark:border-gray-700 cursor-pointer flex justify-between items-center rounded-full p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
												onClick={handleViewTransactions}
											>
												<div
													className="flex gap-2 items-center"
													onClick={() =>
														setSelectedCard(card)
													}
												>
													<Menu className="w-4 h-4 text-foreground" />
													<p className="text-foreground text-sm sm:text-base">
														View card Transaction
													</p>
												</div>
												<ChevronRight className="w-4 h-4 text-muted-foreground" />
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				) : (
					<div className="flex flex-col items-center justify-center">
						<LottieAnimation />
						<div className="text-center mb-6">
							<h3 className="text-2xl font-bold mb-2 text-foreground">
								No Cards Yet
							</h3>
							<p className="text-muted-foreground">
								You do not have any Clyppay cards yet.
							</p>
						</div>
						<Button
							className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-full py-3 px-6"
							onClick={openCreateVirtualCardDrawer}
						>
							Create a Clyp Card
						</Button>
					</div>
				))}

			{activeTab === "physical" && (
				<div className="flex flex-col items-center justify-center py-20">
					<LottieAnimation />

					<h3 className="text-xl font-bold mb-2 text-foreground">
						Physical Cards
					</h3>
					<p className="text-muted-foreground mb-4">
						You do not have any physical cards yet.
					</p>
					<Button
						className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-full py-3 px-6"
						onClick={openCreateVirtualCardDrawer}
					>
						Create Card
					</Button>
				</div>
			)}
		</div>
	);
};

export default CardsView;

// const CardItem = ({ item, index }: { item: ICard; index: number }) => {
// 	const colors = {
// 		amber: {
// 			bg: "bg-amber-500/[0.2] dark:bg-amber-700/[0.3]",
// 			text: "text-amber-700 dark:text-amber-400",
// 			accent: "bg-amber-500/40 dark:bg-amber-600/50",
// 		},
// 		red: {
// 			bg: "bg-red-500/[0.2] dark:bg-red-700/[0.3]",
// 			text: "text-red-700 dark:text-red-400",
// 			accent: "bg-red-500/40 dark:bg-red-600/50",
// 		},
// 		blue: {
// 			bg: "bg-blue-500/[0.2] dark:bg-blue-700/[0.3]",
// 			text: "text-blue-700 dark:text-blue-400",
// 			accent: "bg-blue-500/40 dark:bg-blue-600/50",
// 		},
// 		green: {
// 			bg: "bg-green-500/[0.2] dark:bg-green-700/[0.3]",
// 			text: "text-green-700 dark:text-green-400",
// 			accent: "bg-green-500/40 dark:bg-green-600/50",
// 		},
// 		purple: {
// 			bg: "bg-purple-500/[0.2] dark:bg-purple-700/[0.3]",
// 			text: "text-purple-700 dark:text-purple-400",
// 			accent: "bg-purple-500/40 dark:bg-purple-600/50",
// 		},
// 	};

// 	const color = Object.values(colors)[index % Object.values(colors).length];
// 	// const colorKey = Object.keys(colors)[index % Object.keys(colors).length];

// 	const renderType = (type: string) => {
// 		switch (type?.toLowerCase()) {
// 			case "mastercard":
// 				return <MasterCardIcon className="w-8 h-8" />;
// 			case "verve":
// 				return <CustomText className="" text={type} />;
// 			default:
// 				return <CustomText className="" text={type} />;
// 		}
// 	};

// 	return (
// 		<div className="w-full px-4 py-6 cursor-pointer" onClick={() => ""}>
// 			<div className="bg-card border border-border rounded-xl">
// 				<div
// 					className={`${color.bg} w-full h-[200px] flex flex-col justify-between p-4 rounded-xl relative overflow-hidden`}
// 				>
// 					<div className="flex w-full px-3 flex-row justify-between items-center">
// 						<Icon name="clyppay_logo" className="w-22 h-auto" />
// 						{renderType(item?.issuer)}
// 					</div>

// 					<div className="w-full px-4">
// 						<CustomText
// 							text="Card Number"
// 							className="w-full text-sm mt-4"
// 						/>
// 						<CustomText
// 							text={item?.masked_pan}
// 							className="w-full text-2xl font-bold"
// 						/>
// 					</div>

// 					<div className="flex w-full px-3 flex-row justify-between">
// 						<div className="flex flex-row">
// 							<div className="mx-2">
// 								<CustomText
// 									text="Currency"
// 									className="w-full text-xs"
// 								/>
// 								<CustomText
// 									text={item?.currency}
// 									className="w-full"
// 								/>
// 							</div>
// 							<div className="mx-4">
// 								<CustomText
// 									text="Issuer"
// 									className="w-full text-xs"
// 								/>
// 								<CustomText
// 									text={item?.issuer}
// 									className="w-full"
// 								/>
// 							</div>
// 						</div>
// 					</div>

// 					{/* Decorative elements */}
// 					<div
// 						className={`${color.bg} w-8 h-[30%] absolute rounded-tl-3xl bottom-0 right-[18.5%]`}
// 					/>
// 					<div
// 						className={`${color.accent} w-8 h-[50%] absolute rounded-tl-3xl bottom-0 right-[9.2%]`}
// 					/>
// 					<div
// 						className={`${color.accent} w-8 h-[70%] absolute rounded-tl-3xl bottom-0 right-[0%] rounded-br-xl`}
// 					/>
// 				</div>
// 			</div>
// 		</div>
// 	);
// };

// interface CustomTextProps {
// 	text: string;
// 	className?: string;
// 	heading?: boolean;
// }

// export const CustomText = ({
// 	text,
// 	className,
// 	heading = false,
// }: CustomTextProps) => {
// 	const baseClasses = "text-foreground";

// 	if (heading) {
// 		return (
// 			<h4 className={cn(baseClasses, "font-bold", className)}>{text}</h4>
// 		);
// 	}

// 	return <p className={cn(baseClasses, className)}>{text}</p>;
// };

// interface IconProps {
// 	className?: string;
// }

// export const MasterCardIcon = ({ className = "" }: IconProps) => {
// 	return (
// 		<svg
// 			className={className}
// 			viewBox="0 0 38 24"
// 			xmlns="http://www.w3.org/2000/svg"
// 		>
// 			<rect width="38" height="24" fill="none" />
// 			<path d="M15.4 4H22.6V20H15.4V4Z" fill="#FF5F00" />
// 			<path
// 				d="M16.2 12C16.2 8.7 17.8 5.8 20.1 4C18.2 2.4 15.7 1.5 13 1.5C6.9 1.5 2 6.4 2 12.5C2 18.6 6.9 23.5 13 23.5C15.7 23.5 18.2 22.6 20.1 21C17.8 19.2 16.2 16.2 16.2 13V12Z"
// 				fill="#EB001B"
// 			/>
// 			<path
// 				d="M36 12.5C36 18.6 31.1 23.5 25 23.5C22.3 23.5 19.8 22.6 17.9 21C20.2 19.2 21.8 16.3 21.8 12.5C21.8 8.7 20.2 5.8 17.9 4C19.8 2.4 22.3 1.5 25 1.5C31.1 1.5 36 6.4 36 12.5Z"
// 				fill="#F79E1B"
// 			/>
// 		</svg>
// 	);
// };
