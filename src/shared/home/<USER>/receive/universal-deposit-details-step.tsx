import { useEffect, useRef, useState } from "react";
import useReceiveCryptoStore from "@/store/receive-crypto-store";
import { Copy, CheckCheck } from "lucide-react";
import { ResponseStatus } from "@/config/enums";
import { notify } from "@/utils/notify";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { Button } from "@/components/custom/button";

export function UniversalDepositDetailsStep() {
	const { closeDrawer } = useDrawer();
	const { displayAccountsData, reset } =
		useReceiveCryptoStore();

	const [displayText, setDisplayText] = useState("00:00");
	const [isCopied, setIsCopied] = useState(false);
	const isExpiredRef = useRef(false);
	const timerIdRef = useRef<NodeJS.Timeout | null>(null);
	const targetDateRef = useRef<Date | null>(null);

	
	const data = displayAccountsData?.data;
	const payload = displayAccountsData?.payload;

	useEffect(() => {
		if (!data || !data.expiry_time) return;

		
		if (timerIdRef.current) {
			clearInterval(timerIdRef.current);
			timerIdRef.current = null;
		}

		
		isExpiredRef.current = false;

		
		const now = new Date();
		targetDateRef.current = new Date(
			now.getTime() + data.expiry_time * 60 * 1000,
		);

		
		const updateTimer = () => {
			if (!targetDateRef.current) return;

			const now = new Date();
			const diff = targetDateRef.current.getTime() - now.getTime();

			if (diff <= 0) {
				
				setDisplayText("00:00");
				if (!isExpiredRef.current) {
					notify("Wallet expired!", ResponseStatus.ERROR);
					
					isExpiredRef.current = true;
				}
				if (timerIdRef.current) {
					clearInterval(timerIdRef.current);
					timerIdRef.current = null;
				}
				return;
			}

			
			const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
			const seconds = Math.floor((diff % (1000 * 60)) / 1000);

			
			setDisplayText(
				`${minutes.toString().padStart(2, "0")}:${seconds
					.toString()
					.padStart(2, "0")}`,
			);
		};

		
		updateTimer();
		timerIdRef.current = setInterval(updateTimer, 1000);

		
		return () => {
			if (timerIdRef.current) {
				clearInterval(timerIdRef.current);
				timerIdRef.current = null;
			}
		};
	}, [data]);

	if (!data || !payload) return null;

	const copyWalletAddress = () => {
		if (!data.deposit_address) {
			notify("No wallet address to copy.", ResponseStatus.ERROR);
			return;
		}

		navigator.clipboard
			.writeText(data.deposit_address)
			.then(() => {
				notify(
					"Wallet address copied to clipboard",
					ResponseStatus.SUCCESS,
				);
				
				setIsCopied(true);
				
				setTimeout(() => {
					setIsCopied(false);
				}, 1000);
			})
			.catch(() => {
				notify(
					`An error occurred while copying wallet address.`,
					ResponseStatus.ERROR,
				);
			});
	};

	const handleDone = () => {
		reset();
		closeDrawer();
		
	};

	return (
		<div className="p-6 space-y-6">
			<div className="flex flex-row justify-between items-center mb-4">
				<div className="flex-col w-4/5">
					<h2 className="text-xl font-bold">
						Crypto {payload.coin?.currency} Deposit
					</h2>
					<p className="text-gray-400 text-sm">
						Please make a deposit into the following account.
					</p>
				</div>
			</div>

			<div className="my-2 p-4 flex flex-row justify-between items-center border border-gray-200 rounded-lg">
				<div className="flex flex-row items-center">
					<div className="w-10 h-10 rounded-full overflow-hidden">
						{payload.coin?.image ? (
							<img
								src={payload.coin.image}
								alt={payload.coin?.currency}
								className="w-full h-full object-cover"
							/>
						) : (
							<div className="w-full h-full bg-amber-500 flex items-center justify-center">
								<span className="text-white font-bold">
									{payload.coin?.currency?.charAt(0)}
								</span>
							</div>
						)}
					</div>
					<div className="flex flex-col mx-4">
						<p className="text-sm text-gray-500">
							You are depositing
						</p>
						<p className="text-amber-500 font-medium">
							{payload.coin?.coin_name || ""}{" "}
							{payload.coin?.currency}
						</p>
					</div>
				</div>
				<div className="flex flex-col">
					<p className="text-lg text-right ">
						{data.expected_amount}
					</p>
				</div>
			</div>

			<div className="my-2 p-4 flex flex-row justify-between items-center border border-gray-200 rounded-lg">
				<div className="flex flex-col">
					<p className="text-gray-500">Wallet ID</p>
					<p className="text-sm mt-2 break-all">
						{data.deposit_address}
					</p>
				</div>
				<button
					onClick={copyWalletAddress}
					className="p-2 text-primary hover:text-amber-500 transition-colors"
					aria-label={isCopied ? "Copied" : "Copy to clipboard"}
				>
					{isCopied ? (
						<CheckCheck size={20} className="text-green-700" />
					) : (
						<Copy size={20} />
					)}
				</button>
			</div>

			<div className="my-2 p-4 flex flex-row justify-between items-center border border-gray-200 rounded-lg">
				<div className="flex flex-col">
					<p className="text-gray-400">Network</p>
					<p className="mt-2">{payload.network?.label}</p>
				</div>
				<div className="flex flex-col items-end">
					<p className="text-sm text-gray-400 text-center mb-2">
						Wallet expires in {displayText}
					</p>
				</div>
			</div>

			<Button
				onClick={handleDone}
				className="w-full py-4 mt-8 text-white rounded-full font-medium"
			>
				Done
			</Button>
		</div>
	);
}
