"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { X } from "lucide-react";
import { Icon } from "@/components/icons";
import { SelectStep } from "./select-step";
import { StaticDepositStep } from "./static-deposit-step";
import { UniversalDepositDetailsStep } from "./universal-deposit-details-step";
import { UniversalDepositFormStep } from "./universal-deposit-form-step";
import useReceiveCryptoStore from "@/store/receive-crypto-store";


export function ReceiveCryptoFlow() {
	const { closeDrawer } = useDrawer();

	
	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);

	
	const { currentStep, setCurrentStep } = useReceiveCryptoStore();

	
	const handleClose = () => {
		
		closeDrawer();

		
		setTimeout(() => {
			
			useReceiveCryptoStore.getState().reset();
			setDirection("forward");
		}, 300);
	};

	
	const handleStepChange = (
		step: typeof currentStep,
		dir: "forward" | "backward" = "forward",
	) => {
		setDirection(dir);
		setCurrentStep(step);
	};

	
	const handleBack = () => {
		switch (currentStep) {
			case "staticDeposit":
				handleStepChange("select", "backward");
				break;
			case "universalDepositForm":
				handleStepChange("select", "backward");
				break;
			case "universalDepositDetails":
				handleStepChange("universalDepositForm", "backward");
				break;
			default:
				break;
		}
	};

	
	const getStepTitle = () => {
		switch (currentStep) {
			case "select":
				return "Receive Crypto";
			case "staticDeposit":
				return "Crypto BTC Deposit";
			case "universalDepositForm":
				return "Universal Deposit";
			case "universalDepositDetails":
				return "Universal Deposit";
			default:
				return "";
		}
	};

	
	const showBackButton = [
		"staticDeposit",
		"universalDepositForm",
		"universalDepositDetails",
	].includes(currentStep);

	
	const horizontalVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex flex-col mt-10 relative px-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>
				{(getStepTitle() || showBackButton) && (
					<div className="flex items-center justify-center relative">
						{showBackButton && (
							<span
								onClick={handleBack}
								className="absolute left-0 bg-primary-light text-primary rounded-full p-2 cursor-pointer"
							>
								<Icon name="arrow-left" className="size-6" />
							</span>
						)}
						{getStepTitle() && (
							<h2 className="text-2xl font-semibold w-full text-center">
								{getStepTitle()}
							</h2>
						)}
					</div>
				)}
			</div>

			<div className="p-0 h-full overflow-hidden relative flex-grow">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{currentStep === "select" && (
						<motion.div
							key="select"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<SelectStep />
						</motion.div>
					)}

					{currentStep === "staticDeposit" && (
						<motion.div
							key="staticDeposit"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<StaticDepositStep />
						</motion.div>
					)}

					{currentStep === "universalDepositForm" && (
						<motion.div
							key="universalDepositForm"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<UniversalDepositFormStep />
						</motion.div>
					)}

					{currentStep === "universalDepositDetails" && (
						<motion.div
							key="universalDepositDetails"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<UniversalDepositDetailsStep />
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}
