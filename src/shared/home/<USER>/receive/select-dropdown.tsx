"use client";

import { IOption } from "@/types/general";
import { ChevronDown } from "lucide-react";
import { useState, useRef, useEffect } from "react";

interface SelectDropdownProps<T> {
	options: IOption<T>[];
	value: string | null;
	onChange: (option: IOption<T>) => void;
	placeholder: string;
	label?: string;
	disabled?: boolean;
}

export function SelectDropdown<T>({
	options,
	value,
	onChange,
	placeholder,
	label,
	disabled = false,
}: SelectDropdownProps<T>) {
	const [isOpen, setIsOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

	const selectedOption = options.find((option) => option.value === value);

	const filteredOptions = options.filter((option) => !option.hidden);

	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		}

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const handleSelect = (option: IOption<T>) => {
		onChange(option);
		setIsOpen(false);
	};

	return (
		<div className="relative w-full" ref={dropdownRef}>
			{label && <div className="text-sm text-gray-400 mb-1">{label}</div>}
			<button
				type="button"
				className={`flex items-center justify-between w-full px-4 py-3 bg-gray-800 rounded-full text-left ${
					disabled
						? "opacity-70 cursor-not-allowed"
						: "cursor-pointer"
				}`}
				onClick={() => !disabled && setIsOpen(!isOpen)}
				disabled={disabled}
			>
				{selectedOption ? (
					<div className="flex items-center">
						{selectedOption.icon && (
							<span className="mr-2 text-amber-500">
								{selectedOption.icon}
							</span>
						)}
						<span className="text-white">
							{selectedOption.label}
						</span>
					</div>
				) : (
					<span className="text-gray-400">{placeholder}</span>
				)}
				<ChevronDown />
			</button>

			{isOpen && (
				<div className="absolute z-10 w-full mt-2 bg-gray-800 rounded-xl shadow-lg">
					<div className="p-4">
						<h3 className="text-lg font-medium text-white mb-2">
							{placeholder}
						</h3>
						<p className="text-sm text-gray-400 mb-4">
							Choose the method you want to use to receive this
							transaction.
						</p>
						<div className="space-y-2">
							{filteredOptions.map((option) => (
								<button
									key={option.value}
									className="flex items-center w-full p-3 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors"
									onClick={() => handleSelect(option)}
								>
									{option.icon && (
										<span className="mr-2 text-white">
											{option.icon}
										</span>
									)}
									<span className="text-white">
										{option.label}
									</span>
								</button>
							))}
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
