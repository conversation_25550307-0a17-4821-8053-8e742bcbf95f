import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON> } from "lucide-react";
import useReceiveCryptoStore from "@/store/receive-crypto-store";
import { useEffect, useState } from "react";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import {
	ICryptoDepositData,
	ICryptoWithdrawalMethod,
} from "@/types/crypto-banking";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { useCreateCryptoDeposit } from "@/hooks/api/crypto-banking";
import { Button } from "@/components/custom/button";

interface DisplayAccountData {
	data?: ICryptoDepositData;

	payload?: {
		coin: ICryptoWallet;
		method: ICryptoWithdrawalMethod;
		network: string;
		amount: string;
		feeInfo: {
			fee: number;
			amount: number;
		};
	};
}

export function StaticDepositStep() {
	const { selectedCoin, selectedDepositMethod } = useReceiveCryptoStore();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [displayAccountData, setDisplayAccountData] =
		useState<Partial<DisplayAccountData> | null>(null);

	const { mutateAsync: createDepositMutation } = useCreateCryptoDeposit();

	async function createDeposit() {
		if (!selectedCoin || !selectedDepositMethod?.raw) {
			setError("Missing required deposit information");
			return;
		}

		try {
			setIsLoading(true);
			setError(null);

			const result = await createDepositMutation({
				currency: selectedCoin.currency,
				amount: 13,
				method: selectedDepositMethod.raw.name,
				paynetwork: selectedCoin.currency,
			});

			if (!result || !result.deposit_data) {
				throw new Error("Invalid response from server");
			}

			const payload: DisplayAccountData["payload"] = {
				coin: selectedCoin,
				method: selectedDepositMethod.raw,
				amount: "0",
				network: selectedDepositMethod.raw.name,
				feeInfo: {
					fee: 0,
					amount: 0,
				},
			};

			const newDisplayData = {
				payload,
				data: result.deposit_data,
			};

			setDisplayAccountData(newDisplayData);
		} catch (err) {
			console.error("Error creating deposit:", err);
			setError("Failed to create deposit");
			notify("Failed to create deposit", ResponseStatus.ERROR);
		} finally {
			setIsLoading(false);
		}
	}

	useEffect(() => {
		if (selectedCoin && selectedDepositMethod?.raw) {
			createDeposit();
		}
	}, [selectedCoin, selectedDepositMethod]);

	if (isLoading) {
		return (
			<div className="p-6 flex flex-col items-center justify-center h-64">
				<div className="w-10 h-10 border-4 border-amber-500 border-t-transparent rounded-full animate-spin mb-4"></div>
				<p>Generating your deposit address...</p>
			</div>
		);
	}

	if (error) {
		return (
			<div className="p-6 flex flex-col items-center justify-center h-64">
				<p className="text-red-500 mb-4">{error}</p>
				<Button onClick={createDeposit} className="rounded-full">
					Try Again
				</Button>
			</div>
		);
	}

	if (displayAccountData) {
		return <StaticDepositView displayAccountData={displayAccountData} />;
	}

	return null;
}

export function StaticDepositView({
	displayAccountData,
}: {
	displayAccountData: DisplayAccountData;
}) {
	const { closeDrawer } = useDrawer();
	const { reset } = useReceiveCryptoStore();
	const [isCopied, setIsCopied] = useState(false);

	const data = displayAccountData?.data;
	const payload = displayAccountData?.payload;

	const copyWalletAddress = () => {
		if (!data?.address || data?.address === "Generating...") {
			notify("No wallet address to copy.", ResponseStatus.ERROR);
			return;
		}

		navigator.clipboard
			.writeText(data.address)
			.then(() => {
				notify(
					"Wallet address copied to clipboard",
					ResponseStatus.SUCCESS,
				);

				setIsCopied(true);

				setTimeout(() => {
					setIsCopied(false);
				}, 1000);
			})
			.catch(() => {
				notify(
					`An error occurred while copying wallet address.`,
					ResponseStatus.ERROR,
				);
			});
	};

	const handleDone = () => {
		reset();
		closeDrawer();
	};

	if (!data || !payload) {
		return null;
	}

	const isGenerating = data.deposit_address === "Generating...";

	return (
		<div className="p-6 space-y-6">
			<div>
				<h2 className="text-xl font-bold mb-1">
					Crypto {payload.coin?.currency} Deposit
				</h2>
				<p className="text-gray-500 text-sm">
					Find below the details to make a deposit into your account.
				</p>
			</div>

			<div className="border border-gray-200 rounded-lg p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center">
						<div className="w-10 h-10 rounded-full bg-amber-500 flex items-center justify-center mr-3">
							{payload.coin?.image ? (
								<img
									src={payload.coin.image}
									alt={payload.coin?.currency}
									className="w-full h-full object-cover"
								/>
							) : (
								<div className="w-full h-full bg-amber-500 flex items-center justify-center">
									<span className="text-white font-bold">
										{payload.coin?.currency?.charAt(0)}
									</span>
								</div>
							)}
						</div>
						<div>
							<p className="text-sm text-gray-400">
								You are depositing
							</p>
							<p className="font-medium">
								{payload.coin?.coin_name || ""}{" "}
								{payload.coin?.currency}
							</p>
						</div>
					</div>
				</div>
			</div>

			<div className="border border-gray-200 rounded-lg p-4">
				<div className="flex items-center justify-between">
					<div className="flex-1">
						<p className="text-sm text-gray-500">Wallet Address</p>
						<p className="text-sm font-medium break-all">
							{isGenerating
								? "Your wallet address is being generated... You will be notified when it's ready."
								: data?.address}
						</p>
					</div>
					{!isGenerating && (
						<button
							className="p-2 text-primary ml-2 flex-shrink-0"
							onClick={copyWalletAddress}
							aria-label={
								isCopied ? "Copied" : "Copy to clipboard"
							}
						>
							{isCopied ? (
								<CheckCheck
									size={20}
									className="text-green-700"
								/>
							) : (
								<Copy size={20} />
							)}
						</button>
					)}
				</div>
			</div>

			<div className="border border-gray-200 rounded-lg p-4">
				<div>
					<p className="text-sm text-gray-500">Network</p>
					<p className="font-medium">{payload.network}</p>
				</div>
			</div>

			<Button
				onClick={handleDone}
				className="w-full py-4 rounded-full text-white font-medium"
			>
				Done
			</Button>
		</div>
	);
}
