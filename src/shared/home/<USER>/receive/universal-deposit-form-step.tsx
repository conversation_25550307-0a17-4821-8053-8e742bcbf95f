import { useState } from "react";
import { useForm, Controller } from "react-hook-form";
import { cn } from "@/lib/utils";

import useReceiveCryptoStore from "@/store/receive-crypto-store";
import {
	useCreateCryptoDeposit,
	useGetCryptoUniversalDepositMethods,
} from "@/hooks/api/crypto-banking";
import { IOption } from "@/types/general";
import { ICryptoUniversalNetworkData } from "@/types/crypto-banking";
import EnhancedSelect from "@/components/enhanced-select";
import { Button } from "@/components/ui/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";


type FormValues = {
	network: IOption<ICryptoUniversalNetworkData> | null;
	amount: string;
};

export function UniversalDepositFormStep() {
	const {
		selectedCoin,
		selectedDepositMethod,
		setCurrentStep,
		setDisplayAccountsData,
	} = useReceiveCryptoStore();

	const [isCreating, setIsCreating] = useState(false);
	const [networkError, setNetworkError] = useState("");

	const { data: coinNetworks = [], isLoading } =
		useGetCryptoUniversalDepositMethods(selectedCoin?.currency || "");
	const { mutate: createDeposit } = useCreateCryptoDeposit();

	
	const {
		control,
		handleSubmit,
		watch,
		formState: { errors },
	} = useForm<FormValues>({
		defaultValues: {
			network: null,
			amount: "",
		},
		mode: "onChange",
	});

	
	const watchAmount = watch("amount");
	const watchNetwork = watch("network");

	const generateNetworkOption = (
		x: ICryptoUniversalNetworkData,
	): IOption<ICryptoUniversalNetworkData> => {
		if (!selectedCoin) {
			return {
				label: "",
				value: x?.currency,
				raw: x,
			};
		}

		const coin_name = selectedCoin?.currency;
		
		const label =
			x.currency.toLowerCase().trim() === coin_name?.toLowerCase().trim()
				? `${x?.currency.toUpperCase()} Network`
				: x.currency
						.toLowerCase()
						.replace(coin_name?.toLowerCase(), "")
						.toUpperCase();

		return {
			label,
			value: x?.currency,
			raw: x,
		};
	};

	const universalNetworkOptions: IOption<ICryptoUniversalNetworkData>[] =
		coinNetworks?.map(generateNetworkOption) || [];

	const calculateFee = () => {
		
		return 0.01; 
	};

	const calcResolvedAmount = (amountValue: string) => {
		const amountNum = parseFloat(amountValue || "0");
		const feePercentage = calculateFee();
		const feeAmount = amountNum * (feePercentage / 100);

		return {
			fee: feePercentage,
			amount: feeAmount,
		};
	};

	const validateForm = () => {
		let isValid = true;

		
		if (!watchNetwork) {
			setNetworkError("Network is required");
			isValid = false;
		} else {
			setNetworkError("");
		}

		return isValid;
	};

	const validateAmount = (value: string) => {
		if (!value) return "Amount is required";

		if (!/^\d*\.?\d*$/.test(value)) {
			return "Please enter a valid amount";
		}

		const amount = parseFloat(value);
		if (amount <= 0) {
			return "Amount must be greater than 0";
		}

		
		

		return true;
	};

	const onSubmit = (data: FormValues) => {
		if (!validateForm()) return;
		if (!data.network?.raw || !selectedCoin || !selectedDepositMethod?.raw)
			return;

		setIsCreating(true);

		createDeposit(
			{
				currency: selectedCoin.currency,
				amount: parseFloat(data.amount),
				paynetwork: data.network.raw.currency,
				method: selectedDepositMethod?.raw?.name || "",
			},
			{
				onSuccess: (result) => {
					
					const payload: any = {
						coin: selectedCoin,
						method: selectedDepositMethod.raw,
						network: data.network,
						amount: data.amount,
						feeInfo: calcResolvedAmount(data.amount),
					};

					
					setDisplayAccountsData({
						data: result.deposit_data.data,
						payload,
					});

					
					setCurrentStep("universalDepositDetails");
				},
				onError: (error) => {
					console.error("Error creating deposit:", error);
					
				},
				onSettled: () => {
					setIsCreating(false);
				},
			},
		);
	};

	return (
		<div className="p-6 space-y-6">
			<div>
				<p className="text-gray-400 text-sm">
					Please complete below to deposit into your{" "}
					{selectedCoin?.currency} account
				</p>
			</div>

			<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
				<div className="relative">
					<label className="block text-sm font-medium text-gray-300 mb-1">
						Network
					</label>
					<Controller
						name="network"
						control={control}
						rules={{ required: "Network is required" }}
						render={({ field }) => (
							<EnhancedSelect<ICryptoUniversalNetworkData>
								options={universalNetworkOptions}
								value={field.value}
								onChange={(value) => {
									field.onChange(value);
									setNetworkError("");
								}}
								placeholder="Select Network"
								isLoading={isLoading}
								isSearchable={false}
								className="w-full"
								displayClassName="p-4"
								renderSelected={(option) => (
									<div className="flex items-center gap-2">
										<span>{option.label}</span>
									</div>
								)}
								renderOption={(option) => (
									<div className="flex items-center gap-2 w-full">
										<div className="flex flex-col">
											<span>{option.label}</span>
										</div>
									</div>
								)}
							/>
						)}
					/>
					{networkError && (
						<p className="text-red-500 text-xs mt-1">
							{networkError}
						</p>
					)}
				</div>

				<div className="relative">
					<Controller
						name="amount"
						control={control}
						rules={{
							required: "Amount is required",
							validate: validateAmount,
						}}
						render={({ field, fieldState }) => (
							<>
								<FloatingLabelInput
									label="Enter Amount"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => {
										
										const value = e.target.value.replace(
											/[^0-9.]/g,
											"",
										);

										
										const parts = value.split(".");
										if (parts.length > 2) {
											return;
										}

										field.onChange(value);
									}}
									className={cn(
										"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
										fieldState.error &&
											"border-red-500 focus:ring-red-200 focus:border-red-500",
									)}
									labelClassName={cn(
										fieldState.error && "text-red-500",
									)}
								/>
								{fieldState.error && (
									<p className="text-red-500 text-xs mt-1">
										{fieldState.error.message}
									</p>
								)}
							</>
						)}
					/>

					{watchAmount && !errors.amount && (
						<div className="text-xs text-gray-400 mt-1 px-4">
							Fee: {calculateFee()}% ~{" "}
							{(
								(parseFloat(watchAmount || "0") *
									calculateFee()) /
								100
							).toFixed(8)}
						</div>
					)}
				</div>

				<Button
					type="submit"
					className={`w-full py-8 rounded-full font-medium mt-6 ${
						watchNetwork &&
						watchAmount &&
						!errors.network &&
						!errors.amount
							? "bg-primary text-white"
							: "bg-gray-700 text-gray-400 cursor-not-allowed"
					}`}
					disabled={
						!watchNetwork ||
						!watchAmount ||
						!!errors.network ||
						!!errors.amount ||
						isCreating ||
						isLoading
					}
				>
					{isCreating ? "Creating..." : "Create Deposit"}
				</Button>
			</form>
		</div>
	);
}
