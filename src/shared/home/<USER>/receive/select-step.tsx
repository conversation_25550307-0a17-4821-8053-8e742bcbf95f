// import useReceiveCryptoStore from "@/store/receive-crypto-store";
// import { formatAmount } from "@/utils/format-amount";
// import { Button } from "@/components/ui/button";
// import { Plus } from "lucide-react";
// import EnhancedSelect from "@/components/enhanced-select";
// import { ICryptoWallet } from "@/types/crypto-wallets";
// import { IOption } from "@/types/general";
// import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
// import { useState } from "react";
// import { ICryptoDepositMethod } from "@/types/crypto-banking";
// import { useGetReceiveCryptoMethods } from "@/hooks/api/crypto-banking";

// export function SelectStep() {
// 	const {
// 		setSelectedDepositMethod,
// 		setSelectedCoin: setSelectedCurrency,
// 		setCurrentStep,
// 	} = useReceiveCryptoStore();

// 	const [selectedCoin, setSelectedCoin] = useState<ICryptoWallet | null>(
// 		null,
// 	);

// 	const [selectedReceiveMethod, setSelectedReceiveMethod] =
// 		useState<IOption<ICryptoDepositMethod> | null>(null);
// 	const {
// 		data: receiveMethods = [],
// 		isLoading: isLoadingCryptoReceiveMethod,
// 	} = useGetReceiveCryptoMethods(selectedCoin?.currency ?? "");

// 	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
// 		useCryptoUserWallets();

// 	const cryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
// 		(wallet) => ({
// 			id: `crypto-${wallet.currency}`,
// 			value: wallet.currency,
// 			label: wallet.currency,
// 			icon: wallet.image,
// 			raw: wallet,
// 		}),
// 	);

// 	const receiveMethodOptions: IOption<ICryptoDepositMethod>[] =
// 		receiveMethods?.map(generateOption).filter((x) => !x.hidden) || [];

// 	const selectedCryptoOption =
// 		cryptoOptions.find(
// 			(option) => option.value === selectedCoin?.currency,
// 		) || null;

// 	function generateOption(
// 		item: ICryptoDepositMethod,
// 	): IOption<ICryptoDepositMethod> {
// 		if (item.name.includes("DYNAMIC")) {
// 			return {
// 				label: "Dynamic Deposit via Link",
// 				icon: <Link02Icon />,
// 				value: "DYNAMIC",
// 				hidden: true,
// 				raw: item,
// 			};
// 		} else if (item.name.includes("UNIVERSAL DEPOSIT")) {
// 			return {
// 				label: "DEPOSIT VIA UNIVERSAL DEPOSIT",
// 				icon: <CircleArrowDown02Icon />,
// 				value: "UNIVERSAL_DEPOSIT",
// 				hidden: false,
// 				raw: item,
// 			};
// 		} else if (
// 			item.name.includes("CLYP") &&
// 			item.name.includes("DEPOSIT")
// 		) {
// 			return {
// 				label:
// 					item?.name ||
// 					`Static Deposit - ${item.name.split("CLYP")[1]}`,
// 				icon: <Coins02Icon />,
// 				value: "STATIC_DEPOSIT",
// 				raw: item,
// 			};
// 		} else {
// 			return {
// 				label: "",
// 				value: "-",
// 				hidden: true,
// 			};
// 		}
// 	}

// 	const handleCryptoChange = (option: IOption<ICryptoWallet> | null) => {
// 		if (option && option.raw) {
// 			setSelectedCoin(option.raw);

// 			setSelectedReceiveMethod(null);
// 		} else {
// 			setSelectedCoin(null);
// 			setSelectedReceiveMethod(null);
// 		}
// 	};

// 	const handleReceiveMethodChange = (
// 		option: IOption<ICryptoDepositMethod> | null,
// 	) => {
// 		if (option && option.raw) {
// 			setSelectedDepositMethod(option);
// 			if (selectedCoin) {
// 				setSelectedCurrency(selectedCoin);
// 			}
// 			setSelectedReceiveMethod(option);

// 			if (option.raw.name.includes("UNIVERSAL DEPOSIT")) {
// 				setCurrentStep("universalDepositForm");
// 			} else if (
// 				option.raw.name.includes("CLYP") &&
// 				option.raw.name.includes("DEPOSIT")
// 			) {
// 				setCurrentStep("staticDeposit");
// 			}
// 		}
// 	};

// 	const handleAddNew = () => {};

// 	const walletHeader = (
// 		<div className="py-2 flex justify-between items-center">
// 			<h3 className="text-2xl font-bold font-body">Wallets</h3>
// 			<Button
// 				onClick={handleAddNew}
// 				className="text-sm flex items-center rounded-full"
// 			>
// 				Add New
// 				<Plus className="h-3 w-3 ml-2" />
// 			</Button>
// 		</div>
// 	);

// 	return (
// 		<div className="p-6 space-y-6">
// 			{/* Coin selection */}
// 			<div className="relative">
// 				<EnhancedSelect<ICryptoWallet>
// 					header={walletHeader}
// 					options={cryptoOptions}
// 					value={selectedCryptoOption}
// 					onChange={handleCryptoChange}
// 					placeholder="Select Cryptocurrency"
// 					isLoading={cryptoLoading}
// 					className="w-full mb-6"
// 					displayClassName="p-4"
// 					renderSelected={(option) => (
// 						<div className="flex items-center gap-2">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<span>{option.value}</span>
// 						</div>
// 					)}
// 					renderOption={(option) => (
// 						<div className="flex items-center gap-2 w-full">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<div className="flex flex-col">
// 								<span>{option.label}</span>
// 								<span className="text-xs text-gray-500 group-hover:text-white">
// 									Balance:{" "}
// 									{formatAmount(
// 										option.raw?.balance,
// 										option.value,
// 									)}
// 								</span>
// 							</div>
// 						</div>
// 					)}
// 				/>
// 			</div>

// 			{/* Receive method selection */}
// 			<div className="relative">
// 				<EnhancedSelect<ICryptoDepositMethod>
// 					options={receiveMethodOptions}
// 					value={selectedReceiveMethod}
// 					onChange={handleReceiveMethodChange}
// 					placeholder="Choose Receive Method"
// 					isLoading={isLoadingCryptoReceiveMethod}
// 					className="w-full mb-6"
// 					displayClassName="p-4"
// 					isSearchable={false}
// 					disabled={!selectedCoin}
// 					renderSelected={(option) => (
// 						<div className="flex items-center gap-2">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<span>{option.label}</span>
// 						</div>
// 					)}
// 					renderOption={(option) => (
// 						<div className="flex items-center gap-2 w-full">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<div className="flex flex-col">
// 								<span>{option.label}</span>
// 							</div>
// 						</div>
// 					)}
// 				/>
// 			</div>
// 		</div>
// 	);
// }

// export const Link02Icon = (props: React.SVGProps<SVGSVGElement>) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M9.5 14.5L14.5 9.49995"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 		/>
// 		<path
// 			d="M16.8463 14.6095L19.4558 12C21.5147 9.94108 21.5147 6.60298 19.4558 4.54411C17.397 2.48524 14.0589 2.48524 12 4.54411L9.39045 7.15366M14.6095 16.8463L12 19.4558C9.94113 21.5147 6.60303 21.5147 4.54416 19.4558C2.48528 17.3969 2.48528 14.0588 4.54416 12L7.1537 9.39041"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 		/>
// 	</svg>
// );

// export const CircleArrowDown02Icon = (props: React.SVGProps<SVGSVGElement>) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<circle
// 			cx="12"
// 			cy="12"
// 			r="10"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 		<path
// 			d="M9.5 13.5C9.99153 14.0057 11.2998 16 12 16M14.5 13.5C14.0085 14.0057 12.7002 16 12 16M12 16V8"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		/>
// 	</svg>
// );

// export const Coins02Icon = (props: React.SVGProps<SVGSVGElement>) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M14 18C18.4183 18 22 14.4183 22 10C22 5.58172 18.4183 2 14 2C9.58172 2 6 5.58172 6 10C6 14.4183 9.58172 18 14 18Z"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 		/>
// 		<path
// 			d="M13.1669 20.9689C12.063 21.6239 10.7742 22 9.3975 22C5.31197 22 2 18.688 2 14.6025C2 13.2258 2.37607 11.937 3.03107 10.8331"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 		/>
// 	</svg>
// );


import useReceiveCryptoStore from "@/store/receive-crypto-store";
import useCryptoSelectionStore from "@/store/crypto-selection-store"; // Import the crypto selection store
import { formatAmount } from "@/utils/format-amount";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import EnhancedSelect from "@/components/enhanced-select";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { IOption } from "@/types/general";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { useState, useEffect } from "react";
import { ICryptoDepositMethod } from "@/types/crypto-banking";
import { useGetReceiveCryptoMethods } from "@/hooks/api/crypto-banking";

export function SelectStep() {
	const {
		setSelectedDepositMethod,
		setSelectedCoin: setSelectedCurrency,
		setCurrentStep,
	} = useReceiveCryptoStore();

	const { selectedCrypto } = useCryptoSelectionStore(); // Get selected crypto from store

	const [selectedCoin, setSelectedCoin] = useState<ICryptoWallet | null>(
		null,
	);
	
	// Add state to track manual selection and store selection
	const [manuallySelectedCrypto, setManuallySelectedCrypto] = useState<string | null>(null);
	const [lastStoreSelection, setLastStoreSelection] = useState<string | null>(null);

	const [selectedReceiveMethod, setSelectedReceiveMethod] =
		useState<IOption<ICryptoDepositMethod> | null>(null);
	const {
		data: receiveMethods = [],
		isLoading: isLoadingCryptoReceiveMethod,
	} = useGetReceiveCryptoMethods(selectedCoin?.currency ?? "");

	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
		useCryptoUserWallets();

	const cryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
		(wallet) => ({
			id: `crypto-${wallet.currency}`,
			value: wallet.currency,
			label: wallet.currency,
			icon: wallet.image,
			raw: wallet,
		}),
	);

	const receiveMethodOptions: IOption<ICryptoDepositMethod>[] =
		receiveMethods?.map(generateOption).filter((x) => !x.hidden) || [];

	const selectedCryptoOption =
		cryptoOptions.find(
			(option) => option.value === selectedCoin?.currency,
		) || null;

	// Auto-select crypto from store when component mounts or when data changes
	useEffect(() => {
		if (cryptoWallets.length === 0) return;

		const currentStoreSelection = selectedCrypto?.currency || null;
		
		// Check if store selection has changed
		const storeSelectionChanged = currentStoreSelection !== lastStoreSelection;
		
		if (storeSelectionChanged) {
			setLastStoreSelection(currentStoreSelection);
			
			// If store has a new selection, use it (this allows store updates to work)
			if (currentStoreSelection) {
				const matchingWallet = cryptoWallets.find(
					wallet => wallet.currency === currentStoreSelection
				);
				
				if (matchingWallet) {
					setSelectedCoin(matchingWallet);
					// Clear manual selection since store provided a new selection
					setManuallySelectedCrypto(null);
					// Reset receive method when crypto changes
					setSelectedReceiveMethod(null);
					return;
				}
			}
		}

		// If we have a manual selection and store hasn't changed, respect manual selection
		if (manuallySelectedCrypto) {
			const manualWallet = cryptoWallets.find(
				wallet => wallet.currency === manuallySelectedCrypto
			);
			
			if (manualWallet) {
				setSelectedCoin(manualWallet);
				return;
			} else {
				// Manual selection is no longer available, clear it
				setManuallySelectedCrypto(null);
			}
		}

		// Fallback: if no manual selection and no store selection, use first available
		if (!selectedCoin && cryptoWallets.length > 0) {
			setSelectedCoin(cryptoWallets[0]);
		}
	}, [selectedCrypto, cryptoWallets, manuallySelectedCrypto, lastStoreSelection, selectedCoin]);

	function generateOption(
		item: ICryptoDepositMethod,
	): IOption<ICryptoDepositMethod> {
		if (item.name.includes("DYNAMIC")) {
			return {
				label: "Dynamic Deposit via Link",
				icon: <Link02Icon />,
				value: "DYNAMIC",
				hidden: true,
				raw: item,
			};
		} else if (item.name.includes("UNIVERSAL DEPOSIT")) {
			return {
				label: "DEPOSIT VIA UNIVERSAL DEPOSIT",
				icon: <CircleArrowDown02Icon />,
				value: "UNIVERSAL_DEPOSIT",
				hidden: false,
				raw: item,
			};
		} else if (
			item.name.includes("CLYP") &&
			item.name.includes("DEPOSIT")
		) {
			return {
				label:
					item?.name ||
					`Static Deposit - ${item.name.split("CLYP")[1]}`,
				icon: <Coins02Icon />,
				value: "STATIC_DEPOSIT",
				raw: item,
			};
		} else {
			return {
				label: "",
				value: "-",
				hidden: true,
			};
		}
	}

	const handleCryptoChange = (option: IOption<ICryptoWallet> | null) => {
		if (!option) {
			setManuallySelectedCrypto(null);
			setSelectedCoin(null);
			setSelectedReceiveMethod(null);
			return;
		}

		// Mark that user has manually selected a crypto
		setManuallySelectedCrypto(option.value);

		if (option.raw) {
			setSelectedCoin(option.raw);
		} else {
			const selectedWallet = cryptoWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setSelectedCoin(selectedWallet || null);
		}

		// Reset receive method when crypto changes
		setSelectedReceiveMethod(null);
	};

	const handleReceiveMethodChange = (
		option: IOption<ICryptoDepositMethod> | null,
	) => {
		if (option && option.raw) {
			setSelectedDepositMethod(option);
			if (selectedCoin) {
				setSelectedCurrency(selectedCoin);
			}
			setSelectedReceiveMethod(option);

			if (option.raw.name.includes("UNIVERSAL DEPOSIT")) {
				setCurrentStep("universalDepositForm");
			} else if (
				option.raw.name.includes("CLYP") &&
				option.raw.name.includes("DEPOSIT")
			) {
				setCurrentStep("staticDeposit");
			}
		}
	};

	const handleAddNew = () => {};

	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	return (
		<div className="p-6 space-y-6">
			{/* Coin selection */}
			<div className="relative">
				<EnhancedSelect<ICryptoWallet>
					header={walletHeader}
					options={cryptoOptions}
					value={selectedCryptoOption}
					onChange={handleCryptoChange}
					placeholder="Select Cryptocurrency"
					isLoading={cryptoLoading}
					className="w-full mb-6"
					displayClassName="p-4"
					renderSelected={(option) => (
						<div className="flex items-center gap-2">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<span>{option.value}</span>
						</div>
					)}
					renderOption={(option) => (
						<div className="flex items-center gap-2 w-full">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<div className="flex flex-col">
								<span>{option.label}</span>
								<span className="text-xs text-gray-500 group-hover:text-white">
									Balance:{" "}
									{formatAmount(
										option.raw?.balance,
										option.value,
									)}
								</span>
							</div>
						</div>
					)}
				/>
			</div>

			{/* Receive method selection */}
			<div className="relative">
				<EnhancedSelect<ICryptoDepositMethod>
					options={receiveMethodOptions}
					value={selectedReceiveMethod}
					onChange={handleReceiveMethodChange}
					placeholder="Choose Receive Method"
					isLoading={isLoadingCryptoReceiveMethod}
					className="w-full mb-6"
					displayClassName="p-4"
					isSearchable={false}
					disabled={!selectedCoin}
					renderSelected={(option) => (
						<div className="flex items-center gap-2">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<span>{option.label}</span>
						</div>
					)}
					renderOption={(option) => (
						<div className="flex items-center gap-2 w-full">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<div className="flex flex-col">
								<span>{option.label}</span>
							</div>
						</div>
					)}
				/>
			</div>
		</div>
	);
}

export const Link02Icon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M9.5 14.5L14.5 9.49995"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
		/>
		<path
			d="M16.8463 14.6095L19.4558 12C21.5147 9.94108 21.5147 6.60298 19.4558 4.54411C17.397 2.48524 14.0589 2.48524 12 4.54411L9.39045 7.15366M14.6095 16.8463L12 19.4558C9.94113 21.5147 6.60303 21.5147 4.54416 19.4558C2.48528 17.3969 2.48528 14.0588 4.54416 12L7.1537 9.39041"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
		/>
	</svg>
);

export const CircleArrowDown02Icon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<circle
			cx="12"
			cy="12"
			r="10"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
		<path
			d="M9.5 13.5C9.99153 14.0057 11.2998 16 12 16M14.5 13.5C14.0085 14.0057 12.7002 16 12 16M12 16V8"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Coins02Icon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M14 18C18.4183 18 22 14.4183 22 10C22 5.58172 18.4183 2 14 2C9.58172 2 6 5.58172 6 10C6 14.4183 9.58172 18 14 18Z"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
		/>
		<path
			d="M13.1669 20.9689C12.063 21.6239 10.7742 22 9.3975 22C5.31197 22 2 18.688 2 14.6025C2 13.2258 2.37607 11.937 3.03107 10.8331"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
		/>
	</svg>
);