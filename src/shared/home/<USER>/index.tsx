import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CreditCard, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import useCreateCardStore, { CreateCardStep } from "@/store/create-card-store";
import { Icon } from "@/components/icons";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import CreateVirtualCardForm from "./create-virtual-card-form";

export function CreateVirtualCardFlow() {
	const { closeDrawer } = useDrawer();

	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);

	const [cardType, setCardtype] = useState<"virtual" | "physical">(null);
	const [accountType,setAccountType] = useState<"crypto"|"fiat">(null);

	const { currentStep, cardData, setCurrentStep } = useCreateCardStore();

	const handleClose = () => {
		closeDrawer();

		setTimeout(() => {
			useCreateCardStore.getState().reset();
			setDirection("forward");
		}, 300);
	};

	const handleStepChange = (
		step: CreateCardStep,
		dir: "forward" | "backward" = "forward",
	) => {
		setDirection(dir);
		setCurrentStep(step);
	};

	const handleBack = () => {
		switch (currentStep) {
			case "accountType":
				handleStepChange("cardType", "backward");
				break;
			case "createCard":
				handleStepChange("accountType", "backward");
				break;
			case "success":
				break;
			default:
				break;
		}
	};

	const handleDone = () => {
		handleClose();
	};

	

	const getStepTitle = () => {
		switch (currentStep) {
			case "cardType":
				return "Select Card Type";
			case "accountType":
				return "Select Account Type";
				return "Select Currency";
			case "createCard":
				return "create Card";
			case "success":
				return null;
			default:
				return "Create Virtual Card";
		}
	};

	const showBackButton = ["accountType","createCard"].includes(currentStep);

	const horizontalVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	const verticalVariants = {
		enter: {
			y: 50,
			opacity: 0,
		},
		center: {
			y: 0,
			opacity: 1,
		},
		exit: {
			y: -50,
			opacity: 0,
		},
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex flex-col mt-10 relative px-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>
				{(getStepTitle() || showBackButton) && (
					<div className="flex items-center justify-center relative">
						{showBackButton && (
							<span
								onClick={handleBack}
								className="absolute left-0 bg-primary-light text-primary rounded-full p-2 cursor-pointer"
							>
								<Icon name="arrow-left" className="size-6" />
							</span>
						)}
						{getStepTitle() && (
							<h2 className="text-2xl font-semibold w-full text-center">
								{getStepTitle()}
							</h2>
						)}
					</div>
				)}
			</div>

			<div className="p-0 h-full overflow-auto relative flex-grow">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{currentStep === "cardType" && (
						<motion.div
							key="cardType"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="flex p-10 justify-start items-center flex-col gap-10"
						>
							<div
								onClick={() => setCardtype("virtual")}
								className={`rounded-lg flex w-full border cursor-pointer p-5 items-center gap-3  transition-all ${
									cardType === "virtual"
										? " border-primary bg-primary/5 "
										: "border-border hover:border-primary/50"
								}`}
							>
								<CreditCard /> <p>Virtual Card</p>
							</div>
							<div
								onClick={() => setCardtype("physical")}
								className={`rounded-lg flex border cursor-pointer p-5 w-full items-center gap-3  transition-all  ${
									cardType === "physical"
										? " border-primary bg-primary/5 "
										: "border-border hover:border-primary/50"
								}`}
							>
								<CreditCard /> <p>Physical Card</p>
							</div>

							{cardType && (
								<Button
									onClick={() =>
										handleStepChange("accountType")
									}
									className="rounded-full w-xs text-lg font-light"
								>
									Continue
								</Button>
							)}
						</motion.div>
					)}
					{currentStep === "accountType" && (
						<motion.div
							key="accountType"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="flex p-10 justify-start items-center flex-col gap-10"
						>
							<div
								onClick={() => setAccountType("crypto")}
								className={`rounded-lg flex w-full border cursor-pointer p-5 items-center gap-3  transition-all ${
									accountType === "crypto"
										? " border-primary bg-primary/5 "
										: "border-border hover:border-primary/50"
								}`}
							>
								<CreditCard /> <p>Crypto account</p>
							</div>
							<div
								onClick={() => setAccountType("fiat")}
								className={`rounded-lg flex border cursor-pointer p-5 w-full items-center gap-3  transition-all  ${
									accountType === "fiat"
										? " border-primary bg-primary/5 "
										: "border-border hover:border-primary/50"
								}`}
							>
								<CreditCard /> <p>Fiat Account</p>
							</div>

							{cardType && (
								<Button
									onClick={() =>
										handleStepChange("createCard")
									}
									className="rounded-full w-xs text-lg font-light"
								>
									Continue
								</Button>
							)}
						</motion.div>
					)}

					{currentStep === "createCard" && (
						<motion.div
							key="billingInfo"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-auto"
						>
							{cardType==="physical" &&(
								<CreateVirtualCardForm
								handleStepChange={() =>
									handleStepChange("success")
								}
							
							/>
							)}

							{cardType === "virtual" &&(
								<div className="p-4 text-center text-muted-foreground">
									<p>Choose a fiat currency account which you want the card to permanently be in and funded with</p>


								<div className="bg-muted-foreground text-gray-900 rounded-full my-3 p-4">Card fee-2000NGN</div>
								<Button
								onClick={()=>handleStepChange("success")}
								>Create virtual card</Button>
								</div>
							)}
							
						</motion.div>
					)}

					{currentStep === "success" && (
						<motion.div
							key="success"
							variants={verticalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								y: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-auto"
						>
							<TransactionSuccess
								title="Card Created!"
								message={
									<p>
										{cardData ? (
											<>
												Your {cardData.currency} virtual
												card has been successfully
												created! <br />
												You can now use it for online
												payments.
											</>
										) : (
											"Your virtual card has been created successfully!"
										)}
									</p>
								}
								buttonText="Done"
								onNextAction={handleDone}
							/>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}
