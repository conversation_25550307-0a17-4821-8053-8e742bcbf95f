import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { TransactionSummaryCard } from "@/components/transaction-summary-card";
import { CryptoTransaction } from "./send-crypto-form";

export interface TransactionReceiptStepProps {
	transaction: CryptoTransaction;
	onShare: () => void;
}

const TransactionReceiptStep: React.FC<TransactionReceiptStepProps> = ({
	transaction,
	onShare,
}) => {
	
	const coinStr =
		typeof transaction.coin === "string"
			? transaction.coin
			: transaction.coin.currency;

	return (
		<div className="flex flex-col gap-4 p-4">
			<TransactionSummaryCard
				amount={transaction.amount}
				currency={coinStr}
				prefix="-"
				status="successful"
				details={[
					{ label: "Network", value: transaction.network },
					{ label: "Address", value: transaction.address },
					{
						label: "Amount",
						value: `${transaction.amount} ${coinStr}`,
					},
					{ label: "Fee", value: transaction.fee },
					{ label: "Source", value: transaction.source },
					{ label: "Date", value: transaction.date },
				]}
			/>
			<Button onClick={onShare} className="w-full py-7 rounded-full">
				Share Receipt
			</Button>
		</div>
	);
};

export default TransactionReceiptStep;
