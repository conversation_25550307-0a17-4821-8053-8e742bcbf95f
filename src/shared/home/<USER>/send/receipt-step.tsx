import { Button } from "@/components/ui/button";
import useSendCryptoStore from "@/store/send-crypto-store";
import { formatAmount } from "@/utils/format-amount";

interface ReceiptStepProps {
	onShareReceipt: () => void;
}

export function ReceiptStep({ onShareReceipt }: ReceiptStepProps) {
	const { transaction } = useSendCryptoStore();

	if (!transaction) {
		return (
			<div className="p-6 text-center">Transaction data not found</div>
		);
	}

	const { from, to_address, amount, network, note } = transaction;

	
	const transactionId =
		"TRX" + Math.random().toString(36).substring(2, 10).toUpperCase();
	const timestamp = new Date().toLocaleString();
	const fee = 0.15 * Number(amount);
	

	return (
		<div className="flex flex-col h-full p-6 bg-gray-900">
			<div className="bg-gray-800 rounded-2xl p-6">
				<div className="border-b border-gray-700 pb-4 mb-4">
					<h3 className="text-lg font-semibold text-center">
						Transaction Details
					</h3>
				</div>

				<div className="space-y-4">
					<div className="flex justify-between">
						<span className="text-gray-400">Transaction ID</span>
						<span className="font-medium">{transactionId}</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Date & Time</span>
						<span className="font-medium">{timestamp}</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Status</span>
						<span className="text-green-500 font-medium">
							Completed
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">From</span>
						<span className="font-medium">
							{from.currency} Wallet
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">To</span>
						<span className="font-medium truncate max-w-[200px]">
							{to_address}
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Network</span>
						<span className="font-medium">{network}</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Amount</span>
						<span className="font-medium">
							{formatAmount(Number(amount), from.currency)}
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Fee</span>
						<span className="font-medium">
							{formatAmount(fee, from.currency)}
						</span>
					</div>

					{note && (
						<div className="flex justify-between">
							<span className="text-gray-400">Note</span>
							<span className="font-medium">{note}</span>
						</div>
					)}
				</div>

				<div className="border-t border-gray-700 mt-4 pt-4">
					<div className="flex justify-between font-bold">
						<span>Total</span>
						<span>
							{formatAmount(Number(amount), from.currency)}
						</span>
					</div>
				</div>
			</div>

			<div className="mt-auto">
				<Button
					className="w-full bg-amber-500 hover:bg-amber-600 text-white p-6 rounded-full mt-4"
					onClick={onShareReceipt}
				>
					Share Receipt
				</Button>
			</div>
		</div>
	);
}
