import React from "react";
import { Button } from "@/components/ui/button";

export interface QRScannerStepProps {
	onScan: (address: string) => void;
	onCancel: () => void;
}

const QRScannerStep: React.FC<QRScannerStepProps> = ({ onScan, onCancel }) => {
	return (
		<div className="flex flex-col h-full bg-gray-900 justify-center items-center">
			<div className="w-64 h-64 border-2 border-amber-500 relative mx-auto my-8">
				<div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-amber-500"></div>
				<div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-amber-500"></div>
				<div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-amber-500"></div>
				<div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-amber-500"></div>

				{/* Placeholder QR code */}
				<div className="p-2">
					<img
						src="/api/placeholder/200/200"
						alt="QR Code"
						className="w-full h-full"
						onClick={() => onScan("0sbfcjh879839239ihks")}
					/>
				</div>
			</div>

			<Button
				variant="secondary"
				onClick={onCancel}
				className="mt-8 w-64"
			>
				Cancel
			</Button>
		</div>
	);
};

export default QRScannerStep;
