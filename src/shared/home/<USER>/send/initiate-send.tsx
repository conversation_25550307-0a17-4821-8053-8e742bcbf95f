import React from "react";
import { But<PERSON> } from "@/components/ui/button";

export interface InitialOptionsStepProps {
	onSendViaWallet: () => void;
	onSendViaUsername: () => void;
}

const InitialOptionsStep: React.FC<InitialOptionsStepProps> = ({
	onSendViaWallet,
	onSendViaUsername,
}) => {
	return (
		<div className="flex flex-col gap-4 p-4">
			<Button
				onClick={onSendViaWallet}
				className="w-full py-7 rounded-full"
			>
				Send via Wallet
			</Button>

			<Button
				variant="outline"
				onClick={onSendViaUsername}
				className="w-full py-7 rounded-full"
			>
				Send via Clypay Username
			</Button>
		</div>
	);
};

export default InitialOptionsStep;
