import React, { useState, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown } from "lucide-react";

export interface Network {
	id: string;
	name: string;
}

interface NetworkSelectorProps {
	networks: Network[];
	selectedNetwork?: Network | null;
	onSelect: (network: Network) => void;
	className?: string;
	title?: string;
	description?: string;
	placeholder?: string;
}

export const NetworkSelector: React.FC<NetworkSelectorProps> = ({
	networks,
	selectedNetwork,
	onSelect,
	className,
	title = "Select Method",
	description = "Select the method you want to use to send your crypto.",
	placeholder = "Choose network",
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const renderArrowIcon = () => (
		<svg
			width="20"
			height="20"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M5 12H19M5 12L11 6M5 12L11 18"
				stroke="currentColor"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	);

	return (
		<div className={cn("relative w-full", className)} ref={dropdownRef}>
			{/* Title and Description */}
			{title && (
				<h2 className="text-2xl font-semibold text-white mb-2">
					{title}
				</h2>
			)}
			{description && <p className="text-gray-300 mb-6">{description}</p>}

			{/* Select Trigger */}
			<motion.div
				className="w-full px-5 py-4 flex items-center justify-between rounded-full border border-gray-700 bg-transparent cursor-pointer"
				onClick={() => setIsOpen(!isOpen)}
				whileTap={{ scale: 0.99 }}
				animate={{
					borderColor: isOpen ? "#6b7280" : "#374151",
				}}
				transition={{ duration: 0.2 }}
			>
				<span
					className={selectedNetwork ? "text-white" : "text-gray-400"}
				>
					{selectedNetwork ? selectedNetwork.name : placeholder}
				</span>
				<motion.div
					animate={{ rotate: isOpen ? 180 : 0 }}
					transition={{
						duration: 0.3,
						type: "spring",
						stiffness: 300,
						damping: 20,
					}}
				>
					<ChevronDown className="h-5 w-5 text-gray-400" />
				</motion.div>
			</motion.div>

			{/* Dropdown Content */}
			<AnimatePresence>
				{isOpen && (
					<motion.div
						className="absolute z-50 mt-2 w-full bg-gray-800 border border-gray-700 rounded-3xl shadow-lg overflow-hidden"
						initial={{ opacity: 0, height: 0, y: -10 }}
						animate={{ opacity: 1, height: "auto", y: 0 }}
						exit={{ opacity: 0, height: 0, y: -10 }}
						transition={{
							duration: 0.2,
							type: "spring",
							stiffness: 500,
							damping: 30,
						}}
					>
						<div className="p-2">
							<motion.div
								className="space-y-2"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 0.1, duration: 0.2 }}
							>
								{networks.map((network, index) => (
									<motion.div
										key={network.id}
										className="flex items-center px-5 py-4 rounded-full cursor-pointer text-white hover:bg-gray-700"
										onClick={() => {
											onSelect(network);
											setIsOpen(false);
										}}
										initial={{ opacity: 0, y: 10 }}
										animate={{ opacity: 1, y: 0 }}
										exit={{ opacity: 0, y: 10 }}
										transition={{
											delay: 0.05 + index * 0.05,
											duration: 0.2,
										}}
										whileTap={{ scale: 0.98 }}
									>
										<span className="mr-4 text-white">
											{renderArrowIcon()}
										</span>
										<span className="font-medium tracking-wide">
											{network.name}
										</span>
									</motion.div>
								))}
							</motion.div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
};
