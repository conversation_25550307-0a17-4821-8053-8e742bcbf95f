// import { useState, useEffect } from "react";
// import { Button } from "@/components/ui/button";
// import useSendCryptoStore from "@/store/send-crypto-store";
// import { ICryptoWithdrawalMethod } from "@/types/crypto-banking";
// import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
// import EnhancedSelect from "@/components/enhanced-select";
// import { Plus } from "lucide-react";
// import { useGetCoinWithdrawalMethod } from "@/hooks/api/crypto-banking";
// import FloatingLabelInput from "@/components/custom/input/floating-label-input";
// import cn from "@/utils/class-names";
// import { useForm } from "react-hook-form";
// import { ICryptoWallet } from "@/types/crypto-wallets";
// import { IOption } from "@/types/general";
// import { formatAmount } from "@/utils/format-amount";
// import { useGetUserCustomerByClypId } from "@/hooks/api/user";
// import { useDebounce } from "@/hooks/use-debounce";

// interface SendCryptoStepProps {
// 	onProceed: () => void;
// }

// interface SendCryptoFormData {
// 	amount: string;
// 	address?: string;
// 	identifier?: string;
// 	note: string;
// }

// export interface IResolved {
// 	fee: number;
// 	amount: number;
// }

// const calcResolvedAmount = (
// 	amount: string,
// 	selectedMethodOption: IOption<ICryptoWithdrawalMethod> | null,
// 	resolveCalculation: "add" | "minus" = "minus",
// ): IResolved => {
// 	const parsedAmount = parseFloat(amount || "0");
// 	const feeAmount = parseFloat(selectedMethodOption?.raw?.fee || "0");

// 	if (isNaN(parsedAmount) || isNaN(feeAmount)) {
// 		return {
// 			fee: 0,
// 			amount: 0,
// 		};
// 	}

// 	if (selectedMethodOption?.raw?.fee_type === "PERCENTAGE") {
// 		const fee = (parsedAmount * feeAmount) / 100;
// 		const resolvedAmount =
// 			resolveCalculation === "add"
// 				? parsedAmount + fee
// 				: parsedAmount - fee;
// 		return {
// 			fee,
// 			amount: resolvedAmount >= 0 ? resolvedAmount : 0,
// 		};
// 	} else {
// 		const fee = feeAmount;
// 		const resolvedAmount =
// 			resolveCalculation === "add"
// 				? parsedAmount + fee
// 				: parsedAmount - fee;
// 		return {
// 			fee: fee,
// 			amount: resolvedAmount >= 0 ? resolvedAmount : 0,
// 		};
// 	}
// };

// export function SendCryptoStep({ onProceed }: SendCryptoStepProps) {
// 	const {
// 		register,
// 		formState: { errors },

// 		watch,
// 		handleSubmit,
// 		reset,
// 	} = useForm<SendCryptoFormData>({
// 		mode: "all",
// 		defaultValues: {
// 			amount: "",
// 			address: "",
// 			identifier: "",
// 			note: "",
// 		},
// 	});

// 	console.log(errors);

// 	const { setTransaction } = useSendCryptoStore();

// 	const [selectedCoin, setSelectedCoin] = useState<ICryptoWallet | null>(
// 		null,
// 	);
// 	const [selectedMethodOption, setSelectedMethodOption] =
// 		useState<IOption<ICryptoWithdrawalMethod> | null>(null);
// 	const [error, setError] = useState<string | null>(null);

// 	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
// 		useCryptoUserWallets();

// 	const { data: withdrawalMethods = [], isLoading: withdrawalMethodLoading } =
// 		useGetCoinWithdrawalMethod(selectedCoin?.currency ?? "");

// 	const debouncedIdentifier = useDebounce(watch("identifier") || "", 500);

// 	const { data: userData } = useGetUserCustomerByClypId(debouncedIdentifier);

// 	const cryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
// 		(wallet) => ({
// 			id: `crypto-${wallet.currency}`,
// 			value: wallet.currency,
// 			label: wallet.currency,
// 			icon: wallet.image,
// 			raw: wallet,
// 		}),
// 	);

// 	function generateOption(
// 		item: ICryptoWithdrawalMethod,
// 	): IOption<ICryptoWithdrawalMethod> {
// 		if (item.name.includes("INTERNAL_TRANSFER")) {
// 			return {
// 				label: "Send to Clyppay User via username",
// 				icon: <ArrowDataTransferHorizontalIcon />,
// 				value: "INTERNAL_TRANSFER",
// 				hidden: true,
// 				raw: item,
// 			};
// 		} else if (item.name.includes("INTERNAL TRANSFER")) {
// 			return {
// 				label: "SEND TO CLYPPAY USER",
// 				icon: <ArrowDataTransferHorizontalIcon />,
// 				value: "INTERNAL_TRANSFER",
// 				hidden: false,
// 				raw: item,
// 			};
// 		} else if (item.name.includes("UNIVERSAL")) {
// 			return {
// 				label: `Send to any ${selectedCoin?.currency} Wallet`,
// 				icon: <ArrowDataTransferHorizontalIcon />,
// 				value: "UNIVERSAL",
// 				hidden: true,
// 				raw: item,
// 			};
// 		} else if (item.name.includes("DYNAMIC WITHDRAWAL")) {
// 			return {
// 				label: "Make Dynamic Withdrawal",
// 				icon: <BankIcon />,
// 				value: "DYNAMIC_WITHDRAWAL",
// 				hidden: true,
// 				raw: item,
// 			};
// 		} else if (item.name.includes("WITHDRAWAL(")) {
// 			return {
// 				hidden: true,
// 				label: "Withdraw to Bank",
// 				icon: <BankIcon />,
// 				value: "WITHDRAWAL",
// 				raw: item,
// 			};
// 		} else {
// 			return {
// 				label: item?.name,
// 				value: "STATIC_SEND",
// 				raw: item,
// 				icon: <ArrowDataTransferHorizontalIcon />,
// 			};
// 		}
// 	}

// 	const withdrawalMethodOptions: IOption<ICryptoWithdrawalMethod>[] =
// 		withdrawalMethods?.map(generateOption).filter((x) => !x.hidden);

// 	const selectedCryptoOption =
// 		cryptoOptions.find(
// 			(option) => option.value === selectedCoin?.currency,
// 		) || null;

// 	useEffect(() => {
// 		setSelectedMethodOption(null);
// 		reset({
// 			amount: "",
// 			address: "",
// 			identifier: "",
// 			note: "",
// 		});
// 	}, [selectedCoin, reset]);

// 	const handleCryptoChange = (option: IOption<ICryptoWallet> | null) => {
// 		if (option && option.raw) {
// 			setSelectedCoin(option.raw);
// 		} else {
// 			setSelectedCoin(null);
// 		}
// 	};

// 	const handleWithdrawalMethodChange = (
// 		option: IOption<ICryptoWithdrawalMethod> | null,
// 	) => {
// 		setSelectedMethodOption(option);

// 		reset({
// 			amount: "",
// 			address: "",
// 			identifier: "",
// 			note: "",
// 		});
// 	};

// 	const handleAddNew = () => {
// 		console.log("Add new wallet clicked");
// 	};

// 	const walletHeader = (
// 		<div className="py-2 flex justify-between items-center">
// 			<h3 className="text-2xl font-bold font-body">Wallets</h3>
// 			<Button
// 				onClick={handleAddNew}
// 				className="text-sm flex items-center rounded-full"
// 			>
// 				Add New
// 				<Plus className="h-3 w-3 ml-2" />
// 			</Button>
// 		</div>
// 	);

// 	const isFormValid = () => {
// 		if (!selectedCoin) return false;
// 		if (!selectedMethodOption) return false;

// 		const amount = watch("amount");
// 		const note = watch("note");

// 		if (selectedMethodOption?.value === "INTERNAL_TRANSFER") {
// 			const identifier = watch("identifier");
// 			if (!identifier) return false;
// 		} else {
// 			const address = watch("address");
// 			if (!address) return false;
// 		}

// 		if (!amount || isNaN(Number(amount)) || Number(amount) <= 0)
// 			return false;

// 		const resolved = calcResolvedAmount(
// 			amount,
// 			selectedMethodOption,
// 			"add",
// 		);
// 		if (resolved.amount > selectedCoin.balance) {
// 			return false;
// 		}

// 		if (!note || note.trim() === "") return false;

// 		return true;
// 	};

// 	const handleProceed = (data: SendCryptoFormData) => {
// 		if (!selectedMethodOption?.raw || !selectedCoin) {
// 			setError("Invalid withdrawal method or coin");
// 			return;
// 		}

// 		let finalAddress = "";
// 		if (selectedMethodOption?.value === "INTERNAL_TRANSFER") {
// 			finalAddress = userData?.id || "";
// 		} else {
// 			finalAddress = data.address || "";
// 		}

// 		setTransaction({
// 			from: selectedCoin,
// 			method: selectedMethodOption.raw,
// 			to_address: finalAddress,
// 			amount: data.amount,
// 			description: data.note,
// 			network: selectedMethodOption.raw.network || "",
// 			feeInfo: calcResolvedAmount(data.amount, selectedMethodOption),

// 			...(selectedMethodOption?.value === "INTERNAL_TRANSFER" && userData
// 				? { userData: userData, type: "internal" }
// 				: null),
// 		});

// 		onProceed();
// 	};

// 	return (
// 		<div className="flex flex-col h-full p-6">
// 			<form
// 				onSubmit={handleSubmit(handleProceed)}
// 				className="space-y-6 w-full"
// 			>
// 				{/* Coin selection */}
// 				<div className="relative">
// 					<EnhancedSelect<ICryptoWallet>
// 						header={walletHeader}
// 						options={cryptoOptions}
// 						value={selectedCryptoOption}
// 						onChange={handleCryptoChange}
// 						placeholder="Select Cryptocurrency"
// 						isLoading={cryptoLoading}
// 						className="w-full mb-6"
// 						displayClassName="p-4"
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>{option.value}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<div className="flex flex-col">
// 									<span>{option.label}</span>
// 									<span className="text-xs text-gray-500 group-hover:text-white">
// 										Balance:{" "}
// 										{formatAmount(
// 											option.raw?.balance,
// 											option.value,
// 										)}
// 									</span>
// 								</div>
// 							</div>
// 						)}
// 					/>
// 				</div>

// 				{/* Withdrawal method selection */}
// 				<div className="relative">
// 					<EnhancedSelect<ICryptoWithdrawalMethod>
// 						options={withdrawalMethodOptions}
// 						value={selectedMethodOption}
// 						onChange={handleWithdrawalMethodChange}
// 						placeholder="Select Crypto Send Method"
// 						isLoading={withdrawalMethodLoading}
// 						isSearchable={false}
// 						disabled={!selectedCoin}
// 						className="w-full mb-6"
// 						displayClassName="p-4"
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								{option.icon}
// 								<span>{option.label}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								{option.icon}
// 								<span>{option.label}</span>
// 							</div>
// 						)}
// 					/>
// 				</div>

// 				{/* Recipient address or Clyp ID */}
// 				<div className="relative">
// 					{selectedMethodOption?.value === "INTERNAL_TRANSFER" ? (
// 						<>
// 							<FloatingLabelInput
// 								label="Enter or paste Clyp ID"
// 								type="text"
// 								{...register("identifier", {
// 									required: "Clyp ID is required",
// 									disabled:
// 										!selectedMethodOption || !selectedCoin,
// 								})}
// 								className={cn(
// 									"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
// 									errors.identifier &&
// 										"border-red-500 focus:ring-red-200 focus:border-red-500",
// 								)}
// 								labelClassName={cn(
// 									errors.identifier && "text-red-500",
// 								)}
// 							/>
// 							<p className="text-sm text-gray-400 mt-1 ml-4">
// 								{userData
// 									? `${userData.first_name} ${userData.last_name}`
// 									: "Must be a valid Clyp ID"}
// 							</p>
// 						</>
// 					) : (
// 						<>
// 							<FloatingLabelInput
// 								label="Paste Wallet Address"
// 								type="text"
// 								{...register("address", {
// 									required: "Wallet address is required",
// 									disabled:
// 										!selectedMethodOption || !selectedCoin,
// 								})}
// 								className={cn(
// 									"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
// 									errors.address &&
// 										"border-red-500 focus:ring-red-200 focus:border-red-500",
// 								)}
// 								labelClassName={cn(
// 									errors.address && "text-red-500",
// 								)}
// 							/>
// 							<p className="text-sm text-gray-400 mt-1 ml-4">
// 								Must be a valid wallet address
// 							</p>
// 						</>
// 					)}
// 				</div>

// 				{/* Amount */}
// 				<div className="relative">
// 					<FloatingLabelInput
// 						label="Enter Amount"
// 						type="text"
// 						{...register("amount", {
// 							required: "Amount is required",
// 							pattern: {
// 								value: /^\d*\.?\d*$/,
// 								message: "Please enter a valid amount",
// 							},
// 							validate: (value) => {
// 								const amount = parseFloat(value);
// 								if (amount <= 0) {
// 									return "Amount must be greater than 0";
// 								}

// 								// Calculate total amount including fee
// 								// const resolved = calcResolvedAmount(value, selectedMethodOption, "add");

// 								// if (
// 								// 	selectedCoin &&
// 								// 	resolved.amount > selectedCoin.balance
// 								// ) {
// 								// 	return "Insufficient balance (including fee)";
// 								// }

// 								// Add restriction checks
// 								const minAmount =
// 									selectedMethodOption?.raw?.restrictions
// 										?.TransactionLimits?.[0];
// 								const maxAmount =
// 									selectedMethodOption?.raw?.restrictions
// 										?.TransactionLimits?.[1];

// 								if (minAmount && amount < minAmount) {
// 									return `Minimum amount is ${minAmount}`;
// 								}
// 								if (maxAmount && amount > maxAmount) {
// 									return `Maximum amount is ${maxAmount}`;
// 								}
// 								return true;
// 							},
// 							disabled: !selectedMethodOption || !selectedCoin,
// 						})}
// 						className={cn(
// 							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
// 							errors.amount &&
// 								"border-red-500 focus:ring-red-200 focus:border-red-500",
// 						)}
// 						labelClassName={cn(errors.amount && "text-red-500")}
// 					/>
// 					{errors.amount ? (
// 						<span className="text-red-500 text-sm">
// 							{errors.amount.message}
// 						</span>
// 					) : (
// 						<p className="text-sm text-gray-400 mt-1 ml-4">
// 							Fee:{" "}
// 							{
// 								calcResolvedAmount(
// 									watch("amount"),
// 									selectedMethodOption,
// 								).fee
// 							}
// 						</p>
// 					)}
// 				</div>

// 				{/* Note (now required) */}
// 				<div className="relative">
// 					<FloatingLabelInput
// 						label="Add Note"
// 						type="text"
// 						{...register("note", {
// 							required: "Note is required",
// 							minLength: {
// 								value: 3,
// 								message: "Note must be at least 3 characters",
// 							},
// 							disabled: !selectedMethodOption || !selectedCoin,
// 						})}
// 						className={cn(
// 							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
// 							errors.note &&
// 								"border-red-500 focus:ring-red-200 focus:border-red-500",
// 						)}
// 						labelClassName={cn(errors.note && "text-red-500")}
// 					/>
// 					{errors.note ? (
// 						<span className="text-red-500 text-sm">
// 							{errors.note.message}
// 						</span>
// 					) : (
// 						<p className="text-sm text-gray-400 mt-1 ml-4">
// 							Add a descriptive note for reference
// 						</p>
// 					)}
// 				</div>

// 				{/* Total Amount */}
// 				<div className="text-center mt-8">
// 					<p className="text-gray-400">Total Amount to be deducted</p>
// 					<p className="text-xl font-bold">
// 						{watch("amount")
// 							? calcResolvedAmount(
// 									watch("amount"),
// 									selectedMethodOption,
// 									"add",
// 							  ).amount.toFixed(6)
// 							: "0.000000"}
// 					</p>
// 				</div>

// 				{/* Error message */}
// 				{error && (
// 					<div className="text-red-500 text-center">{error}</div>
// 				)}

// 				{/* Proceed button */}
// 				<Button
// 					type="submit"
// 					className="w-full bg-primary hover:bg-primary/80 text-white p-6 rounded-full mt-4"
// 					disabled={!isFormValid()}
// 				>
// 					Proceed
// 				</Button>
// 			</form>
// 		</div>
// 	);
// }

// // Options Icons

// const ArrowDataTransferHorizontalIcon = (
// 	props: React.SVGProps<SVGSVGElement>,
// ) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M19 9H6.65856C5.65277 9 5.14987 9 5.02472 8.69134C4.89957 8.38268 5.25517 8.01942 5.96637 7.29289L8.21091 5"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		/>
// 		<path
// 			d="M5 15H17.3414C18.3472 15 18.8501 15 18.9753 15.3087C19.1004 15.6173 18.7448 15.9806 18.0336 16.7071L15.7891 19"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		/>
// 	</svg>
// );

// const BankIcon = (props: React.SVGProps<SVGSVGElement>) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M2 8.56907C2 7.37289 2.48238 6.63982 3.48063 6.08428L7.58987 3.79744C9.7431 2.59915 10.8197 2 12 2C13.1803 2 14.2569 2.59915 16.4101 3.79744L20.5194 6.08428C21.5176 6.63982 22 7.3729 22 8.56907C22 8.89343 22 9.05561 21.9646 9.18894C21.7785 9.88945 21.1437 10 20.5307 10H3.46928C2.85627 10 2.22152 9.88944 2.03542 9.18894C2 9.05561 2 8.89343 2 8.56907Z"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 		<path
// 			d="M11.9959 7H12.0049"
// 			stroke="currentColor"
// 			strokeWidth="2"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		/>
// 		<path
// 			d="M4 10V18.5M8 10V18.5"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 		<path
// 			d="M16 10V18.5M20 10V18.5"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 		<path
// 			d="M19 18.5H5C3.34315 18.5 2 19.8431 2 21.5C2 21.7761 2.22386 22 2.5 22H21.5C21.7761 22 22 21.7761 22 21.5C22 19.8431 20.6569 18.5 19 18.5Z"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 	</svg>
// );


import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import useSendCryptoStore from "@/store/send-crypto-store";
import { ICryptoWithdrawalMethod } from "@/types/crypto-banking";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import EnhancedSelect from "@/components/enhanced-select";
import { Plus } from "lucide-react";
import { useGetCoinWithdrawalMethod } from "@/hooks/api/crypto-banking";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import cn from "@/utils/class-names";
import { useForm } from "react-hook-form";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { IOption } from "@/types/general";
import { formatAmount } from "@/utils/format-amount";
import { useGetUserCustomerByClypId } from "@/hooks/api/user";
import { useDebounce } from "@/hooks/use-debounce";
import useCryptoSelectionStore from "@/store/crypto-selection-store";

interface SendCryptoStepProps {
	onProceed: () => void;
}

interface SendCryptoFormData {
	amount: string;
	address?: string;
	identifier?: string;
	note: string;
}

export interface IResolved {
	fee: number;
	amount: number;
}

const calcResolvedAmount = (
	amount: string,
	selectedMethodOption: IOption<ICryptoWithdrawalMethod> | null,
	resolveCalculation: "add" | "minus" = "minus",
): IResolved => {
	const parsedAmount = parseFloat(amount || "0");
	const feeAmount = parseFloat(selectedMethodOption?.raw?.fee || "0");

	if (isNaN(parsedAmount) || isNaN(feeAmount)) {
		return {
			fee: 0,
			amount: 0,
		};
	}

	if (selectedMethodOption?.raw?.fee_type === "PERCENTAGE") {
		const fee = (parsedAmount * feeAmount) / 100;
		const resolvedAmount =
			resolveCalculation === "add"
				? parsedAmount + fee
				: parsedAmount - fee;
		return {
			fee,
			amount: resolvedAmount >= 0 ? resolvedAmount : 0,
		};
	} else {
		const fee = feeAmount;
		const resolvedAmount =
			resolveCalculation === "add"
				? parsedAmount + fee
				: parsedAmount - fee;
		return {
			fee: fee,
			amount: resolvedAmount >= 0 ? resolvedAmount : 0,
		};
	}
};

export function SendCryptoStep({ onProceed }: SendCryptoStepProps) {
	const {
		register,
		formState: { errors },
		watch,
		handleSubmit,
		reset,
	} = useForm<SendCryptoFormData>({
		mode: "all",
		defaultValues: {
			amount: "",
			address: "",
			identifier: "",
			note: "",
		},
	});

	const { setTransaction } = useSendCryptoStore();
	const { selectedCrypto } = useCryptoSelectionStore();

	const [selectedCoin, setSelectedCoin] = useState<ICryptoWallet | null>(null);
	const [selectedMethodOption, setSelectedMethodOption] =
		useState<IOption<ICryptoWithdrawalMethod> | null>(null);
	const [error, setError] = useState<string | null>(null);
	
	// Auto/manual selection state - same pattern as BuyCrypto
	const [manuallySelectedCrypto, setManuallySelectedCrypto] = useState<string | null>(null);
	const [lastStoreSelection, setLastStoreSelection] = useState<string | null>(null);

	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
		useCryptoUserWallets();

	const { data: withdrawalMethods = [], isLoading: withdrawalMethodLoading } =
		useGetCoinWithdrawalMethod(selectedCoin?.currency ?? "");

	const debouncedIdentifier = useDebounce(watch("identifier") || "", 500);

	const { data: userData } = useGetUserCustomerByClypId(debouncedIdentifier);

	const cryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
		(wallet) => ({
			id: `crypto-${wallet.currency}`,
			value: wallet.currency,
			label: wallet.currency,
			icon: wallet.image,
			raw: wallet,
		}),
	);

	function generateOption(
		item: ICryptoWithdrawalMethod,
	): IOption<ICryptoWithdrawalMethod> {
		if (item.name.includes("INTERNAL_TRANSFER")) {
			return {
				label: "Send to Clyppay User via username",
				icon: <ArrowDataTransferHorizontalIcon />,
				value: "INTERNAL_TRANSFER",
				hidden: true,
				raw: item,
			};
		} else if (item.name.includes("INTERNAL TRANSFER")) {
			return {
				label: "SEND TO CLYPPAY USER",
				icon: <ArrowDataTransferHorizontalIcon />,
				value: "INTERNAL_TRANSFER",
				hidden: false,
				raw: item,
			};
		} else if (item.name.includes("UNIVERSAL")) {
			return {
				label: `Send to any ${selectedCoin?.currency} Wallet`,
				icon: <ArrowDataTransferHorizontalIcon />,
				value: "UNIVERSAL",
				hidden: true,
				raw: item,
			};
		} else if (item.name.includes("DYNAMIC WITHDRAWAL")) {
			return {
				label: "Make Dynamic Withdrawal",
				icon: <BankIcon />,
				value: "DYNAMIC_WITHDRAWAL",
				hidden: true,
				raw: item,
			};
		} else if (item.name.includes("WITHDRAWAL(")) {
			return {
				hidden: true,
				label: "Withdraw to Bank",
				icon: <BankIcon />,
				value: "WITHDRAWAL",
				raw: item,
			};
		} else {
			return {
				label: item?.name,
				value: "STATIC_SEND",
				raw: item,
				icon: <ArrowDataTransferHorizontalIcon />,
			};
		}
	}

	const withdrawalMethodOptions: IOption<ICryptoWithdrawalMethod>[] =
		withdrawalMethods?.map(generateOption).filter((x) => !x.hidden);

	const selectedCryptoOption =
		cryptoOptions.find(
			(option) => option.value === selectedCoin?.currency,
		) || null;

	// Auto-select crypto from store when component mounts or when data changes - same logic as BuyCrypto
	useEffect(() => {
		if (cryptoWallets.length === 0) return;

		const currentStoreSelection = selectedCrypto?.currency || null;
		
		// Check if store selection has changed
		const storeSelectionChanged = currentStoreSelection !== lastStoreSelection;
		
		if (storeSelectionChanged) {
			setLastStoreSelection(currentStoreSelection);
			
			// If store has a new selection, use it (this allows store updates to work)
			if (currentStoreSelection) {
				const matchingWallet = cryptoWallets.find(
					wallet => wallet.currency === currentStoreSelection
				);
				
				if (matchingWallet) {
					setSelectedCoin(matchingWallet);
					// Clear manual selection since store provided a new selection
					setManuallySelectedCrypto(null);
					return;
				}
			}
		}

		// If we have a manual selection and store hasn't changed, respect manual selection
		if (manuallySelectedCrypto) {
			const manualWallet = cryptoWallets.find(
				wallet => wallet.currency === manuallySelectedCrypto
			);
			
			if (manualWallet) {
				setSelectedCoin(manualWallet);
				return;
			} else {
				// Manual selection is no longer available, clear it
				setManuallySelectedCrypto(null);
			}
		}

		// Fallback: if no manual selection and no store selection, use first available
		if (!selectedCoin && cryptoWallets.length > 0) {
			setSelectedCoin(cryptoWallets[0]);
		}
	}, [selectedCrypto, cryptoWallets, manuallySelectedCrypto, lastStoreSelection, selectedCoin]);

	useEffect(() => {
		setSelectedMethodOption(null);
		reset({
			amount: "",
			address: "",
			identifier: "",
			note: "",
		});
	}, [selectedCoin, reset]);

	const handleCryptoChange = (option: IOption<ICryptoWallet> | null) => {
		if (!option) {
			setManuallySelectedCrypto(null);
			setSelectedCoin(null);
			return;
		}

		// Mark that user has manually selected a crypto
		setManuallySelectedCrypto(option.value);

		if (option.raw) {
			setSelectedCoin(option.raw);
		} else {
			const selectedWallet = cryptoWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setSelectedCoin(selectedWallet || null);
		}
	};

	const handleWithdrawalMethodChange = (
		option: IOption<ICryptoWithdrawalMethod> | null,
	) => {
		setSelectedMethodOption(option);

		reset({
			amount: "",
			address: "",
			identifier: "",
			note: "",
		});
	};

	const handleAddNew = () => {
		console.log("Add new wallet clicked");
	};

	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	const isFormValid = () => {
		if (!selectedCoin) return false;
		if (!selectedMethodOption) return false;

		const amount = watch("amount");
		const note = watch("note");

		if (selectedMethodOption?.value === "INTERNAL_TRANSFER") {
			const identifier = watch("identifier");
			if (!identifier) return false;
		} else {
			const address = watch("address");
			if (!address) return false;
		}

		if (!amount || isNaN(Number(amount)) || Number(amount) <= 0)
			return false;

		const resolved = calcResolvedAmount(
			amount,
			selectedMethodOption,
			"add",
		);
		if (resolved.amount > selectedCoin.balance) {
			return false;
		}

		if (!note || note.trim() === "") return false;

		return true;
	};

	const handleProceed = (data: SendCryptoFormData) => {
		if (!selectedMethodOption?.raw || !selectedCoin) {
			setError("Invalid withdrawal method or coin");
			return;
		}

		let finalAddress = "";
		if (selectedMethodOption?.value === "INTERNAL_TRANSFER") {
			finalAddress = userData?.id || "";
		} else {
			finalAddress = data.address || "";
		}

		setTransaction({
			from: selectedCoin,
			method: selectedMethodOption.raw,
			to_address: finalAddress,
			amount: data.amount,
			description: data.note,
			network: selectedMethodOption.raw.network || "",
			feeInfo: calcResolvedAmount(data.amount, selectedMethodOption),

			...(selectedMethodOption?.value === "INTERNAL_TRANSFER" && userData
				? { userData: userData, type: "internal" }
				: null),
		});

		onProceed();
	};

	return (
		<div className="flex flex-col h-full p-6">
			<form
				onSubmit={handleSubmit(handleProceed)}
				className="space-y-6 w-full"
			>
				{/* Coin selection */}
				<div className="relative">
					<EnhancedSelect<ICryptoWallet>
						header={walletHeader}
						options={cryptoOptions}
						value={selectedCryptoOption}
						onChange={handleCryptoChange}
						placeholder="Select Cryptocurrency"
						isLoading={cryptoLoading}
						className="w-full mb-6"
						displayClassName="p-4"
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>{option.value}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<div className="flex flex-col">
									<span>{option.label}</span>
									<span className="text-xs text-gray-500 group-hover:text-white">
										Balance:{" "}
										{formatAmount(
											option.raw?.balance,
											option.value,
										)}
									</span>
								</div>
							</div>
						)}
					/>
				</div>

				{/* Withdrawal method selection */}
				<div className="relative">
					<EnhancedSelect<ICryptoWithdrawalMethod>
						options={withdrawalMethodOptions}
						value={selectedMethodOption}
						onChange={handleWithdrawalMethodChange}
						placeholder="Select Crypto Send Method"
						isLoading={withdrawalMethodLoading}
						isSearchable={false}
						disabled={!selectedCoin}
						className="w-full mb-6"
						displayClassName="p-4"
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								{option.icon}
								<span>{option.label}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								{option.icon}
								<span>{option.label}</span>
							</div>
						)}
					/>
				</div>

				{/* Recipient address or Clyp ID */}
				<div className="relative">
					{selectedMethodOption?.value === "INTERNAL_TRANSFER" ? (
						<>
							<FloatingLabelInput
								label="Enter or paste Clyp ID"
								type="text"
								{...register("identifier", {
									required: "Clyp ID is required",
									disabled:
										!selectedMethodOption || !selectedCoin,
								})}
								className={cn(
									"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
									errors.identifier &&
										"border-red-500 focus:ring-red-200 focus:border-red-500",
								)}
								labelClassName={cn(
									errors.identifier && "text-red-500",
								)}
							/>
							<p className="text-sm text-gray-400 mt-1 ml-4">
								{userData
									? `${userData.first_name} ${userData.last_name}`
									: "Must be a valid Clyp ID"}
							</p>
						</>
					) : (
						<>
							<FloatingLabelInput
								label="Paste Wallet Address"
								type="text"
								{...register("address", {
									required: "Wallet address is required",
									disabled:
										!selectedMethodOption || !selectedCoin,
								})}
								className={cn(
									"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
									errors.address &&
										"border-red-500 focus:ring-red-200 focus:border-red-500",
								)}
								labelClassName={cn(
									errors.address && "text-red-500",
								)}
							/>
							<p className="text-sm text-gray-400 mt-1 ml-4">
								Must be a valid wallet address
							</p>
						</>
					)}
				</div>

				{/* Amount */}
				<div className="relative">
					<FloatingLabelInput
						label="Enter Amount"
						type="text"
						{...register("amount", {
							required: "Amount is required",
							pattern: {
								value: /^\d*\.?\d*$/,
								message: "Please enter a valid amount",
							},
							validate: (value) => {
								const amount = parseFloat(value);
								if (amount <= 0) {
									return "Amount must be greater than 0";
								}

								// Add restriction checks
								const minAmount =
									selectedMethodOption?.raw?.restrictions
										?.TransactionLimits?.[0];
								const maxAmount =
									selectedMethodOption?.raw?.restrictions
										?.TransactionLimits?.[1];

								if (minAmount && amount < minAmount) {
									return `Minimum amount is ${minAmount}`;
								}
								if (maxAmount && amount > maxAmount) {
									return `Maximum amount is ${maxAmount}`;
								}
								return true;
							},
							disabled: !selectedMethodOption || !selectedCoin,
						})}
						className={cn(
							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
							errors.amount &&
								"border-red-500 focus:ring-red-200 focus:border-red-500",
						)}
						labelClassName={cn(errors.amount && "text-red-500")}
					/>
					{errors.amount ? (
						<span className="text-red-500 text-sm">
							{errors.amount.message}
						</span>
					) : (
						<p className="text-sm text-gray-400 mt-1 ml-4">
							Fee:{" "}
							{
								calcResolvedAmount(
									watch("amount"),
									selectedMethodOption,
								).fee
							}
						</p>
					)}
				</div>

				{/* Note (now required) */}
				<div className="relative">
					<FloatingLabelInput
						label="Add Note"
						type="text"
						{...register("note", {
							required: "Note is required",
							minLength: {
								value: 3,
								message: "Note must be at least 3 characters",
							},
							disabled: !selectedMethodOption || !selectedCoin,
						})}
						className={cn(
							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
							errors.note &&
								"border-red-500 focus:ring-red-200 focus:border-red-500",
						)}
						labelClassName={cn(errors.note && "text-red-500")}
					/>
					{errors.note ? (
						<span className="text-red-500 text-sm">
							{errors.note.message}
						</span>
					) : (
						<p className="text-sm text-gray-400 mt-1 ml-4">
							Add a descriptive note for reference
						</p>
					)}
				</div>

				{/* Total Amount */}
				<div className="text-center mt-8">
					<p className="text-gray-400">Total Amount to be deducted</p>
					<p className="text-xl font-bold">
						{watch("amount")
							? calcResolvedAmount(
									watch("amount"),
									selectedMethodOption,
									"add",
							  ).amount.toFixed(6)
							: "0.000000"}
					</p>
				</div>

				{/* Error message */}
				{error && (
					<div className="text-red-500 text-center">{error}</div>
				)}

				{/* Proceed button */}
				<Button
					type="submit"
					className="w-full bg-primary hover:bg-primary/80 text-white p-6 rounded-full mt-4"
					disabled={!isFormValid()}
				>
					Proceed
				</Button>
			</form>
		</div>
	);
}

// Options Icons

const ArrowDataTransferHorizontalIcon = (
	props: React.SVGProps<SVGSVGElement>,
) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M19 9H6.65856C5.65277 9 5.14987 9 5.02472 8.69134C4.89957 8.38268 5.25517 8.01942 5.96637 7.29289L8.21091 5"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M5 15H17.3414C18.3472 15 18.8501 15 18.9753 15.3087C19.1004 15.6173 18.7448 15.9806 18.0336 16.7071L15.7891 19"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

const BankIcon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M2 8.56907C2 7.37289 2.48238 6.63982 3.48063 6.08428L7.58987 3.79744C9.7431 2.59915 10.8197 2 12 2C13.1803 2 14.2569 2.59915 16.4101 3.79744L20.5194 6.08428C21.5176 6.63982 22 7.3729 22 8.56907C22 8.89343 22 9.05561 21.9646 9.18894C21.7785 9.88945 21.1437 10 20.5307 10H3.46928C2.85627 10 2.22152 9.88944 2.03542 9.18894C2 9.05561 2 8.89343 2 8.56907Z"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
		<path
			d="M11.9959 7H12.0049"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M4 10V18.5M8 10V18.5"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
		<path
			d="M16 10V18.5M20 10V18.5"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
		<path
			d="M19 18.5H5C3.34315 18.5 2 19.8431 2 21.5C2 21.7761 2.22386 22 2.5 22H21.5C21.7761 22 22 21.7761 22 21.5C22 19.8431 20.6569 18.5 19 18.5Z"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
	</svg>
);