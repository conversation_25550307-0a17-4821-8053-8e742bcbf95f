import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import CustomSelect from "@/components/custom/input/select-input";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { useGetCoinWithdrawalMethod } from "@/hooks/api/crypto-banking";
import { WalletSelector } from "../../wallet-selector";

export type SendMode = "wallet" | "username";

export interface SelectOption {
	id?: string;
	label: string | React.ReactNode;
	value: string | number;
}

export interface CryptoTransaction {
	amount: string;
	fee: string;
	address: string;
	network: string;
	coin: string | ICryptoWallet;
	source: string;
	date: string;
}

export interface SendCryptoFormStepProps {
	sendMode: SendMode;
	initialScannedValue: string | null;
	onSubmit: (transaction: CryptoTransaction) => void;
	onScanQR: () => void;
	onNeedsPinCreation: (transaction: CryptoTransaction) => void;
}

const SendCryptoFormStep: React.FC<SendCryptoFormStepProps> = ({
	sendMode,
	initialScannedValue,
	onSubmit,
	onScanQR,
	onNeedsPinCreation,
}) => {
	const [selectedCoin, setSelectedCoin] = useState<ICryptoWallet | null>(
		null,
	);
	const [selectedNetwork, setSelectedNetwork] = useState<SelectOption | null>(
		null,
	);
	const [recipient, setRecipient] = useState<string>(
		initialScannedValue || "",
	);
	const [amount, setAmount] = useState<string>("");
	const [fee, setFee] = useState<string>("0.00");
	const [amountAfterFee, setAmountAfterFee] = useState<string>("");
	const [isLoading, setIsLoading] = useState<boolean>(false);

	const [networkOptions, setNetworkOptions] = useState<SelectOption[]>([]);
	const isWalletMode = sendMode === "wallet";

	const { data: userWallets } = useCryptoUserWallets();
	const { data: withdrawalMethods = [] } = useGetCoinWithdrawalMethod(
		selectedCoin?.currency || "",
	);

	useEffect(() => {
		if (initialScannedValue) {
			setRecipient(initialScannedValue);
		}
	}, [initialScannedValue]);

	useEffect(() => {
		setSelectedNetwork(null);
	}, [selectedCoin]);

	useEffect(() => {
		if (!selectedCoin) {
			setNetworkOptions([]);
			return;
		}

		const relevantMethods = withdrawalMethods.filter(
			(method) => method.currency === selectedCoin.currency,
		);

		const options = relevantMethods.map((method, index) => ({
			id: `network-${index}`,
			label: method.name,
			value: method.network || `network-${index}`,
		}));

		setNetworkOptions(options);

		if (selectedCoin.currency) {
			setFee(`0.00 ${selectedCoin.currency}`);
		}
	}, [withdrawalMethods, selectedCoin]);

	useEffect(() => {
		if (!amount) {
			setAmountAfterFee("");
			return;
		}

		const amountNum = parseFloat(amount) || 0;
		const feeValue = parseFloat(fee.split(" ")[0]) || 0;

		if (amountNum > feeValue) {
			setAmountAfterFee((amountNum - feeValue).toFixed(8));
		} else {
			setAmountAfterFee("0.00000000");
		}
	}, [amount, fee]);

	const handleNetworkChange = (option: SelectOption) => {
		setSelectedNetwork(option);

		if (selectedCoin && withdrawalMethods) {
			const method = withdrawalMethods.find(
				(m) => m.network === option.value || m.name === option.label,
			);

			if (method) {
				const networkFee = method.fee || "0.00";
				setFee(`${networkFee} ${selectedCoin.currency}`);
			}
		}
	};

	const handleSubmit = async () => {
		const hasRequiredFields = isWalletMode
			? selectedCoin && selectedNetwork && recipient && amount
			: recipient && amount;

		if (!hasRequiredFields) {
			return;
		}

		setIsLoading(true);

		try {
			const transaction: CryptoTransaction = {
				amount,
				fee,
				address: recipient,
				network:
					isWalletMode && selectedNetwork
						? typeof selectedNetwork.label === "string"
							? selectedNetwork.label
							: String(selectedNetwork.value)
						: "Internal",
				coin: selectedCoin?.currency || "BTC",
				source: "Crypto Wallet",
				date: new Date().toLocaleString(),
			};

			const hasPin = true;

			if (hasPin) {
				onSubmit(transaction);
			} else {
				onNeedsPinCreation(transaction);
			}
		} finally {
			setIsLoading(false);
		}
	};

	const handleAddNewWallet = () => {};

	const canSelectNetwork = !!selectedCoin;
	const canEnterRecipient =
		!isWalletMode || (isWalletMode && !!selectedCoin && !!selectedNetwork);
	const canEnterAmount = canEnterRecipient && !!recipient;

	return (
		<div className="flex flex-col gap-4 p-4">
			{/* Coin Selection */}
			<div className="mb-4">
				<label className="block text-sm font-medium text-gray-700 mb-1 ml-2">
					Select Coin
				</label>
				<WalletSelector
					wallets={userWallets || []}
					selectedWallet={selectedCoin}
					onSelect={setSelectedCoin}
					onAddNew={handleAddNewWallet}
				/>
			</div>

			{/* Network Selection */}
			<div className="mb-4">
				<label className="block text-sm font-medium text-gray-700 mb-1 ml-2">
					Select Network
				</label>
				<CustomSelect
					options={networkOptions}
					placeholder={
						selectedCoin ? "Select Network" : "Select a coin first"
					}
					value={selectedNetwork}
					onChange={handleNetworkChange}
					className={!canSelectNetwork ? "opacity-75" : ""}
				/>
				{selectedNetwork && (
					<p className="mt-1 text-xs text-gray-600 ml-2">
						Selected network: {selectedNetwork.label}
					</p>
				)}
			</div>

			{/* Recipient Address */}
			<div className="mb-4">
				<FloatingLabelInput
					label={
						isWalletMode ? "Paste wallet address" : "Enter Username"
					}
					name={isWalletMode ? "wallet-address" : "username"}
					value={recipient}
					onChange={(e) => setRecipient(e.target.value)}
					disabled={!canEnterRecipient}
					className={!canEnterRecipient ? "bg-gray-50" : ""}
				/>
				{!canEnterRecipient && (
					<p className="text-xs text-gray-500 ml-2 mt-1">
						{!selectedCoin
							? "Select a coin first"
							: "Select a network first"}
					</p>
				)}
			</div>

			{/* Amount Input */}
			<div className="mb-4">
				<FloatingLabelInput
					label="Enter Amount"
					name="amount"
					value={amount}
					onChange={(e) => setAmount(e.target.value)}
					disabled={!canEnterAmount}
					className={!canEnterAmount ? "bg-gray-50" : ""}
				/>
				{amount && (
					<p className="text-sm text-gray-500 ml-2 mt-1">
						Fee: {fee}
					</p>
				)}
				{!canEnterAmount && (
					<p className="text-xs text-gray-500 ml-2 mt-1">
						{isWalletMode
							? !selectedCoin
								? "Select a coin first"
								: !selectedNetwork
								? "Select a network first"
								: "Enter recipient address first"
							: "Enter username first"}
					</p>
				)}
			</div>

			{/* Amount Summary */}
			{amount && (
				<div className="bg-blue-50 rounded-full py-4 text-center text-sm text-gray-700 mb-4">
					Amount to be sent after fee is deducted: {amountAfterFee}{" "}
					{selectedCoin?.currency || ""}
				</div>
			)}

			<div className="text-center my-2 text-sm">Or</div>

			{/* Action Buttons */}
			<div className="flex flex-col sm:flex-row gap-4 mt-auto">
				<Button
					onClick={onScanQR}
					variant="outline"
					className="flex-1 rounded-full border-primary text-primary hover:bg-primary/5 hover:text-primary/80 h-14"
				>
					Scan QR code
				</Button>
				<Button
					onClick={handleSubmit}
					className="flex-1 rounded-full bg-primary hover:bg-primary/90 h-14"
					disabled={
						isWalletMode
							? !selectedCoin ||
							  !selectedNetwork ||
							  !recipient ||
							  !amount ||
							  isLoading
							: !recipient || !amount || isLoading
					}
				>
					{isLoading ? "Processing..." : "Send Crypto"}
				</Button>
			</div>
		</div>
	);
};

export default SendCryptoFormStep;
