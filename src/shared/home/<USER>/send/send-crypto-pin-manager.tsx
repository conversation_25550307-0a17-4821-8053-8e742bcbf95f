import { <PERSON>n<PERSON><PERSON><PERSON> } from "@/components/transaction/transaction-pin-manager";
import { ResponseStatus } from "@/config/enums";
import { useMakeCryptoWithdrawal } from "@/hooks/api/crypto-banking";
import useSendCryptoStore from "@/store/send-crypto-store";
import { ICryptoWithdrawalRequest } from "@/types/crypto-banking";
import { notify } from "@/utils/notify";
import { useState } from "react";



export function SendCryptoPinManager({
	onComplete,
}: {
	onComplete: () => void;
}) {
	const { transaction } = useSendCryptoStore();

	const [isProcessingSend, setIsProcessingSend] = useState(false);

	const { mutateAsync: requestCryptoWithdrawMutation } =
		useMakeCryptoWithdrawal();

	const handleComplete = async () => {
		if (
			!transaction ||
			!transaction.from ||
			!transaction.method ||
			!transaction.amount
		) {
			notify("Transaction details are incomplete", ResponseStatus.ERROR);
			return;
		}
		

		let withdrawPayload: ICryptoWithdrawalRequest;

		if (transaction.type && transaction.type == "internal") {
			withdrawPayload = {
				to_address: transaction.to_address,
				amount: parseFloat(transaction.amount),
				currency: transaction.from.currency,
				network: transaction.from.currency,
				withdrawal_method: transaction.method.name,
				paynetwork: transaction.from.currency,
			};
		} else {
			withdrawPayload = {
				to_address: transaction.to_address,
				amount: parseFloat(transaction.amount),
				currency: transaction.from.currency,
				network: transaction.network,
				withdrawal_method: transaction.method.name,
				paynetwork: transaction.network,
			};
		}

		setIsProcessingSend(true);
		try {
			await requestCryptoWithdrawMutation(withdrawPayload, {
				onSuccess: () => {
					notify(
						"Withdrawal created successfully",
						ResponseStatus.SUCCESS,
					);
					setIsProcessingSend(false);
					onComplete();
				},
				onError: (error) => {
					console.error("Withdrawal request failed:", error);
					notify("Withdrawal request failed", ResponseStatus.ERROR);
					setIsProcessingSend(false);
				},
			});

			
			
		} catch (error) {
			console.error("Swap failed:", error);
			notify("Something went wrong", ResponseStatus.ERROR);
			setIsProcessingSend(false);
		}
	};

	return (
		<PinManager
			onComplete={handleComplete}
			isProcessing={isProcessingSend}
			
			
			
		/>
	);
}
