import React from "react";
import { Button } from "@/components/ui/button";
import { TransactionSummaryCard } from "@/components/transaction-summary-card";
import { ICryptoWallet } from "@/types/crypto-wallets";

export interface CryptoTransaction {
	amount: string;
	fee: string;
	address: string;
	network: string;
	coin: string | ICryptoWallet;
	source: string;
	date: string;
}

export interface ConfirmTransactionStepProps {
	transaction: CryptoTransaction;
	onConfirm: () => void;
}

const ConfirmTransactionStep: React.FC<ConfirmTransactionStepProps> = ({
	transaction,
	onConfirm,
}) => {
	
	const coinStr =
		typeof transaction.coin === "string"
			? transaction.coin
			: transaction.coin.currency;

	return (
		<div className="flex flex-col gap-4 p-4">
			<TransactionSummaryCard
				amount={transaction.amount}
				currency={coinStr}
				prefix="-"
				highlightAmount={true}
				details={[
					{ label: "Network", value: transaction.network },
					{ label: "Address", value: transaction.address },
					{
						label: "Amount",
						value: `${transaction.amount} ${coinStr}`,
					},
					{ label: "Fee", value: transaction.fee },
					{ label: "Source", value: transaction.source },
					{ label: "Date", value: transaction.date },
				]}
			/>
			<Button onClick={onConfirm} className="w-full py-7 rounded-full">
				Send Crypto
			</Button>
		</div>
	);
};

export default ConfirmTransactionStep;
