import React from "react";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { CryptoTransaction } from "./send-crypto-form";

	const firstname = localStorage.getItem("firstName")
	const lastname= localStorage.getItem("lastName")

export interface TransactionSuccessStepProps {
	transaction: CryptoTransaction;
	onViewReceipt: () => void;
}

const TransactionSuccessStep: React.FC<TransactionSuccessStepProps> = ({
	transaction,
	onViewReceipt,
}) => {
	// Convert coin to string if it's an object
	const coinStr =
		typeof transaction.coin === "string"
			? transaction.coin
			: transaction.coin.currency;

	const message = (
		<p className="text-center mb-2">
			Hi {firstname} {lastname}, you have successfully sent <br />
			<span className="text-xl font-bold">
				{transaction.amount} {coinStr}
			</span>
		</p>
	);

	return (
		<div className="flex flex-col items-center justify-center gap-4 p-4 flex-grow">
			<TransactionSuccess
				title="Transaction Successful!"
				message={message}
				buttonText="Proceed"
				onNextAction={onViewReceipt}
			/>
		</div>
	);
};

export default TransactionSuccessStep;
