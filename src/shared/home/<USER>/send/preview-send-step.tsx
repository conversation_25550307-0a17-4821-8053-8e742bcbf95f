import { Button } from "@/components/ui/button";
import useSendCryptoStore from "@/store/send-crypto-store";
import { IClypUser } from "@/types/user";
import { formatAmount } from "@/utils/format-amount";


interface PreviewSendStepProps {
	onConfirm: () => void;
}

export function PreviewSendStep({ onConfirm }: PreviewSendStepProps) {
	const { transaction } = useSendCryptoStore();

	if (!transaction) {
		return (
			<div className="p-6 text-center">Transaction data not found</div>
		);
	}

	const {
		from,
		method,
		to_address,
		amount,
		feeInfo,
		network,
		description,
		userData,
	} = transaction;

	
	const isInternalTransfer = method?.name.includes("INTERNAL TRANSFER");
	const user = userData as IClypUser;

	return (
		<div className="flex flex-col h-full p-6">
			<div className=" rounded-2xl p-6">
				<h2 className="text-xl font-bold mb-2">
					{isInternalTransfer
						? `Send ${from?.currency} to ${user?.first_name ?? "user"}`
						: `Confirm ${from?.currency} Withdrawal`}
				</h2>
				<p className="text-gray-400 mb-6">
					{isInternalTransfer
						? "You are about to perform an Internal transfer.\nThis action cannot be reversed after confirmation."
						: "You are about to make a withdrawal. This action cannot be reversed after confirmation."}
				</p>

				{/* <div className="space-y-4">

					<div className="flex justify-between">
						<span className="text-gray-400">Date & Time</span>
						<span className="font-medium">{format(new Date(), 'dd-MM-yyyy | HH:mm:ss')}</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Status</span>
						<span className="text-green-500 font-medium">
							Completed
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">From</span>
						<span className="font-medium">
							{from.currency} Wallet
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">To</span>
						<span className="font-medium truncate max-w-[200px]">
							{to_address}
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Network</span>
						<span className="font-medium">{network}</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Amount</span>
						<span className="font-medium">
							{formatAmount(Number(amount), from.currency)}
						</span>
					</div>

					<div className="flex justify-between">
						<span className="text-gray-400">Fee</span>
						<span className="font-medium">
							{formatAmount(feeInfo?.fee, from.currency)}
						</span>
					</div>

					{description && (
						<div className="flex justify-between">
							<span className="text-gray-400">Note</span>
							<span className="font-medium">{description}</span>
						</div>
					)}
				</div>

				<div className="border-t border-gray-700 mt-4 pt-4">
					<div className="flex justify-between font-bold">
						<span>Total</span>
						<span>
							{formatAmount(Number(amount), from.currency)}
						</span>
					</div>
				</div> */}

				{/* From section */}
				{/* <div className="bg-gray-900 rounded-xl p-4 flex items-center justify-between mb-4">
					<div className="flex items-center">
						{from.icon ? (
							<img
								src={from.icon}
								alt={from.currency}
								className="w-10 h-10 mr-3"
							/>
						) : (
							<div className="w-10 h-10 bg-gray-700 rounded-full mr-3"></div>
						)}
						<div>
							<p className="text-sm text-gray-400">From</p>
							<p className="font-medium">{from.currency}</p>
						</div>
					</div>
					<div className="text-right font-bold">{amount}</div>
				</div> */}
				<div className="p-4 border rounded-2xl mb-4 space-y-4">
					<div className="flex justify-between">
						<span className="text-gray-400">From</span>
						<span className="text-gray-500 font-medium">
							{from?.coin_name} {from?.currency}
						</span>
					</div>
					<div className="flex justify-between">
						<span className="text-gray-400">Total amount</span>
						<span className="text-gray-500 font-medium">
							{formatAmount(amount)}
						</span>
					</div>
					<div className="flex justify-between">
						<span className="text-gray-400">To</span>
						<span className="text-gray-500 font-medium">
							{isInternalTransfer
								? `${user?.first_name ?? ""} ${user?.last_name ?? ""}`
								: to_address}
						</span>
					</div>
					<div className="flex justify-between">
						<span className="text-gray-400">
							{isInternalTransfer ? "Type:" : "Network:"}
						</span>
						<span className="text-gray-500 font-medium">
							{isInternalTransfer ? "Internal Transfer" : network}
						</span>
					</div>

                    <div className="flex justify-between">
						<span className="text-gray-400">From</span>
						<span className="text-gray-500 font-medium">
							{from?.coin_name} {from?.currency}
						</span>
					</div>

					{/* <div className="flex justify-between items-center">
						<div className="flex items-center gap-4">
							{from?.image && (
								<img
									src={from.image}
									alt={from.currency}
									className="w-10 h-10 rounded-full"
								/>
							)}
							<div>
								<p className="font-bold">From</p>
								<p className="text-primary">
									{from?.coin_name} {from?.currency}
								</p>
							</div>
						</div>
						<p className="text-lg font-semibold">
							{formatAmount(amount)}
						</p>
					</div> */}
				</div>

				{/* To section */}
				{/* <div className="bg-gray-900 rounded-xl p-4 mb-4">
					<div className="flex justify-between mb-2">
						<p className="text-gray-400">To:</p>
						<p className="text-gray-400">Network:</p>
					</div>
					<div className="flex justify-between">
						<p className="font-medium break-all">{to_address}</p>
						<p className="font-medium ml-2">{network}</p>
					</div>
				</div> */}
				{/* To Section */}
				{/* <div className="p-4 border rounded-2xl mb-4">
					<div className="flex justify-between items-center">
						<div className="w-1/2">
							<p className="text-gray-500">To:</p>
							<p className="text-lg mt-1 truncate">
								{isInternalTransfer
									? `${user?.first_name} ${user?.last_name}`
									: to_address}
							</p>
						</div>
						<div className="w-1/2 text-right">
							<p className="text-gray-500">
								{isInternalTransfer ? "Type:" : "Network:"}
							</p>
							<p className="text-lg mt-1">
								{isInternalTransfer
									? "Internal Transfer"
									: network}
							</p>
						</div>
					</div>
				</div> */}

				{/* <div className="p-4 border rounded-2xl mb-8">
					<div className="flex justify-between items-center">
						<div>
							<p className="text-gray-500">
								{isInternalTransfer
									? `${user?.first_name} will receive`
									: "You will receive"}
							</p>
							<p className="text-lg mt-1">
								{feeInfo?.amount?.toFixed(4)}
							</p>
						</div>
						<div className="text-right">
							<p className="text-gray-500">Transaction Fee</p>
							<p className="text-lg mt-1">
								{feeInfo?.fee?.toFixed(4)}
							</p>
						</div>
					</div>
				</div> */}

				{description && (
					<div className="p-4 border rounded-2xl mb-8">
						<p className="text-gray-500">Note</p>
						<p className="mt-1">{description}</p>
					</div>
				)}

				{/* Details section */}
				<div className="bg-gray-100 rounded-xl p-4">
					<div className="flex justify-between mb-2">
						<p className="text-gray-400">You will receive</p>
						<p className="font-medium">
							{formatAmount(feeInfo?.amount, from.currency, {
								overrideMinDecimalPlaces: 4,
							})}
						</p>
					</div>
					<div className="flex justify-between">
						<p className="text-gray-400">Transaction Fee</p>
						<p className="font-medium">
							{formatAmount(feeInfo?.fee, from.currency, {
								overrideMinDecimalPlaces: 4,
							})}
						</p>
					</div>
				</div>
			</div>

			<div className="mt-auto">
				<Button
					className="w-full bg-primary hover:bg-primary/85 text-white p-6 rounded-full mt-4"
					onClick={onConfirm}
				>
					Confirm
				</Button>
			</div>
		</div>
	);
}
