
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CryptoTransaction, SendMode } from "@/types/crypto";
import { WalletSelector } from "../../wallet-selector";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import CustomSelect from "@/components/custom/input/select-input";
import { TransactionSummaryCard } from "@/components/transaction-summary-card";
import OTPInput from "@/components/custom/input/otp-input";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { useGetCoinWithdrawalMethod } from "@/hooks/api/crypto-banking";


export interface InitialOptionsStepProps {
	onSendViaWallet: () => void;
	onSendViaUsername: () => void;
}

export const InitialOptionsStep: React.FC<InitialOptionsStepProps> = ({
	onSendViaWallet,
	onSendViaUsername,
}) => {
	return (
		<div className="flex flex-col gap-4 p-4">
			<Button
				onClick={onSendViaWallet}
				className="w-full py-7 rounded-full"
			>
				Send via Wallet
			</Button>

			<Button
				variant="outline"
				onClick={onSendViaUsername}
				className="w-full py-7 rounded-full"
			>
				Send via Clypay Username
			</Button>
		</div>
	);
};


export interface SendCryptoFormStepProps {
	sendMode: SendMode;
	initialScannedValue: string | null;
	onSubmit: (transaction: CryptoTransaction) => void;
	onScanQR: () => void;
	onNeedsPinCreation: (transaction: CryptoTransaction) => void;
}

export const SendCryptoFormStep: React.FC<SendCryptoFormStepProps> = ({
	sendMode,
	initialScannedValue,
	onSubmit,
	onScanQR,
	onNeedsPinCreation,
}) => {
	
	const [selectedCoin, setSelectedCoin] = useState<ICryptoWallet | null>(null);
	const [recipient, setRecipient] = useState<string>("");
	const [amount, setAmount] = useState<string>("");
	const [fee] = useState<string>("0.00000011 BTC");
	const [amountAfterFee, setAmountAfterFee] = useState<string>("");
	const [isLoading] = useState<boolean>(false);

	
	const [selectedNetwork, setSelectedNetwork] = useState<any>(null);

	const { data: userWallets } =
		useCryptoUserWallets();

	const { data = [] } = useGetCoinWithdrawalMethod(
		selectedCoin?.currency || "",
	);

	console.log("Withdrawal Method" ,data);
	

	
	useEffect(() => {
		if (initialScannedValue) {
			setRecipient(initialScannedValue);
		}
	}, [initialScannedValue]);

	
	useEffect(() => {
		setSelectedNetwork("");
	}, [selectedCoin]);

	
	useEffect(() => {
		if (amount) {
			const amountNum = parseFloat(amount) || 0;
			const feeNum = 0.00000011;
			if (amountNum > feeNum) {
				setAmountAfterFee((amountNum - feeNum).toFixed(8));
			} else {
				setAmountAfterFee("0.00000000");
			}
		} else {
			setAmountAfterFee("");
		}
	}, [amount]);

	const isWalletMode = sendMode === "wallet";

	const handleSubmit = async () => {
		// Validate required fields
		// const hasRequiredFields = isWalletMode
		// 	? selectedCoin && selectedNetwork && recipient && amount
		// 	: recipient && amount;
		const hasRequiredFields = true;

		if (hasRequiredFields) {
			// if (hasRequiredFields) {
			// Create transaction object
			const transaction: CryptoTransaction = {
				amount,
				fee,
				address: isWalletMode
					? "0sbfcjh879839239ihks\nhnc89w90euydhios909" 
					: recipient,
				network: isWalletMode ? selectedNetwork : "Internal",
				coin: selectedCoin?.currency || "BTC",
				source: "Crypto Wallet",
				date: "12-05-2024 | 09:33:00",
			};

			
			const hasPin = true;
			if (hasPin) {
				
				onSubmit(transaction);
			} else {
				
				onNeedsPinCreation(transaction);
			}
		}
	};

	const networkOptions = [
		{ id: "1", label: "CLYP USDT WITHDRAWAL TRC20 (TRX)", value: "trc20" },
		{ id: "2", label: "CLYP USDT WITHDRAWAL BEP20 (BSC)", value: "bep20" },
		{ id: "3", label: "SEND TO CLYPPAY USER", value: "clypuser" },
		{ id: "4", label: "CLYP USDT WITHDRAWAL ERC20 (ETH)", value: "erc20" },
	];

	const handleAddNewWallet = () => {
		
		console.log("Opening add wallet modal");
	};

	

	return (
		<div className="flex flex-col gap-4 p-4">
			<WalletSelector
				wallets={userWallets || []}
				selectedWallet={selectedCoin}
				onSelect={setSelectedCoin}
				onAddNew={handleAddNewWallet}
				// isLoading={isLoadingWallets}
			/>
			<CustomSelect
				options={networkOptions}
				placeholder="Select Network"
				value={selectedNetwork}
				onChange={setSelectedNetwork}
			/>
			{selectedNetwork && (
				<p className="mt-2 text-sm text-gray-600">
					Selected value: {selectedNetwork?.value}
				</p>
			)}

			<FloatingLabelInput
				label={isWalletMode ? "Paste wallet address" : "Enter Username"}
				name={isWalletMode ? "wallet-address" : "username"}
				value={recipient}
				onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
					setRecipient(e.target.value)
				}
				// helperText={
				// 	isWalletMode
				// 		? "Must be a valid wallet address"
				// 		: "Must be a valid clypay username"
				// }
			/>
			<div>
				<FloatingLabelInput
					label="Enter Amount"
					name="amount"
					value={amount}
					onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
						setAmount(e.target.value)
					}
				/>
				<p className="text-sm text-gray-500 px-4">
					Fee is displayed here: {fee}
				</p>
			</div>
			{amount && (
				<div className="bg-blue-50 rounded-full py-4 text-center text-sm text-gray-700">
					Amount to be sent after fee is deducted: {amountAfterFee}{" "}
					{selectedCoin?.currency || ""}
				</div>
			)}
			<div className="text-center my-2 text-sm">Or</div>
			<div className="flex flex-col sm:flex-row gap-4 mt-auto">
				<Button
					onClick={onScanQR}
					variant="outline"
					className="flex-1 rounded-full border-primary text-primary hover:bg-primary/5 hover:text-primary/80 h-14"
				>
					Scan QR code
				</Button>
				<Button
					onClick={handleSubmit}
					className="flex-1 rounded-full bg-primary hover:bg-primary/90 h-14"
				>
					{isLoading ? "Processing..." : "Send Crypto"}
				</Button>
			</div>
		</div>
	);
};

// Step 3: QR Scanner
export interface QRScannerStepProps {
	onScan: (address: string) => void;
	onCancel: () => void;
}

export const QRScannerStep: React.FC<QRScannerStepProps> = ({
	onScan,
	onCancel,
}) => {
	return (
		<div className="flex flex-col h-full bg-gray-900 justify-center items-center">
			<div className="w-64 h-64 border-2 border-amber-500 relative mx-auto my-8">
				<div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-amber-500"></div>
				<div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-amber-500"></div>
				<div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-amber-500"></div>
				<div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-amber-500"></div>

				{/* Placeholder QR code */}
				<div className="p-2">
					<img
						src="/api/placeholder/200/200"
						alt="QR Code"
						className="w-full h-full"
						onClick={() => onScan("0sbfcjh879839239ihks")}
					/>
				</div>
			</div>

			<Button
				variant="secondary"
				onClick={onCancel}
				className="mt-8 w-64"
			>
				Cancel
			</Button>
		</div>
	);
};

// Step 4: Confirm Transaction
export interface ConfirmTransactionStepProps {
	transaction: CryptoTransaction;
	onConfirm: () => void;
}

export const ConfirmTransactionStep: React.FC<ConfirmTransactionStepProps> = ({
	transaction,
	onConfirm,
}) => {
	console.log(transaction);
	
	return (
		<div className="flex flex-col gap-4 p-4">
			<TransactionSummaryCard
				amount="0.00005739"
				currency="BTC"
				prefix="-"
				highlightAmount={true}
				details={[
					{ label: "Network", value: "BSC" },
					{
						label: "Address",
						value: "0sbfcjh879839239iihks hnc89w90euydhios909",
					},
					{ label: "Amount", value: "0.00005750 BTC" },
					{ label: "Fee", value: "0.00000011 BTC" },
					{ label: "Source", value: "Crypto Wallet" },
					{ label: "Date", value: "12-05-2024 | 09:33:00" },
				]}
			/>
			<Button onClick={onConfirm} className="w-full py-7 rounded-full">
				Send Crypto
			</Button>
		</div>
	);
};

// Step 5: Create Transaction PIN
export interface CreatePinStepProps {
	onPinCreated: () => void;
}

export const CreatePinStep: React.FC<CreatePinStepProps> = ({
	onPinCreated,
}) => {
	// State to store the PIN
	const [pin, setPin] = useState<string>("");
	const [loading, setLoading] = useState<boolean>(false);

	// Handle PIN change from OTP component
	const handlePinChange = (value: string) => {
		setPin(value);
	};

	// Handle when PIN is complete (all digits entered)
	const handlePinComplete = (value: string) => {
		console.log("PIN entry complete:", value);
	};

	// Handle create PIN button click
	const handleCreatePin = async () => {
		if (pin.length !== 4) return;

		setLoading(true);
		try {
			// In a real app, you would make an API call here to save the PIN
			await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate API call

			// Call the parent component's callback with the created PIN
			onPinCreated();
		} catch (error) {
			console.error("Failed to create PIN:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="flex flex-col gap-4 p-4">
			<div className="text-center mb-12">
				<p>Create transaction pin to complete transaction.</p>
			</div>

			<div className="mb-8">
				<OTPInput
					length={4}
					value={pin}
					onChange={handlePinChange}
					onComplete={handlePinComplete}
					autoFocus
				/>
			</div>

			<div className="mt-8">
				<Button
					onClick={handleCreatePin}
					className="w-full py-7 rounded-full"
					disabled={pin.length !== 4 || loading}
				>
					{loading ? "Creating..." : "Create Pin"}
				</Button>
			</div>
		</div>
	);
};

// Step 6: PIN Creation Success
export interface PinSuccessStepProps {
	onProceed: () => void;
}

	const firstname = localStorage.getItem("firstName")
	const lastname= localStorage.getItem("lastName")

export const PinSuccessStep: React.FC<PinSuccessStepProps> = ({
	onProceed,
}) => {
	return (
		<div className="flex flex-col items-center justify-center gap-4 p-4 flex-grow">
			<TransactionSuccess
				title="Transaction Pin Created Successfully!"
				// message={`Hi Mary, you have successfully purchased 0.921 worth of BTC`}
				message={`Hi ${firstname} ${lastname}, you have successfully created your transaction pin.`}
				buttonText="Proceed"
				onNextAction={onProceed}
			/>
		</div>
	);
};

// Step 7: Enter PIN
export interface EnterPinStepProps {
	onPinVerified: () => void;
}

export const EnterPinStep: React.FC<EnterPinStepProps> = ({
	onPinVerified,
}) => {
	// State to store the PIN
	const [pin, setPin] = useState<string>("");
	const [loading, setLoading] = useState<boolean>(false);

	// Handle PIN change from OTP component
	const handlePinChange = (value: string) => {
		setPin(value);
	};

	// Handle when PIN is complete (all digits entered)
	const handlePinComplete = (value: string) => {
		console.log("PIN entry complete:", value);
	};

	// Handle create PIN button click
	const handleCreatePin = async () => {
		if (pin.length !== 4) return;

		setLoading(true);
		try {
			// In a real app, you would make an API call here to save the PIN
			await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate API call

			// Call the parent component's callback with the created PIN
			onPinVerified();
		} catch (error) {
			console.error("Failed to create PIN:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="flex flex-col gap-4 p-4">
			<div className="text-center mb-12">
				<p>Input transaction pin to complete transaction.</p>
			</div>

			<div className="mb-8">
				<OTPInput
					length={4}
					value={pin}
					onChange={handlePinChange}
					onComplete={handlePinComplete}
					autoFocus
				/>
			</div>

			<div className="mt-8">
				<Button
					onClick={handleCreatePin}
					className="w-full py-7 rounded-full"
					disabled={pin.length !== 4 || loading}
				>
					{loading ? "Processing..." : "Send Crypto"}
				</Button>
			</div>
		</div>
	);
};

// Step 8: Transaction Success
export interface TransactionSuccessStepProps {
	transaction: CryptoTransaction;
	onViewReceipt: () => void;
}


export const TransactionSuccessStep: React.FC<TransactionSuccessStepProps> = ({
	transaction,
	
	onViewReceipt,
}) => {
	const message = (
		
		<p className="text-center mb-2">
			Hi {firstname} {lastname}, you have successfully sent <br />
			<span className="text-xl font-bold">
				{transaction.amount} {transaction.coin}
			</span>
		</p>
	);
	return (
		<div className="flex flex-col items-center justify-center gap-4 p-4 flex-grow">
			<TransactionSuccess
				title="Transaction Successful!"
				// message={`Hi Mary, you have successfully purchased 0.921 worth of BTC`}
				message={message}
				buttonText="Proceed"
				onNextAction={onViewReceipt}
			/>
		</div>
	);
};


// Step 9: Transaction Receipt
export interface TransactionReceiptStepProps {
	transaction: CryptoTransaction;
	onShare: () => void;
}

export const TransactionReceiptStep: React.FC<TransactionReceiptStepProps> = ({
	transaction,
	onShare,
}) => {
	return (
		<div className="flex flex-col gap-4 p-4">
			<TransactionSummaryCard
				amount={transaction.amount}
				currency={transaction.coin}
				prefix="-"
				status="successful"
				details={[
					{ label: "Network", value: transaction.network },
					{ label: "Address", value: transaction.address },
					{
						label: "Amount",
						value: `${transaction.amount} ${transaction.coin}`,
					},
					{ label: "Fee", value: transaction.fee },
					{ label: "Source", value: transaction.source },
					{ label: "Date", value: transaction.date },
				]}
			/>
			<Button onClick={onShare} className="w-full py-7 rounded-full">
				Share Receipt
			</Button>
		</div>
	);
};
