import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import useSendCryptoStore, { SendCryptoStep } from "@/store/send-crypto-store";
import { Icon } from "@/components/icons";
import { SendCryptoStep as SendCryptoForm } from "./send-crypto-step";
import { PreviewSendStep } from "./preview-send-step";
import { ReceiptStep } from "./receipt-step";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { SendCryptoPinManager } from "./send-crypto-pin-manager";

export function SendCryptoFlow() {
	const { closeDrawer } = useDrawer();

	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);

	const { currentStep, transaction, setCurrentStep } = useSendCryptoStore();

	const handleClose = () => {
		closeDrawer();

		setTimeout(() => {
			useSendCryptoStore.getState().reset();
			setDirection("forward");
		}, 300);
	};

	const handleStepChange = (
		step: SendCryptoStep,
		dir: "forward" | "backward" = "forward",
	) => {
		setDirection(dir);
		setCurrentStep(step);
	};

	const handleBack = () => {
		switch (currentStep) {
			case "preview":
				handleStepChange("select", "backward");
				break;
			case "pin":
				handleStepChange("preview", "backward");
				break;
			case "success":
				break;
			case "receipt":
				handleStepChange("success", "backward");
				break;
			default:
				break;
		}
	};

	const handleTransactionComplete = () => {
		handleStepChange("success");
	};

	const handleViewReceipt = () => {
		handleStepChange("receipt");
	};

	const handleShareReceipt = () => {
		alert("Sharing receipt functionality would go here");
	};

	const getStepTitle = () => {
		switch (currentStep) {
			case "select":
				return "Send Crypto";
			case "preview":
				return "Preview Transaction";
			case "pin":
				return null;
			case "success":
				return null;
			case "receipt":
				return "Transaction Receipt";
			default:
				return "";
		}
	};

	const showBackButton = ["preview", "pin", "receipt"].includes(currentStep);

	const horizontalVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	const verticalVariants = {
		enter: {
			y: 50,
			opacity: 0,
		},
		center: {
			y: 0,
			opacity: 1,
		},
		exit: {
			y: -50,
			opacity: 0,
		},
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex flex-col mt-10 relative px-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-amber-500 rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-amber-500" />
				</Button>
				{(getStepTitle() || showBackButton) && (
					<div className="flex items-center justify-center relative">
						{showBackButton && (
							<span
								onClick={handleBack}
								className="absolute left-0 bg-amber-500/20 text-amber-500 rounded-full p-2 cursor-pointer"
							>
								<Icon name="arrow-left" className="size-6" />
							</span>
						)}
						{getStepTitle() && (
							<h2 className="text-2xl font-semibold w-full text-center">
								{getStepTitle()}
							</h2>
						)}
					</div>
				)}
			</div>

			<div className="p-0 h-full overflow-hidden relative flex-grow">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{currentStep === "select" && (
						<motion.div
							key="select"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<SendCryptoForm
								onProceed={() => handleStepChange("preview")}
							/>
						</motion.div>
					)}

					{currentStep === "preview" && (
						<motion.div
							key="preview"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<PreviewSendStep
								onConfirm={() => handleStepChange("pin")}
							/>
						</motion.div>
					)}

					{currentStep === "pin" && (
						<motion.div
							key="pin"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<SendCryptoPinManager
								onComplete={handleTransactionComplete}
							/>
						</motion.div>
					)}

					{currentStep === "success" && (
						<motion.div
							key="success"
							variants={verticalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								y: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<TransactionSuccess
								title="Transaction Successful!"
								message={
									<p>
										{transaction ? (
											<>
												You have successfully sent{" "}
												<br />
												{transaction.amount}{" "}
												{transaction.from.currency} to{" "}
												<br />
												{transaction.to_address.slice(
													0,
													8,
												)}
												...
											</>
										) : (
											"Your transaction was successful!"
										)}
									</p>
								}
								buttonText="View Receipt"
								onNextAction={handleViewReceipt}
							/>
						</motion.div>
					)}

					{currentStep === "receipt" && (
						<motion.div
							key="receipt"
							variants={verticalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								y: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<ReceiptStep onShareReceipt={handleShareReceipt} />
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}
