import {But<PERSON>} from "@/components/custom/button";
import Checkbox from "@/assets/images/Checkbox.png";
import useUserStore from "@/store/user-store";

interface SavingsSuccessprops{
    goalName: string;
    goalId: string;
    onViewGoal: (goalId: string) => void;
}



export const SavingsSuccess = ({goalName, goalId, onViewGoal}:SavingsSuccessprops)=>{

    const { user } = useUserStore();
    const userName = user?.first_name || user?.username || 'User';
    
    return(
        <div className="flex justify-center mt-20 items-center ">
              <div className="flex flex-col items-center justify-center p-8 gap-8 text-center">
            <div className="size-[200px]">
               <img src={Checkbox} alt="success" className="w-[200px] h-[200px]" />
            </div>
            <h2 className="text-3xl md:tex-5xl font-semibold mb-2">
                Savings Goal Created Successfully!
            </h2>
            <p className="text-gray-600 mb-8">
                {`hello ${userName}, your "${goalName}" Savings Goal has been created successfully`}
            </p>
            <Button 
                onClick={() => onViewGoal(goalId)}
                className="bg-primary text-white rounded-full px-8 py-3"
            >
                View Savings Goal
            </Button>
        </div>
        </div>
      
    )
}