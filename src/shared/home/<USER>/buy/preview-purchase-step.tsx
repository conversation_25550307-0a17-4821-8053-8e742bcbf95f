import { format } from "date-fns";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { formatAmount } from "@/utils/format-amount";
import useBuyCryptoTransactionStore from "@/store/buy-crypto-store";

interface PreviewBuyCryptoProps {
	onBuyCrypto: () => void;
}

export function PreviewBuyCrypto({ onBuyCrypto }: PreviewBuyCryptoProps) {
	const { transaction } = useBuyCryptoTransactionStore();

	if (!transaction) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<p className="text-gray-500">No transaction data available</p>
			</div>
		);
	}

	return (
		<div className="flex flex-col p-6 space-y-6">
			{/* Amount Display */}
			<motion.div
				className="text-center my-4"
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ delay: 0.1 }}
			>
				<h2 className="text-xl font-bold">
					You are about to buy {transaction.from.currency}{" "}
					{transaction.amount.toString()} worth of{" "}
					{transaction.to.currency}
				</h2>
			</motion.div>

			{/* Transaction Details */}
			<motion.div
				className="space-y-4"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
			>
				<div className="flex justify-between">
					<span className="text-gray-500">Coin</span>
					<span className="font-medium">
						{transaction.to.currency}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Rate</span>
					<span className="font-medium">
						{`1 ${transaction.from.currency} ≈ ${formatAmount(
							transaction.price_data.rate,
							transaction.to.currency,
							{ overrideMinDecimalPlaces: 4 },
						)} ${transaction.to.currency}`}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Amount</span>
					<span className="font-medium">
						{`${formatAmount(
							Number(transaction.amount) *
								transaction.price_data.rate,
							transaction.to.currency,
							{ overrideMinDecimalPlaces: 4 },
						)} ${transaction.to.currency}`}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Fee</span>
					<span className="font-medium">
						{`${formatAmount(0.********, transaction.to.currency, {
							overrideMinDecimalPlaces: 8,
						})} ${transaction.to.currency}`}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Payment</span>
					<span className="font-medium">Fiat Account</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Date</span>
					<span className="font-medium">
						{format(new Date(), "dd-MM-yyyy | HH:mm:ss")}
					</span>
				</div>
			</motion.div>

			{/* Account Selection */}
			{/* <motion.div
				className="relative mt-6"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
			>
				<div className="flex items-center justify-between p-4 rounded-full border">
					<span className="font-medium">Choose Account</span>
					<ChevronDown className="h-5 w-5 text-gray-400" />
				</div>
			</motion.div> */}

			{/* Amount to be charged */}
			<motion.div
				className="bg-gray-100 p-4 rounded-full text-center"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.4 }}
			>
				<span className="text-gray-600">
					You will receive{" "}
					{formatAmount(
						Number(transaction.amount) *
							transaction.price_data.rate,
						transaction.to.currency,
						{ overrideMinDecimalPlaces: 4 },
					)}{" "}
					{transaction.to.currency}
				</span>
			</motion.div>

			{/* Buy Button */}
			<motion.div
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.5 }}
			>
				<Button
					className="bg-[#d99c3a] hover:bg-[#c08a2e] text-white rounded-full py-6 mt-6 w-full"
					onClick={onBuyCrypto}
				>
					Buy Crypto
				</Button>
			</motion.div>
		</div>
	);
}
