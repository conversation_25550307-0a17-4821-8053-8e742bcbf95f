import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import useBuyCryptoTransactionStore, {
	PurchaseStep,
} from "@/store/buy-crypto-store";
import { Icon } from "@/components/icons";
import { BuyCrypto } from "./buy-crypto-step";
import { ReceiptStep } from "./receipt-step";
import { formatAmount } from "@/utils/format-amount";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { PreviewBuyCrypto } from "./preview-purchase-step";
import { SwapPinManager } from "./swap-pin-manager";

export function BuyCryptoFlow() {
	const { closeDrawer } = useDrawer();

	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);

	const { currentStep, transaction, setCurrentStep } =
		useBuyCryptoTransactionStore();

	const handleClose = () => {
		closeDrawer();

		setTimeout(() => {
			useBuyCryptoTransactionStore.getState().reset();
			setDirection("forward");
		}, 300);
	};

	const handleStepChange = (
		step: PurchaseStep,
		dir: "forward" | "backward" = "forward",
	) => {
		setDirection(dir);
		setCurrentStep(step);
	};

	const handleBack = () => {
		switch (currentStep) {
			case "preview":
				handleStepChange("select", "backward");
				break;
			case "pin":
				handleStepChange("preview", "backward");
				break;
			case "success":
				break;
			case "receipt":
				handleStepChange("success", "backward");
				break;
			default:
				break;
		}
	};

	const handleViewReceipt = () => {
		handleStepChange("receipt");
	};

	const handleShareReceipt = () => {
		alert("Sharing receipt functionality would go here");
	};

	const getStepTitle = () => {
		switch (currentStep) {
			case "select":
				return "Buy Crypto";
			case "preview":
				return "Preview Order";
			case "pin":
				return null;
			case "success":
				return null;
			case "receipt":
				return "Transaction Receipt";
			default:
				return "";
		}
	};

	const showBackButton = ["preview", "pin", "receipt"].includes(currentStep);

	const horizontalVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	const verticalVariants = {
		enter: {
			y: 50,
			opacity: 0,
		},
		center: {
			y: 0,
			opacity: 1,
		},
		exit: {
			y: -50,
			opacity: 0,
		},
	};

		const firstname = localStorage.getItem("firstName")
	const lastname= localStorage.getItem("lastName")

	return (
		<div className="flex flex-col h-full">
			<div className="flex flex-col mt-10 relative px-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>
				{(getStepTitle() || showBackButton) && (
					<div className="flex items-center justify-center relative">
						{showBackButton && (
							<span
								onClick={handleBack}
								className="absolute left-0 bg-primary-light text-primary rounded-full p-2 cursor-pointer"
							>
								<Icon name="arrow-left" className="size-6" />
							</span>
						)}
						{getStepTitle() && (
							<h2 className="text-2xl font-semibold w-full text-center">
								{getStepTitle()}
							</h2>
						)}
					</div>
				)}
			</div>

			<div className="p-0 h-full overflow-hidden relative flex-grow">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{currentStep === "select" && (
						<motion.div
							key="select"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<BuyCrypto
								onProceed={() => handleStepChange("preview")}
							/>
						</motion.div>
					)}

					{currentStep === "preview" && (
						<motion.div
							key="review"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							{/* <ConfirmPurchaseStep */}
							<PreviewBuyCrypto
								onBuyCrypto={() => handleStepChange("pin")}
							/>
						</motion.div>
					)}

					{currentStep === "pin" && (
						<motion.div
							key="pin"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							{/* <PinManager onComplete={handleBuyCrypto} /> */}
							<SwapPinManager
								onComplete={() => handleStepChange("success")}
							/>
						</motion.div>
					)}

					{currentStep === "success" && (
						<motion.div
							key="success"
							variants={verticalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								y: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<TransactionSuccess
								title="Transaction Successful!"
								message={
									<p>
										{transaction ? (
											<>
												Hi {firstname} {lastname}, you have successfully
												purchased <br />
												{formatAmount(
													Number(transaction.amount) *
														transaction.price_data
															.rate,
													transaction.to.currency,
													{
														overrideMinDecimalPlaces: 4,
													},
												)}{" "}
												worth of{" "}
												{transaction.to.currency}
											</>
										) : (
											"Your transaction was successful!"
										)}
									</p>
								}
								buttonText="View Receipt"
								onNextAction={handleViewReceipt}
							/>
						</motion.div>
					)}

					{currentStep === "receipt" && (
						<motion.div
							key="receipt"
							variants={verticalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								y: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<ReceiptStep onShareReceipt={handleShareReceipt} />
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}
