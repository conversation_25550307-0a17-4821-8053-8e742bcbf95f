import { useState } from "react";
import AccountBalanceCard from "../account-balance-card";
import QuickActionCard from "../quick-action-card";
import PortfolioSection from "../portfolio";
import RecentTransactions from "../recent-transactions";
import { useFiatTransactions } from "@/hooks/api/userFiatTransactions";

const AccountView = () => {
	const [activeView, setActiveView] = useState<"crypto" | "fiat">("crypto");

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
			<div className="space-y-6">
				<AccountBalanceCard
					activeView={activeView}
					onViewChange={setActiveView}
				/>
				<QuickActionCard />
			</div>
			{activeView === "crypto" ? (
				<PortfolioSection userId={""} />
			) : (
				// <RecentTransactions />
<RecentTransactions 
    useTransactionsHook={useFiatTransactions} 
    title="Fiat Transactions"
    useFullSkeleton={true}
    seeAllLink="/fiat-transactions"
    seeAllText="See all"
/>
			)}
		</div>
	);
};

export default AccountView;
