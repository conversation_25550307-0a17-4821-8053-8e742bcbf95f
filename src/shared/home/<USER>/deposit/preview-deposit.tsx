import React, { useState } from "react";
import { But<PERSON> } from "@/components/custom/button";
import { motion } from "framer-motion";
import { format } from "date-fns";
import useDepositFiatStore from "@/store/deposit-fiat-store";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import { useCreateDeposit } from "@/hooks/api/fiat-banking";
import { Loader } from "lucide-react";

interface PreviewDepositProps {
	onConfirm?: (depositType: "paymentLink" | "temporaryAccount") => void;
}

const PreviewDeposit: React.FC<PreviewDepositProps> = ({ onConfirm }) => {
	const [isSubmitting, setIsSubmitting] = useState(false);

	const {
		depositFiatPayload,
		selectedDepositMethod,
		selectedCoin,
		setCurrentStep,
		setTransactionReference,
		setTemporaryAccountDetails,
		setPaymentLink,
	} = useDepositFiatStore();

	const { mutate: createDeposit, isPending } = useCreateDeposit();

	const handleConfirm = () => {
		if (!depositFiatPayload || !selectedDepositMethod?.raw) {
			notify("Missing deposit information", ResponseStatus.ERROR);
			return;
		}

		setIsSubmitting(true);

		createDeposit(
			{
				account_id: depositFiatPayload.payload.coin.account_id,
				transaction_method: selectedDepositMethod.raw.name,
				amount: depositFiatPayload.payload.amount,
			},
			{
				onSuccess: (response) => {
					
					setTransactionReference(response.data.id);

					if (response.data.use_link) {
						
						setPaymentLink(response.data.link);
						setCurrentStep("processingDeposit");

						if (onConfirm) {
							onConfirm("paymentLink");
						}
					} else {
						
						if (typeof response.data.payment_detail !== "string") {
							setTemporaryAccountDetails({
								accountNumber:
									response.data.payment_detail.account_number,
								accountName:
									response.data.payment_detail.account_name,
								bankName:
									response.data.payment_detail.bank_name,
								amount: depositFiatPayload.payload.amount,
								expiresAt:
									response.data.payment_detail.expires_at,
							});
							setCurrentStep("temporaryAccountDetails");

							if (onConfirm) {
								onConfirm("temporaryAccount");
							}
						} else {
							notify(
								"Invalid payment details received",
								ResponseStatus.ERROR,
							);
						}
					}

					setIsSubmitting(false);
				},
				onError: (error) => {
					const errorMessage = error?.message || "Deposit failed";
					notify(errorMessage, ResponseStatus.ERROR);
					setIsSubmitting(false);
				},
			},
		);
	};

	
	if (!depositFiatPayload || !selectedCoin || !selectedDepositMethod?.raw) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<p className="text-gray-500">
					No deposit data available. Please go back and try again.
				</p>
			</div>
		);
	}

	const { payload } = depositFiatPayload;
	const currencySymbol = parseCurrencySymbol(selectedCoin.currency || "");
	const isPaymentLink = payload.type === "paymentLink";
	const depositAmount = parseFloat(payload.amount);
	const feeAmount = payload.feeInfo.fee;
	const finalAmount = payload.feeInfo.amount;

	return (
		<div className="flex flex-col p-6 space-y-6">
			{/* Amount Display */}
			<motion.div
				className="text-center my-4"
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ delay: 0.1 }}
			>
				<h2 className="text-xl font-bold">
					You are about to deposit {currencySymbol}
					{depositAmount.toFixed(2)} via{" "}
					{isPaymentLink ? "Payment Link" : "Temporary Account"}
				</h2>
			</motion.div>

			{/* Transaction Details */}
			<motion.div
				className="space-y-4"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
			>
				<div className="flex justify-between">
					<span className="text-gray-500">Deposit Method</span>
					<span className="font-medium">
						{isPaymentLink ? "Payment Link" : "Temporary Account"}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Currency</span>
					<span className="font-medium">
						{selectedCoin.currency} ({selectedCoin.currency_name})
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Amount</span>
					<span className="font-medium">
						{currencySymbol}
						{depositAmount.toFixed(2)}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Transaction Fee</span>
					<span className="font-medium">
						{currencySymbol}
						{feeAmount.toFixed(2)}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">You'll Receive</span>
					<span className="font-medium text-green-500">
						{currencySymbol}
						{finalAmount.toFixed(2)}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Date</span>
					<span className="font-medium">
						{format(new Date(), "dd-MM-yyyy | HH:mm:ss")}
					</span>
				</div>
			</motion.div>

			{/* Informational text */}
			<motion.div
				className="bg-slate-900/20 p-4 rounded-lg"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
			>
				<p className="text-sm text-gray-800">
					{isPaymentLink
						? "You will be redirected to a secure payment page to complete your deposit."
						: "A temporary account will be generated for you to make the deposit. Please ensure you deposit the exact amount specified."}
				</p>
			</motion.div>

			{/* Confirm Button */}
			<motion.div
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.5 }}
			>
				<Button
					className="bg-primary hover:bg-primary/90 text-white rounded-full py-6 mt-6 w-full flex items-center justify-center gap-2"
					onClick={handleConfirm}
					disabled={isSubmitting || isPending}
				>
					{isSubmitting || isPending ? (
						<>
							<Loader className="w-5 h-5 animate-spin" />
							Processing...
						</>
					) : isPaymentLink ? (
						"Proceed to Payment Page"
					) : (
						"Generate Account Details"
					)}
				</Button>
			</motion.div>
		</div>
	);
};

export default PreviewDeposit;
