import React, { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Button } from "@/components/custom/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import useDepositFiatStore from "@/store/deposit-fiat-store";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";

interface FormData {
	amount: string;
}

interface PaymentLinkDepositProps {
	onContinue?: () => void;
}

const PaymentLinkDeposit: React.FC<PaymentLinkDepositProps> = ({
	onContinue,
}) => {
	const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

	const {
		selectedDepositMethod,
		selectedCoin,
		setCurrentStep,
		setDepositFiatPayload,
	} = useDepositFiatStore();

	const {
		control,
		handleSubmit,
		watch,
		formState: { errors },
	} = useForm<FormData>({
		defaultValues: {
			amount: "",
		},
	});

	const watchAmount = watch("amount");

	const limits =
		selectedDepositMethod?.raw?.restrictions?.TransactionLimits ||
		selectedDepositMethod?.raw?.restrictions?.Transactionlimits;

	const minAmount = limits?.[0] || 0;
	

	const validateAmount = (value: string) => {
		if (!value) return "Amount is required";
		if (isNaN(parseFloat(value)) || parseFloat(value) <= 0)
			return "Please enter a valid amount";
		if (minAmount && parseFloat(value) < Number(minAmount))
			return `Amount must be greater than ${minAmount}`;
		return true;
	};

	const onSubmit = (data: FormData) => {
		setIsSubmitting(true);

		
		if (!selectedCoin || !selectedDepositMethod?.raw) {
			console.error("Missing required data: currency or deposit method");
			setIsSubmitting(false);
			return;
		}

		
		const feeInfo = calcResolvedAmount(data.amount);

		
		setDepositFiatPayload({
			payload: {
				type: "paymentLink",
				coin: selectedCoin,
				method: selectedDepositMethod.raw,
				amount: data.amount,
				feeInfo: feeInfo,
			},
		});

		
		setCurrentStep("previewDeposit", true);

		
		if (onContinue) {
			onContinue();
		}

		setIsSubmitting(false);
	};

	function calcResolvedAmount(amount: string): {
		fee: number;
		amount: number;
	} {
		const parsedAmount = parseFloat(amount || "0");
		const feeAmount = parseFloat(selectedDepositMethod?.raw?.fee || "0");
		const feeType = selectedDepositMethod?.raw?.fee_type;

		if (isNaN(parsedAmount) || isNaN(feeAmount)) {
			return {
				fee: 0,
				amount: 0,
			};
		}

		if (feeType === "PERCENTAGE") {
			const fee = (parsedAmount * feeAmount) / 100;
			return {
				fee,
				amount: parsedAmount - fee,
			};
		} else {
			
			const fee = feeAmount;
			return {
				fee,
				amount: parsedAmount - fee,
			};
		}
	}

	const feeInfo = calcResolvedAmount(watchAmount);
	const currencySymbol = parseCurrencySymbol(selectedCoin?.currency || "");

	return (
		<div className="p-4 max-w-xl mx-auto">
			<div className="mb-6">
				<p className="text-gray-400">
					Enter the amount you wish to deposit. A secure payment link
					will be generated for you.
				</p>
			</div>

			<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
				{/* Amount Input */}
				<div className="relative">
					<Controller
						name="amount"
						control={control}
						rules={{
							required: "Amount is required",
							validate: validateAmount,
						}}
						render={({ field, fieldState }) => (
							<div>
								<FloatingLabelInput
									label="Enter amount to deposit"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => {
										
										const value = e.target.value.replace(
											/[^0-9.]/g,
											"",
										);

										
										const parts = value.split(".");
										if (parts.length > 2) {
											return;
										}

										field.onChange(value);
									}}
									className="border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400"
								/>
								{fieldState.error && (
									<p className="text-red-500 text-xs mt-1">
										{fieldState.error.message}
									</p>
								)}
							</div>
						)}
					/>
					<div className="text-xs text-gray-400 mt-1 px-4">
						Fee: {currencySymbol}
						{feeInfo.fee}
					</div>
				</div>

				{/* Summary */}
				{watchAmount && !errors.amount && (
					<div className="bg-gray-100 p-4 rounded-lg text-center">
						<p className="text-gray-800">
							A total of {currencySymbol}
							{parseFloat(watchAmount)} will be withdrawn from
							your deposit source.
						</p>
					</div>
				)}

				{/* Informational text */}
				<div className="text-center text-sm text-gray-400">
					<p>
						A secure payment link will be generated and opened for
						you to make a deposit into your account.
					</p>
				</div>

				{/* Submit Button */}
				<Button
					type="submit"
					className={`w-full py-4 rounded-full font-medium ${
						watchAmount && !errors.amount
							? "bg-primary text-white"
							: "bg-gray-700 text-gray-400 cursor-not-allowed"
					}`}
					disabled={!watchAmount || !!errors.amount || isSubmitting}
				>
					{isSubmitting ? "Processing..." : "Generate Payment Link"}
				</Button>
			</form>
		</div>
	);
};

export default PaymentLinkDeposit;
