import React, { useState } from "react";
import { Button } from "@/components/custom/button";
import { Clipboard, Check, Copy } from "lucide-react";
import useDepositFiatStore from "@/store/deposit-fiat-store";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";

interface TemporaryAccountDetailsProps {
	onDone?: () => void;
}

const TemporaryAccountDetails: React.FC<TemporaryAccountDetailsProps> = ({
	onDone,
}) => {
	const [copied, setCopied] = useState<string | null>(null);

	const { depositFiatPayload, temporaryAccountDetails, setCurrentStep } =
		useDepositFiatStore();

	const handleCopy = (text: string, field: string) => {
		navigator.clipboard.writeText(text);
		notify("Copied to clipboard", ResponseStatus.SUCCESS);
		setCopied(field);
		setTimeout(() => setCopied(null), 2000);
	};

	
	if (!temporaryAccountDetails || !depositFiatPayload) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<div className="bg-red-100 p-4 rounded-full mb-4">
					<svg
						className="w-12 h-12 text-red-500"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M6 18L18 6M6 6l12 12"
						/>
					</svg>
				</div>
				<h3 className="text-xl font-bold mb-2">
					Account details not found
				</h3>
				<p className="text-gray-500 text-center mb-6">
					We couldn't find your temporary account details. Please try
					again.
				</p>
				<Button
					onClick={() => setCurrentStep("select")}
					className="bg-primary hover:bg-primary/90 text-white rounded-full py-3 px-6 w-full"
				>
					Go Back
				</Button>
			</div>
		);
	}

	const { accountNumber, accountName, bankName, amount } =
		temporaryAccountDetails;
	const currencySymbol = parseCurrencySymbol(
		depositFiatPayload?.payload.coin?.currency || "",
	);

	return (
		<div className="flex flex-col p-6 space-y-6">
			<div className="text-center mb-4">
				<p className="text-gray-500 mt-2">
					Use the following details to transfer exactly{" "}
					{currencySymbol}
					{amount} to complete your deposit.
				</p>
			</div>

			<div className="bg-gray-100 rounded-lg p-6 space-y-4">
				<div>
					<p className="text-gray-500 text-sm">Bank Name</p>
					<div className="flex justify-between items-center mt-1">
						<p className="font-medium">{bankName}</p>
						<button
							onClick={() => handleCopy(bankName, "bank")}
							className="p-2 rounded-full hover:bg-gray-200"
						>
							{copied === "bank" ? (
								<Check className="w-5 h-5 text-green-500" />
							) : (
								<Copy className="w-5 h-5 text-primary" />
							)}
						</button>
					</div>
				</div>

				<div>
					<p className="text-gray-500 text-sm">Account Number</p>
					<div className="flex justify-between items-center mt-1">
						<p className="font-medium">{accountNumber}</p>
						<button
							onClick={() => handleCopy(accountNumber, "account")}
							className="p-2 rounded-full hover:bg-gray-200"
						>
							{copied === "account" ? (
								<Check className="w-5 h-5 text-green-700" />
							) : (
								<Copy className="w-5 h-5 text-primary" />
							)}
						</button>
					</div>
				</div>

				<div>
					<p className="text-gray-500 text-sm">Account Name</p>
					<div className="flex justify-between items-center mt-1">
						<p className="font-medium">{accountName}</p>
					</div>
				</div>

				<div>
					<p className="text-gray-500 text-sm">Amount to Send</p>
					<div className="flex justify-between items-center mt-1">
						<p className="font-medium text-primary">
							{currencySymbol}
							{amount}
						</p>
					</div>
				</div>
			</div>

			<div className="bg-slate-400/20 rounded-lg p-4">
				<p className="text-sm text-gray-800">
					<span className="font-bold">Important:</span> This account
					is valid for 30 minutes only. Please make sure to transfer
					the exact amount specified to avoid delays.
				</p>
			</div>

			<div>
				<Button
					onClick={() =>
						handleCopy(
							`Bank: ${bankName}\nAccount Number: ${accountNumber}\nAccount Name: ${accountName}\nAmount: ${currencySymbol}${amount}`,
							"all",
						)
					}
					className="hover:bg-primary/-40 text-white rounded-full py-3 px-6 w-full flex items-center justify-center gap-2 mb-4"
				>
					{copied === "all" ? (
						<Check className="w-5 h-5" />
					) : (
						<Clipboard className="w-5 h-5" />
					)}
					Copy All Details
				</Button>

				<Button
					onClick={onDone}
					className="bg-primary hover:bg-primary/90 text-white rounded-full py-3 px-6 w-full"
				>
					I've Made the Transfer
				</Button>
			</div>
		</div>
	);
};

export default TemporaryAccountDetails;
