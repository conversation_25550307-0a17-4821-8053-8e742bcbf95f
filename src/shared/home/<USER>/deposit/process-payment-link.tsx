import { useRef, useState, useEffect } from "react";
import { Loader, ArrowLeft, ExternalLink } from "lucide-react";
import useDepositFiatStore from "@/store/deposit-fiat-store";
import { Button } from "@/components/ui/button";

const ProcessingDeposit = () => {
	const { paymentLink, setCurrentStep } = useDepositFiatStore();
	const iframeRef = useRef<HTMLIFrameElement>(null);
	const [iframeLoaded, setIframeLoaded] = useState(false);
	const [showFallback, setShowFallback] = useState(false);

	
	useEffect(() => {
		if (!paymentLink) {
			setCurrentStep("previewDeposit");
			return;
		}

		const timer = setTimeout(() => {
			if (!iframeLoaded) {
				setShowFallback(true);
			}
		}, 5000); 

		return () => clearTimeout(timer);
	}, [paymentLink, iframeLoaded, setCurrentStep]);

	const handleBack = () => {
		setCurrentStep("previewDeposit");
	};

	const openInNewTab = () => {
		window.open(paymentLink, "_blank", "noopener,noreferrer");
	};

	return (
		<div className="flex flex-col h-full bg-background">
			{/* Mobile header with back button */}
			<div className="p-4 border-b flex items-center md:hidden">
				<button
					onClick={handleBack}
					className="p-1 rounded-full hover:bg-gray-100"
				>
					<ArrowLeft className="h-5 w-5 text-foreground" />
				</button>
				<h3 className="ml-4 font-medium text-foreground">
					Complete Payment
				</h3>
			</div>

			{/* Loading state */}
			{!iframeLoaded && !showFallback && (
				<div className="flex flex-col items-center justify-center flex-1 p-6">
					<Loader className="animate-spin h-8 w-8 mb-4 text-primary" />
					<p className="text-foreground">
						Loading payment gateway...
					</p>
				</div>
			)}

			{/* Iframe container */}
			<div className="relative flex-1">
				{!showFallback && (
					<iframe
						ref={iframeRef}
						src={paymentLink}
						className={`absolute inset-0 w-full h-full border-none ${
							iframeLoaded ? "block" : "hidden"
						}`}
						sandbox="allow-scripts allow-forms allow-same-origin"
						onLoad={() => setIframeLoaded(true)}
						allow="payment *"
					/>
				)}

				{/* Mobile fallback */}
				{showFallback && (
					<div className="absolute inset-0 flex flex-col items-center justify-center p-6 bg-background/90 backdrop-blur-sm">
						<div className="max-w-md text-center">
							<h4 className="text-lg font-medium mb-2 text-foreground">
								Payment Gateway Not Loaded
							</h4>
							<p className="text-muted-foreground mb-6">
								We couldn't load the payment form directly.
								Please open it in a new window to complete your
								transaction.
							</p>
							<Button
								onClick={openInNewTab}
								className="gap-2"
								size="lg"
							>
								<ExternalLink className="h-4 w-4" />
								Open Payment Page
							</Button>
							<button
								onClick={() => {
									setShowFallback(false);
									setIframeLoaded(false);
								}}
								className="mt-3 text-sm text-primary underline"
							>
								Try loading again
							</button>
						</div>
					</div>
				)}
			</div>

			{/* Desktop help text */}
			<div className="hidden md:block p-4 border-t text-center text-sm text-muted-foreground">
				Secure payment processing by Paystack
			</div>
		</div>
	);
};

export default ProcessingDeposit;
