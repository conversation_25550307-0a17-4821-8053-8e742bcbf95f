"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { X } from "lucide-react";
import { Icon } from "@/components/icons";
import useDepositFiatStore from "@/store/deposit-fiat-store";
import SelectDepositMethod from "./select-deposit-method";
import PaymentLinkDeposit from "./payment-link-deposit";
import TemporaryAccountDeposit from "./temporary-account-deposit";
import PreviewDeposit from "./preview-deposit";
import TemporaryAccountDetails from "./temporary-account-details";
import ProcessingDeposit from "./process-payment-link";


export function DepositFiatFlow() {
	const { closeDrawer } = useDrawer();

	
	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);

	
	const { currentStep, previousStep, setCurrentStep } = useDepositFiatStore();

	
	const handleClose = () => {
		
		closeDrawer();

		
		setTimeout(() => {
			
			useDepositFiatStore.getState().reset();
			setDirection("forward");
		}, 300);
	};

	
	const handleStepChange = (
		step: typeof currentStep,
		dir: "forward" | "backward" = "forward",
	) => {
		setDirection(dir);
		setCurrentStep(step, dir === "forward");
	};

	
	const handleBack = () => {
		switch (currentStep) {
			case "paymentLink":
			case "temporaryAccount":
				handleStepChange("select", "backward");
				break;
			case "previewDeposit":
				
				if (
					previousStep === "paymentLink" ||
					previousStep === "temporaryAccount"
				) {
					handleStepChange(previousStep, "backward");
				} else {
					
					handleStepChange("select", "backward");
				}
				break;
			default:
				break;
		}
	};

	
	const getStepTitle = () => {
		switch (currentStep) {
			case "select":
				return "Deposit Fiat";
			case "paymentLink":
				return "Deposit via Payment Link";
			case "temporaryAccount":
				return "Deposit via Temporary Account";
			case "temporaryAccountDetails":
				return "Temporary Account Details";
			case "previewDeposit":
				return "Preview Deposit";
			case "processingDeposit":
				return null;
			case "success":
				return "Deposit Successful";
			default:
				return "";
		}
	};

	
	const showBackButton = [
		"paymentLink",
		"temporaryAccount",
		"previewDeposit",
	].includes(currentStep);

	
	const horizontalVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex flex-col mt-10 relative px-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>
				{(getStepTitle() || showBackButton) && (
					<div className="flex items-center justify-center relative">
						{showBackButton && (
							<span
								onClick={handleBack}
								className="absolute left-0 bg-primary-light text-primary rounded-full p-2 cursor-pointer"
							>
								<Icon name="arrow-left" className="size-6" />
							</span>
						)}
						{getStepTitle() && (
							<h2 className="text-2xl font-semibold w-full text-center">
								{getStepTitle()}
							</h2>
						)}
					</div>
				)}
			</div>

			<div className="p-0 h-full overflow-hidden relative flex-grow">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{currentStep === "select" && (
						<motion.div
							key="select"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<SelectDepositMethod
								onMethodSelect={(method) => {
									
									handleStepChange(method, "forward");
								}}
							/>
						</motion.div>
					)}

					{currentStep === "paymentLink" && (
						<motion.div
							key="paymentLink"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<PaymentLinkDeposit
								onContinue={() => {
									handleStepChange(
										"previewDeposit",
										"forward",
									);
								}}
							/>
						</motion.div>
					)}

					{currentStep === "temporaryAccount" && (
						<motion.div
							key="temporaryAccount"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<TemporaryAccountDeposit
								onContinue={() => {
									handleStepChange(
										"previewDeposit",
										"forward",
									);
								}}
							/>
						</motion.div>
					)}

					{currentStep === "previewDeposit" && (
						<motion.div
							key="previewDeposit"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<PreviewDeposit
								onConfirm={(depositType) => {
									if (depositType === "paymentLink") {
										handleStepChange(
											"processingDeposit",
											"forward",
										);
									} else {
										handleStepChange("temporaryAccountDetails", "forward");
									}
								}}
							/>
						</motion.div>
					)}

					{currentStep === "processingDeposit" && (
						<motion.div
							key="processingDeposit"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<ProcessingDeposit
								
								
								
							/>
						</motion.div>
					)}

					{currentStep === "temporaryAccountDetails" && (
						<motion.div
							key="temporaryAccountDetails"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<TemporaryAccountDetails onDone={handleClose} /> 
						</motion.div>
					)}

					{currentStep === "success" && (
						<motion.div
							key="success"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							{/* <DepositSuccess onDone={handleClose} /> */}
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}
