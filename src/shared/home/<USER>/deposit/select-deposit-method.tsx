// import React, { useState } from "react";
// import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
// import { useGetFiatWalletDepositMethod } from "@/hooks/api/fiat-banking";
// import useDepositFiatStore from "@/store/deposit-fiat-store";
// import { DepositStep } from "@/store/deposit-fiat-store";
// import { IOption } from "@/types/general";
// import { IFiatWallet } from "@/types/fiat-wallets";
// import { IFiatDepositMethod } from "@/types/fiat-banking";
// import { formatAmount } from "@/utils/format-amount";
// import { Plus } from "lucide-react";
// import EnhancedSelect from "@/components/enhanced-select";
// import { Button } from "@/components/ui/button";
// import { BankIcon } from "../withdraw/select-withdrawal-method";
// import { Link02Icon } from "../../crypto/receive/select-step";

// interface SelectDepositMethodProps {
// 	onMethodSelect: (method: DepositStep) => void;
// }

// const SelectDepositMethod: React.FC<SelectDepositMethodProps> = ({
// 	onMethodSelect,
// }) => {
// 	const { setSelectedCoin, setSelectedDepositMethod } = useDepositFiatStore();
// 	const [selectedFiat, setSelectedFiat] = useState<IFiatWallet | null>(null);
// 	const [selectedDepositMethodOption, setSelectedDepositMethodOption] =
// 		useState<IOption<IFiatDepositMethod> | null>(null);

// 	const { data: depositMethods = [], isLoading: isLoadingDepositMethods } =
// 		useGetFiatWalletDepositMethod(selectedFiat?.account_id ?? "");
// 		console.log(selectedFiat?.account_id );

// 	const { data: fiatWallets = [], isLoading: isLoadingFiatWallets } =
// 		useFiatUserWallets();

// 	const fiatOptions: IOption<IFiatWallet>[] = fiatWallets.map((wallet) => ({
// 		id: `fiat-${wallet.currency}`,
// 		value: wallet.currency,
// 		label: wallet.currency,
// 		icon: wallet.image,
// 		raw: wallet,
// 	}));

// 	const depositMethodOptions: IOption<IFiatDepositMethod>[] =
// 		depositMethods?.map(generateOption).filter((x) => !x.hidden) || [];

// 	const selectedFiatOption =
// 		fiatOptions.find((option) => option.value === selectedFiat?.currency) ||
// 		null;

// 	function generateOption(
// 		item: IFiatDepositMethod,
// 	): IOption<IFiatDepositMethod> {
// 		if (
// 			item.name.includes("DYNAMIC") &&
// 			item.name.includes("DEPOSIT") &&
// 			item.name.includes("LINK")
// 		) {
// 			return {
// 				label: "Deposit via Payment Link",
// 				icon: <Link02Icon />,
// 				value: "DYNAMIC_LINK",
// 				raw: item,
// 			};
// 		} else if (item.name.includes("DYNAMIC") && item?.is_general) {
// 			return {
// 				label: "Deposit via Temporary Account Details",
// 				icon: <GridIcon />,
// 				value: "DYNAMIC_GENERAL",
// 				raw: item,
// 			};
// 		} else if (item.name.includes("DYNAMIC") && !item?.is_general) {
// 			return {
// 				hidden: true,
// 				label: "Deposit via Unique Account Details",
// 				icon: <GridIcon />,
// 				value: "DYNAMIC_UNIQUE",
// 				raw: item,
// 			};
// 		} else if (item.name.includes("STATIC")) {
// 			return {
// 				hidden: true,
// 				label: "Static Deposit",
// 				icon: <BankIcon />,
// 				value: "STATIC",
// 				raw: item,
// 			};
// 		} else if (item.name.includes("SWAP")) {
// 			return {
// 				hidden: true,
// 				label: "Swap Deposit",
// 				value: "STATIC",
// 				raw: item,
// 			};
// 		}
// 		return {
// 			label: "-",
// 			value: "-",
// 			hidden: true,
// 		};
// 	}

// 	const handleFiatChange = (option: IOption<IFiatWallet> | null) => {
// 		if (option && option.raw) {
// 			setSelectedFiat(option.raw);
// 			setSelectedCoin(option.raw);
// 			setSelectedDepositMethodOption(null);
// 		} else {
// 			setSelectedFiat(null);
// 			setSelectedDepositMethodOption(null);
// 		}
// 	};

// 	const handleDepositMethodChange = (
// 		option: IOption<IFiatDepositMethod> | null,
// 	) => {
// 		if (option && option.raw) {
// 			setSelectedDepositMethod(option);
// 			setSelectedDepositMethodOption(option);

// 			if (option.value === "DYNAMIC_LINK") {
// 				onMethodSelect("paymentLink");
// 			} else if (option.value === "DYNAMIC_GENERAL") {
// 				onMethodSelect("temporaryAccount");
// 			}
// 		}
// 	};

// 	const handleAddNew = () => {};

// 	const walletHeader = (
// 		<div className="py-2 flex justify-between items-center">
// 			<h3 className="text-2xl font-bold font-body">Wallets</h3>
// 			<Button
// 				onClick={handleAddNew}
// 				className="text-sm flex items-center rounded-full"
// 			>
// 				Add New
// 				<Plus className="h-3 w-3 ml-2" />
// 			</Button>
// 		</div>
// 	);

// 	return (
// 		<div className="p-6 space-y-6">
// 			<div className="mb-6">
// 				<p className="text-gray-400 mb-2">
// 					Choose a currency and deposit method to proceed.
// 				</p>
// 			</div>

// 			{/* Currency Selection */}
// 			<div className="relative">
// 				<EnhancedSelect<IFiatWallet>
// 					header={walletHeader}
// 					options={fiatOptions}
// 					value={selectedFiatOption}
// 					onChange={handleFiatChange}
// 					placeholder="Choose Fiat Account "
// 					isLoading={isLoadingFiatWallets}
// 					className="w-full mb-6"
// 					displayClassName="p-4"
// 					renderSelected={(option) => (
// 						<div className="flex items-center gap-2">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<span>{option.value}</span>
// 						</div>
// 					)}
// 					renderOption={(option) => (
// 						<div className="flex items-center gap-2 w-full">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<div className="flex flex-col">
// 								<span>{option.label}</span>
// 								<span className="text-xs text-gray-500 group-hover:text-white">
// 									Balance:{" "}
// 									{formatAmount(
// 										option.raw?.available_balance,
// 										option.value,
// 									)}
// 								</span>
// 							</div>
// 						</div>
// 					)}
// 				/>
// 			</div>

// 			{/* Deposit Methods */}
// 			<div className="relative">
// 				<EnhancedSelect<IFiatDepositMethod>
// 					options={depositMethodOptions}
// 					value={selectedDepositMethodOption}
// 					onChange={handleDepositMethodChange}
// 					placeholder="Choose Deposit Method"
// 					isLoading={isLoadingDepositMethods}
// 					className="w-full mb-6"
// 					displayClassName="p-4"
// 					isSearchable={false}
// 					disabled={!selectedFiat}
// 					renderSelected={(option) => (
// 						<div className="flex items-center gap-2">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<span>{option.label}</span>
// 						</div>
// 					)}
// 					renderOption={(option) => (
// 						<div className="flex items-center gap-2 w-full">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<div className="flex flex-col">
// 								<span>{option.label}</span>
// 							</div>
// 						</div>
// 					)}
// 				/>
// 			</div>
// 		</div>
// 	);
// };

// export default SelectDepositMethod;

// const GridIcon = (props: React.SVGProps<SVGSVGElement>) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M7 3V21"
// 			stroke="#000000"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		></path>
// 		<path
// 			d="M17 3V21"
// 			stroke="#000000"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		></path>
// 		<path
// 			d="M21 7L3 7"
// 			stroke="#000000"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		></path>
// 		<path
// 			d="M21 17L3 17"
// 			stroke="#000000"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		></path>
// 	</svg>
// );


import React, { useState, useEffect } from "react";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useGetFiatWalletDepositMethod } from "@/hooks/api/fiat-banking";
import useDepositFiatStore from "@/store/deposit-fiat-store";
import useFiatSelectionStore from "@/store/fiat-selection-store";
import { DepositStep } from "@/store/deposit-fiat-store";
import { IOption } from "@/types/general";
import { IFiatWallet } from "@/types/fiat-wallets";
import { IFiatDepositMethod } from "@/types/fiat-banking";
import { formatAmount } from "@/utils/format-amount";
import { Plus } from "lucide-react";
import EnhancedSelect from "@/components/enhanced-select";
import { Button } from "@/components/ui/button";
import { BankIcon } from "../withdraw/select-withdrawal-method";
import { Link02Icon } from "../../crypto/receive/select-step";

interface SelectDepositMethodProps {
	onMethodSelect: (method: DepositStep) => void;
}

const SelectDepositMethod: React.FC<SelectDepositMethodProps> = ({
	onMethodSelect,
}) => {
	const { setSelectedCoin, setSelectedDepositMethod } = useDepositFiatStore();
	const { selectedFiat } = useFiatSelectionStore(); // Get selected fiat from store
	const [localSelectedFiat, setLocalSelectedFiat] = useState<IFiatWallet | null>(null);
	const [selectedDepositMethodOption, setSelectedDepositMethodOption] =
		useState<IOption<IFiatDepositMethod> | null>(null);

	const { data: depositMethods = [], isLoading: isLoadingDepositMethods } =
		useGetFiatWalletDepositMethod(localSelectedFiat?.account_id ?? "");

	const { data: fiatWallets = [], isLoading: isLoadingFiatWallets } =
		useFiatUserWallets();

	// Auto-select the fiat from the store when component mounts or selectedFiat changes
	useEffect(() => {
		if (selectedFiat) {
			setLocalSelectedFiat(selectedFiat);
			setSelectedCoin(selectedFiat);
			// Reset deposit method selection when fiat changes
			setSelectedDepositMethodOption(null);
		}
	}, [selectedFiat, setSelectedCoin]);

	const fiatOptions: IOption<IFiatWallet>[] = fiatWallets.map((wallet) => ({
		id: `fiat-${wallet.currency}`,
		value: wallet.currency,
		label: wallet.currency,
		icon: wallet.image,
		raw: wallet,
	}));

	const depositMethodOptions: IOption<IFiatDepositMethod>[] =
		depositMethods?.map(generateOption).filter((x) => !x.hidden) || [];

	const selectedFiatOption =
		fiatOptions.find((option) => option.value === localSelectedFiat?.currency) ||
		null;

	function generateOption(
		item: IFiatDepositMethod,
	): IOption<IFiatDepositMethod> {
		if (
			item.name.includes("DYNAMIC") &&
			item.name.includes("DEPOSIT") &&
			item.name.includes("LINK")
		) {
			return {
				label: "Deposit via Payment Link",
				icon: <Link02Icon />,
				value: "DYNAMIC_LINK",
				raw: item,
			};
		} else if (item.name.includes("DYNAMIC") && item?.is_general) {
			return {
				label: "Deposit via Temporary Account Details",
				icon: <GridIcon />,
				value: "DYNAMIC_GENERAL",
				raw: item,
			};
		} else if (item.name.includes("DYNAMIC") && !item?.is_general) {
			return {
				hidden: true,
				label: "Deposit via Unique Account Details",
				icon: <GridIcon />,
				value: "DYNAMIC_UNIQUE",
				raw: item,
			};
		} else if (item.name.includes("STATIC")) {
			return {
				hidden: true,
				label: "Static Deposit",
				icon: <BankIcon />,
				value: "STATIC",
				raw: item,
			};
		} else if (item.name.includes("SWAP")) {
			return {
				hidden: true,
				label: "Swap Deposit",
				value: "STATIC",
				raw: item,
			};
		}
		return {
			label: "-",
			value: "-",
			hidden: true,
		};
	}

	const handleFiatChange = (option: IOption<IFiatWallet> | null) => {
		if (option && option.raw) {
			setLocalSelectedFiat(option.raw);
			setSelectedCoin(option.raw);
			setSelectedDepositMethodOption(null);
		} else {
			setLocalSelectedFiat(null);
			setSelectedDepositMethodOption(null);
		}
	};

	const handleDepositMethodChange = (
		option: IOption<IFiatDepositMethod> | null,
	) => {
		if (option && option.raw) {
			setSelectedDepositMethod(option);
			setSelectedDepositMethodOption(option);

			if (option.value === "DYNAMIC_LINK") {
				onMethodSelect("paymentLink");
			} else if (option.value === "DYNAMIC_GENERAL") {
				onMethodSelect("temporaryAccount");
			}
		}
	};

	const handleAddNew = () => {};

	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	return (
		<div className="p-6 space-y-6">
			<div className="mb-6">
				<p className="text-gray-400 mb-2">
					Choose a currency and deposit method to proceed.
				</p>
			</div>

			{/* Currency Selection */}
			<div className="relative">
				<EnhancedSelect<IFiatWallet>
					header={walletHeader}
					options={fiatOptions}
					value={selectedFiatOption}
					onChange={handleFiatChange}
					placeholder="Choose Fiat Account "
					isLoading={isLoadingFiatWallets}
					className="w-full mb-6"
					displayClassName="p-4"
					renderSelected={(option) => (
						<div className="flex items-center gap-2">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<span>{option.value}</span>
						</div>
					)}
					renderOption={(option) => (
						<div className="flex items-center gap-2 w-full">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<div className="flex flex-col">
								<span>{option.label}</span>
								<span className="text-xs text-gray-500 group-hover:text-white">
									Balance:{" "}
									{formatAmount(
										option.raw?.available_balance,
										option.value,
									)}
								</span>
							</div>
						</div>
					)}
				/>
			</div>

			{/* Deposit Methods */}
			<div className="relative">
				<EnhancedSelect<IFiatDepositMethod>
					options={depositMethodOptions}
					value={selectedDepositMethodOption}
					onChange={handleDepositMethodChange}
					placeholder="Choose Deposit Method"
					isLoading={isLoadingDepositMethods}
					className="w-full mb-6"
					displayClassName="p-4"
					isSearchable={false}
					disabled={!localSelectedFiat}
					renderSelected={(option) => (
						<div className="flex items-center gap-2">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<span>{option.label}</span>
						</div>
					)}
					renderOption={(option) => (
						<div className="flex items-center gap-2 w-full">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<div className="flex flex-col">
								<span>{option.label}</span>
							</div>
						</div>
					)}
				/>
			</div>
		</div>
	);
};

export default SelectDepositMethod;

const GridIcon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M7 3V21"
			stroke="#000000"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		></path>
		<path
			d="M17 3V21"
			stroke="#000000"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		></path>
		<path
			d="M21 7L3 7"
			stroke="#000000"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		></path>
		<path
			d="M21 17L3 17"
			stroke="#000000"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		></path>
	</svg>
);