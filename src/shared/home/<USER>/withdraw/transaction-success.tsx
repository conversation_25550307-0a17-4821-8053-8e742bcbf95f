import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";

const WithdrawalTransactionSuccess = () => {
	const { withdrawFiatPayload, selectedCoin, setCurrentStep } =
		useWithdrawalFiatStore();

	const onViewReceipt = () => {
		setCurrentStep("receipt");
	};

	if (!withdrawFiatPayload || !selectedCoin) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<p className="text-gray-500">
					Transaction information not available.
				</p>
			</div>
		);
	}

	const { payload } = withdrawFiatPayload;
	const isInternalTransfer = payload.type === "internal";
	const symbol = parseCurrencySymbol(selectedCoin.currency || "");
	const amount = payload.amount;
	const recipientName = isInternalTransfer
		? payload.identifier
		: payload.accountName;

	const message = (
		<p className="text-center text-gray-600">
			You have successfully{" "}
			{isInternalTransfer ? "transferred" : "withdrawn"} <br />
			{symbol}
			{amount}{" "}
			{isInternalTransfer
				? `to Clyp user (${recipientName})`
				: `to your bank account (${recipientName})`}
		</p>
	);

	const title = isInternalTransfer
		? "Transfer Successful!"
		: "Withdrawal Successful!";

	return (
		<div className="flex flex-col items-center justify-center gap-4 p-4 flex-grow">
			<TransactionSuccess
				title={title}
				message={message}
				buttonText="View Receipt"
				onNextAction={onViewReceipt}
			/>
		</div>
	);
};

export default WithdrawalTransactionSuccess;
