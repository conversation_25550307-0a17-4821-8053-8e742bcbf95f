import { useF<PERSON>, Controller } from "react-hook-form";
import { Button } from "@/components/custom/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import EnhancedSelect, { IOption } from "@/components/enhanced-select";
import { useWithdrawSavingsGoalStore } from "@/store/useWithdrawGoalStore";
import { SavingsDetails } from "@/types/fiat-savings";


export function WithdrawSavingsAmountForm({
  goal,
  fiatAccounts,
  cryptoAccounts,
  defaultFundingSource,
  defaultAccount,
}: {
  goal: SavingsDetails;
  fiatAccounts: IOption<string>[];
  cryptoAccounts: IOption<string>[];
  defaultFundingSource?: string;
  defaultAccount?: string;
}) {
  const { setStep, setAmount, setFundingSource, setSelectedAccount } = useWithdrawSavingsGoalStore();
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<{ fundingSourceType: string; account: string; amount: number }>({
    defaultValues: {
      fundingSourceType: defaultFundingSource || "",
      account: defaultAccount || "",
      amount: 0,
    },
  });

  const selectedFundingSource = watch("fundingSourceType");
  const accountOptions =
    selectedFundingSource === "fiat"
      ? fiatAccounts
      : selectedFundingSource === "crypto"
      ? cryptoAccounts
      : [];

  const onSubmit = (data: { fundingSourceType: string; account: string; amount: number }) => {
    setAmount(data.amount);
    setFundingSource(data.fundingSourceType as "fiat" | "crypto");
    setSelectedAccount(data.account);
    setStep("pin");
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6 p-6">
      <h2 className="text-xl font-semibold text-center mb-2">Withdraw from Savings Goal</h2>

      {/* Step 1: Funding Source Type */}
      <Controller
        name="fundingSourceType"
        control={control}
        rules={{ required: "Please select withdrawal type" }}
        render={({ field }) => (
          <div>
            <EnhancedSelect
              options={[
                { id: "fiat", label: "Fiat Wallet", value: "fiat" },
                { id: "crypto", label: "Crypto Wallet", value: "crypto" },
              ]}
              value={
                [
                  { id: "fiat", label: "Fiat Wallet", value: "fiat" },
                  { id: "crypto", label: "Crypto Wallet", value: "crypto" },
                ].find((opt) => opt.value === field.value) || null
              }
              onChange={(opt) => field.onChange(opt?.value)}
              placeholder="Select Withdrawal Type"
              className="w-full"
              displayClassName="h-[48px] rounded-full bg-input border border-border text-base text-foreground"
            />
            {errors.fundingSourceType && (
              <p className="text-sm text-red-500 mt-1 pl-4">
                {errors.fundingSourceType.message}
              </p>
            )}
          </div>
        )}
      />

      {/* Step 2: Account Selection */}
      <Controller
        name="account"
        control={control}
        rules={{ required: "Please select an account" }}
        render={({ field }) => (
          <div>
            <EnhancedSelect
              options={accountOptions}
              value={accountOptions.find((opt) => opt.value === field.value) || null}
              onChange={(opt) => field.onChange(opt?.value)}
              placeholder={
                selectedFundingSource === "fiat"
                  ? "Choose Fiat Account"
                  : "Choose Crypto Wallet"
              }
              className="w-full"
              displayClassName="h-[48px] rounded-full bg-input border border-border text-base text-foreground"
            />
            {errors.account && (
              <p className="text-sm text-red-500 mt-1 pl-4">{errors.account.message}</p>
            )}
          </div>
        )}
      />

      {/* Step 3: Amount Input */}
      <Controller
        name="amount"
        control={control}
        rules={{ 
          required: "Amount is required", 
          min: { value: 1, message: "Amount must be at least 1" },
          max: { 
            value: goal.amountSaved || 0, 
            message: `Cannot withdraw more than available amount (${goal.currency} ${goal.amountSaved})` 
          }
        }}
        render={({ field }) => (
          <FloatingLabelInput
            label={`Enter Withdrawal Amount (${goal.currency})`}
            type="number"
            min="1"
            max={goal.amountSaved?.toString()}
            step="0.01"
            inputMode="decimal"
            placeholder=" "
            {...field}
            className="w-full h-[48px] rounded-full bg-input border border-border px-4 text-base text-foreground"
            error={errors.amount?.message}
          />
        )}
      />

      <Button
        type="submit"
        className="w-full h-[48px] rounded-full bg-primary text-primary-foreground font-semibold text-base shadow-sm hover:bg-primary/90 transition-colors"
      >
        Withdraw from Savings Goal
      </Button>
    </form>
  );
}
