import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { But<PERSON> } from "@/components/custom/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import EnhancedSelect from "@/components/enhanced-select";
import { useBankList, useResolveAccount } from "@/hooks/api/fiat-banking";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import { BankIcon } from "../select-withdrawal-method";
import { Icon } from "@/components/icons";
import { BadgeCheck, Loader2 } from "lucide-react";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";

interface BankOption {
	value: string;
	label: string;
}

interface FormData {
	amount: string;
	bank: BankOption | null;
	accountNumber: string;
	note: string;
}

interface BankWithdrawalMethodProps {
	onContinue?: () => void;
}

const BankWithdrawalMethod: React.FC<BankWithdrawalMethodProps> = ({
	onContinue,
}) => {
	const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

	const {
		selectedWithdrawMethod,
		selectedCoin: selectedCurrency,
		setCurrentStep,
		setWithdrawFiatPayload,
	} = useWithdrawalFiatStore();

	const { data: bankList = [], isLoading: isBankListLoading } = useBankList(
		selectedWithdrawMethod?.raw?.name || "",
	);

	const {
		control,
		handleSubmit,
		watch,
		formState: { errors },
	} = useForm<FormData>({
		defaultValues: {
			amount: "",
			bank: null,
			accountNumber: "",
			note: "",
		},
	});

	const watchAmount = watch("amount");
	const watchBank = watch("bank");
	const watchAccountNumber = watch("accountNumber");

	const { data: resolvedAccount, isLoading: isLoadingResolveAccount } =
		useResolveAccount({
			accountNumber: watchAccountNumber,
			bankCode: watchBank?.value || "",
			transaction_method: selectedWithdrawMethod?.raw?.name || "",
		});

	// const validateAmount = (value: string) => {
	// 	if (!value) return "Amount is required";
	// 	if (isNaN(parseFloat(value)) || parseFloat(value) <= 0)
	// 		return "Please enter a valid amount";
	// 	return true;
	// };

	const validateAmount = (value: string) => {
	const num = parseFloat(value);
	if (!value || isNaN(num)) {
		return "Please enter a valid amount";
	}
	if (num < 1500) {
		return "Amount should not be less than 1500";
	}
	return true;
};

	const onSubmit = (data: FormData) => {
		setIsSubmitting(true);

		if (!selectedCurrency || !selectedWithdrawMethod?.raw) {
			console.error(
				"Missing required data: currency or withdrawal method",
			);
			setIsSubmitting(false);
			return;
		}

		
		if (!resolvedAccount?.data?.account_name || !watchBank?.label) {
			console.error("Account information not resolved");
			setIsSubmitting(false);
			return;
		}

		
		setWithdrawFiatPayload({
			payload: {
				type: "bank",
				coin: selectedCurrency,
				method: selectedWithdrawMethod.raw,
				amount: data.amount,
				note: data.note,
				feeInfo: calcResolvedAmount(),
				accountName: resolvedAccount.data.account_name,
				accountNumber: watchAccountNumber,
				bankName: watchBank.label,
				bankCode: watchBank.value
			},
		});

		
		setCurrentStep("previewWithdrawal", true);

		
		if (onContinue) {
			onContinue();
		}

		setIsSubmitting(false);
	};

	function calcResolvedAmount(): {
		fee: number;
		amount: number;
	} {
		const amount = parseFloat(watchAmount);
		const feeAmount = parseFloat(selectedWithdrawMethod?.raw?.fee || "0");
		const feeType =
			selectedWithdrawMethod?.raw?.fee_type ??
			selectedWithdrawMethod?.raw?.["fee type"];

		if (isNaN(amount) || isNaN(feeAmount)) {
			return {
				fee: 0,
				amount: 0,
			};
		}

		if (feeType === "PERCENTAGE" || feeType === "DYNAMIC") {
			const fee = (amount * feeAmount) / 100;

			return {
				fee,
				amount: amount + fee >= 0 ? amount + fee : 0,
			};
		} else {
			const fee = feeAmount;
			return {
				fee: fee,
				amount: amount + fee >= 0 ? amount + fee : 0,
			};
		}
	}

	return (
		<div className="p-4 max-w-xl mx-auto">
			<div className="mb-6">
				<p className="text-gray-400">
					Withdraw to a specified bank account. Please ensure the
					details are correct to avoid any issues with the
					transaction.
				</p>
			</div>

			<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
				{/* Amount Input */}
				<div className="relative">
					<Controller
						name="amount"
						control={control}
						rules={{
							// required: "Amount is required",
							required: "Amount shouldn't be less than 1500",
							validate: validateAmount,
						}}
						render={({ field, fieldState }) => (
							<div>
								<FloatingLabelInput
									label="Enter Amount"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => {
										
										const value = e.target.value.replace(
											/[^0-9.]/g,
											"",
										);

										
										const parts = value.split(".");
										if (parts.length > 2) {
											return;
										}

										field.onChange(value);
									}}
									className="border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400"
								/>
								{fieldState.error && (
									<p className="text-red-500 text-xs mt-1">
										{fieldState.error.message}
									</p>
								)}
							</div>
						)}
					/>
					{/* <p className="ml-2 pt-2 text-xs text-red-700">Amount shouldn't be less than 2000</p> */}
					<div className="text-xs text-gray-400 mt-1 px-4">
						Fee:{" "}
						{parseCurrencySymbol(selectedCurrency?.currency || "")}{" "}
						{calcResolvedAmount().fee}
					</div>
				</div>
				

				{/* Bank Selection */}
				<div className="relative">
					<Controller
						name="bank"
						control={control}
						rules={{ required: "Bank is required" }}
						render={({ field }) => (
							<EnhancedSelect
								options={bankList}
								value={field.value}
								onChange={(value) => {
									field.onChange(value);
								}}
								isLoading={isBankListLoading}
								placeholder="Select Bank"
								isSearchable={true}
								className="w-full"
								displayClassName="p-4 rounded-full"
								renderSelected={(option) => (
									<div className="flex items-center gap-2">
										<span>{option.label}</span>
									</div>
								)}
								renderOption={(option) => (
									<div className="flex items-center gap-2 w-full">
										<span className="icon">
											<BankIcon />
										</span>
										<div className="flex flex-col">
											<span>{option.label}</span>
										</div>
										<span className="ml-auto">
											<Icon
												name="arrow-left"
												className="size-6 rotate-180"
											/>
										</span>
									</div>
								)}
							/>
						)}
					/>
					{errors.bank && (
						<p className="text-red-500 text-xs mt-1">
							{errors.bank.message}
						</p>
					)}
				</div>

				{/* Account Number */}
				<div className="relative">
					<Controller
						name="accountNumber"
						control={control}
						rules={{
							required: "Account Number is required",
							pattern: {
								value: /^\d{10}$/,
								message: "Account number must be 10 digits",
							},
						}}
						render={({ field, fieldState }) => (
							<div>
								<FloatingLabelInput
									label="Enter Account Number"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => {
										
										const value = e.target.value.replace(
											/\D/g,
											"",
										);
										field.onChange(value);
									}}
									maxLength={10}
									className="border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400"
								/>
								<div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
									{field.value.length}/10
								</div>
								{fieldState.error && (
									<p className="text-red-500 text-xs mt-1">
										{fieldState.error.message ||
											"Account Number is required"}
									</p>
								)}
							</div>
						)}
					/>
				</div>
				<span className="flex items-center gap-1.5 text-[10px] px-4 -mt-4.5">
					{isLoadingResolveAccount ? (
						<span className="flex items-center gap-1.5 text-gray-400">
							<Loader2 className="size-4 animate-spin" />
							Verifying account name...
						</span>
					) : resolvedAccount?.data?.account_name ? (
						<span className="flex items-center gap-1.5 font-semibold text-green-600">
							<BadgeCheck className="text-green-500 size-3.5" />
							{resolvedAccount.data.account_name}
						</span>
					) : null}
				</span>

				{/* Note */}
				<div className="relative">
					<Controller
						name="note"
						control={control}
						render={({ field }) => (
							<div>
								<FloatingLabelInput
									label="Add Note"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => field.onChange(e.target.value)}
									className="border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400"
								/>
								<p className="text-xs text-gray-400 mt-1 px-4">
									Add a descriptive note for reference
								</p>
							</div>
						)}
					/>
				</div>

				{/* Summary */}
				<div className="bg-gray-200 p-4 rounded-lg text-center">
					<p className="text-gray-600">
						{parseCurrencySymbol(selectedCurrency?.currency || "") +
							calcResolvedAmount().amount.toLocaleString()}{" "}
						will be withdrawn from your account.
					</p>
				</div>

				{/* Submit Button */}
				<Button
					type="submit"
					className={`w-full py-4 rounded-full font-medium ${
						watchAmount &&
						watchBank &&
						watchAccountNumber &&
						!errors.amount &&
						!errors.bank &&
						!errors.accountNumber
							? "bg-primary text-black"
							: "bg-gray-700 text-gray-400 cursor-not-allowed"
					}`}
					disabled={
						!watchAmount ||
						!watchBank ||
						!watchAccountNumber ||
						!!errors.amount ||
						!!errors.bank ||
						!!errors.accountNumber ||
						isSubmitting
					}
				>
					{isSubmitting ? "Processing..." : "Continue"}
				</Button>
			</form>
		</div>
	);
};

export default BankWithdrawalMethod;
