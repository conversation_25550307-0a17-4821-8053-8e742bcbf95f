import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";

interface WithdrawSavingsSuccessProps {
  amount: number;
  currency: string;
  goalName: string;
  onViewReceipt?: () => void;
  onClose: () => void;
}

export const WithdrawSavingsSuccess: React.FC<WithdrawSavingsSuccessProps> = ({
  amount,
  currency,
  goalName,
  onViewReceipt,
  onClose,
}) => {
  return (
    <div className="flex flex-col items-center justify-center h-full space-y-6 py-12 px-6">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", duration: 0.5 }}
        className="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center"
      >
        <CheckCircle className="w-12 h-12 text-white" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="text-center space-y-4"
      >
        <h2 className="text-2xl font-bold text-foreground">
          Withdrawal Successful!
        </h2>
        <p className="text-muted-foreground">
          You have successfully withdrawn{" "}
          <span className="font-semibold text-foreground">
            {currency} {amount.toLocaleString()}
          </span>{" "}
          from your "{goalName}" savings goal
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
        className="w-full space-y-3"
      >
        {onViewReceipt && (
          <Button
            onClick={onViewReceipt}
            variant="outline"
            className="w-full h-[48px] rounded-full border-primary text-primary hover:bg-primary hover:text-primary-foreground"
          >
            View Receipt
          </Button>
        )}
        <Button
          onClick={onClose}
          className="w-full h-[48px] rounded-full bg-primary text-primary-foreground"
        >
          Done
        </Button>
      </motion.div>
    </div>
  );
};
