import React, { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { But<PERSON> } from "@/components/custom/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import { useGetUserCustomerByClypId } from "@/hooks/api/user";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";
import { BadgeCheck, Loader2 } from "lucide-react";

interface FormData {
	identifier: string;
	amount: string;
	note: string;
}

const InternalTransferMethod = () => {
	const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

	const {
		selectedWithdrawMethod,
		selectedCoin: selectedCurrency,
		setCurrentStep,
        setWithdrawFiatPayload
	} = useWithdrawalFiatStore();

	const {
		control,
		handleSubmit,
		watch,
		formState: { errors },
	} = useForm<FormData>({
		defaultValues: {
			identifier: "",
			amount: "",
			note: "",
		},
	});

	const watchIdentifier = watch("identifier");
	const watchAmount = watch("amount");

	const { data: userData, isLoading: isUserLoading } =
		useGetUserCustomerByClypId(watchIdentifier);

	const validateIdentifier = (value: string) => {
		if (!value) return "Clyp ID is required";
		if (value.length < 4) return "Clyp ID must be at least 4 characters";
		return true;
	};

	const validateAmount = (value: string) => {
		if (!value) return "Amount is required";
		if (isNaN(parseFloat(value)) || parseFloat(value) <= 0)
			return "Please enter a valid amount";
		const minAmount =
			selectedWithdrawMethod?.raw?.restrictions?.TransactionLimits?.[0];
		if (minAmount && parseFloat(value) < minAmount)
			return `Amount must be greater than ${minAmount}`;
		return true;
	};

	const onSubmit = (data: FormData) => {
		setIsSubmitting(true);

		if (!selectedCurrency || !selectedWithdrawMethod?.raw) {
			console.error(
				"Missing required data: currency or withdrawal method",
			);
			setIsSubmitting(false);
			return;
		}

		
		setWithdrawFiatPayload({
			payload: {
				type: "internal",
				coin: selectedCurrency,
				method: selectedWithdrawMethod.raw,
				amount: data.amount,
				note: data.note,
				feeInfo: calcResolvedAmount(),
				identifier: data.identifier,
			},
		});

		setCurrentStep("previewWithdrawal", true);
		setIsSubmitting(false);
	};

	function calcResolvedAmount(): {
		fee: number;
		amount: number;
	} {
		const amount = parseFloat(watchAmount);
		const feeAmount = parseFloat(selectedWithdrawMethod?.raw?.fee || "0");
		const feeType =
			selectedWithdrawMethod?.raw?.fee_type ??
			selectedWithdrawMethod?.raw?.["fee type"];

		if (isNaN(amount) || isNaN(feeAmount)) {
			return {
				fee: 0,
				amount: 0,
			};
		}

		if (feeType === "PERCENTAGE" || feeType === "DYNAMIC") {
			const fee = (amount * feeAmount) / 100;

			return {
				fee,
				amount: amount + fee >= 0 ? amount + fee : 0,
			};
		} else {
			const fee = feeAmount;
			return {
				fee: fee,
				amount: amount + fee >= 0 ? amount + fee : 0,
			};
		}
	}

	return (
		<div className="p-4 max-w-xl mx-auto">
			<div className="mb-6">
				{/* <h2 className="text-2xl font-bold">Clyp P2P Transfer</h2> */}
				<p className="text-gray-400">
					Withdraw to an account within Clyp
				</p>
			</div>

			<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
				{/* Clyp ID Input */}
				<div className="relative">
					<Controller
						name="identifier"
						control={control}
						rules={{
							required: "Clyp ID is required",
							validate: validateIdentifier,
						}}
						render={({ field, fieldState }) => (
							<div>
								<FloatingLabelInput
									label="Enter user's Clyp ID"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => {
										field.onChange(e.target.value);
									}}
									className="border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400"
								/>
								{fieldState.error && (
									<p className="text-red-500 text-xs mt-1">
										{fieldState.error.message}
									</p>
								)}
							</div>
						)}
					/>
				</div>
				<span className="flex items-center gap-1.5 text-[10px] px-4 -mt-4.5">
					{isUserLoading ? (
						<span className="flex items-center gap-1.5 text-gray-400">
							<Loader2 className="size-4 animate-spin" />
							Verifying clyp user id...
						</span>
					) : userData ? (
						<span className="flex items-center gap-1.5 font-semibold text-green-600">
							<BadgeCheck className="text-green-500 size-3.5" />
							{`${userData.first_name} ${userData.last_name}`}
						</span>
					) : null}
				</span>

				{/* Amount Input */}
				<div className="relative">
					<Controller
						name="amount"
						control={control}
						rules={{
							required: "Amount is required",
							validate: validateAmount,
						}}
						render={({ field, fieldState }) => (
							<div>
								<FloatingLabelInput
									label="Enter Amount"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => {
										
										const value = e.target.value.replace(
											/[^0-9.]/g,
											"",
										);

										
										const parts = value.split(".");
										if (parts.length > 2) {
											return;
										}

										field.onChange(value);
									}}
									className="border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400"
								/>
								{fieldState.error && (
									<p className="text-red-500 text-xs mt-1">
										{fieldState.error.message}
									</p>
								)}
							</div>
						)}
					/>
					<div className="text-xs text-gray-400 mt-1 px-4">
						Fee:{" "}
						{parseCurrencySymbol(selectedCurrency?.currency || "")}{" "}
						{calcResolvedAmount().fee}
					</div>
				</div>

				{/* Note */}
				<div className="relative">
					<Controller
						name="note"
						control={control}
						rules={{
							required: "Note is required",
							minLength: {
								value: 3,
								message: "Note must be at least 3 characters",
							},
						}}
						render={({ field, fieldState }) => (
							<div>
								<FloatingLabelInput
									label="Add Note"
									type="text"
									value={field.value}
									onChange={(
										e: React.ChangeEvent<HTMLInputElement>,
									) => field.onChange(e.target.value)}
									className="border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400"
								/>
								<p className="text-xs text-gray-400 mt-1 px-4">
									Add a descriptive note for reference
								</p>
								{fieldState.error && (
									<p className="text-red-500 text-xs mt-1">
										{fieldState.error.message}
									</p>
								)}
							</div>
						)}
					/>
				</div>

				{/* Summary */}
				<div className="bg-gray-200 p-4 rounded-lg text-center">
					<p className="text-gray-600">
						You are currently sending {selectedCurrency?.currency}
					</p>
				</div>

				{/* Submit Button */}
				<Button
					type="submit"
					className={`w-full py-4 rounded-full font-medium ${
						watchIdentifier &&
						watchAmount &&
						!errors.identifier &&
						!errors.amount &&
						!errors.note
							? "bg-primary text-black"
							: "bg-gray-700 text-gray-400 cursor-not-allowed"
					}`}
					disabled={
						!watchIdentifier ||
						!watchAmount ||
						!!errors.identifier ||
						!!errors.amount ||
						!!errors.note ||
						isSubmitting
					}
				>
					{isSubmitting ? "Processing..." : "Continue"}
				</Button>
			</form>
		</div>
	);
};

export default InternalTransferMethod;
