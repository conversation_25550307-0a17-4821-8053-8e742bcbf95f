// import { Pin<PERSON>anager } from "@/components/transaction/transaction-pin-manager";
// import { ResponseStatus } from "@/config/enums";
// import { useCreateWithdrawal } from "@/hooks/api/fiat-banking";
// import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
// import { notify } from "@/utils/notify";
// import { useState } from "react";

// const WithdrawalPinManager = () => {
// 	const { withdrawFiatPayload, setCurrentStep } = useWithdrawalFiatStore();
// 	const [isProcessingWithdrawal, setIsProcessingWithdrawal] = useState(false);
// 	const { mutateAsync: createWithdrawalMutation } = useCreateWithdrawal();

// 	const handleComplete = async () => {
// 		if (!withdrawFiatPayload) {
// 			notify("Missing withdrawal information", ResponseStatus.ERROR);
// 			return;
// 		}

// 		setIsProcessingWithdrawal(true);

// 		try {
// 			const { payload } = withdrawFiatPayload;

// 			const payloadData =
// 				payload.type === "internal"
// 					// ? {
// 					// 		account_id: payload.coin.account_id,
// 					// 		transaction_method: payload.method.name,
// 					// 		amount: payload.amount,
// 					// 		receiver: payload.identifier,
// 					// 		fee: payload.feeInfo.fee.toString(),
// 					// 		description: payload.note,
// 					//   }
// 					// : {
// 					// 		account_id: payload.coin.account_id,
// 					// 		transaction_method: payload.method.name,
// 					// 		amount: payload.amount,
// 					// 		accountNumber: payload.accountName,
// 					// 		bankCode: payload.bankName,
// 					// 		fee: payload.feeInfo.fee.toString(),
// 					// 		description: payload.note,
// 					//   };

// 					  ? {
//         account_id: payload.coin.account_id,
//         transaction_method: payload.method.name,
//         amount: payload.amount,
//         receiver: payload.identifier,
//         fee: payload.feeInfo.fee.toString(),
//         note: payload.note, // Make sure this is consistent
//         recurrsion: {
//           start_date: new Date().toISOString().split('T')[0],
//           end_date: new Date().toISOString().split('T')[0],
//           frequency: "ONCE",
//           day_of_week: null,
//           date_of_month: null
//         }
//       }
//     : {
//         account_id: payload.coin.account_id,
//         transaction_method: payload.method.name,
//         amount: payload.amount,
//         accountNumber: payload.accountName,
//         bankCode: payload.bankName,
//         fee: payload.feeInfo.fee.toString(),
//         note: payload.note, // Make sure this is consistent
//         recurrsion: {
//           start_date: new Date().toISOString().split('T')[0],
//           end_date: new Date().toISOString().split('T')[0],
//           frequency: "ONCE",
//           day_of_week: null,
//           date_of_month: null 
//         }
//       };

// 			await createWithdrawalMutation(payloadData, {
// 				onSuccess: () => {
// 					notify(
// 						payload.type === "internal"
// 							? "Transfer completed successfully"
// 							: "Withdrawal created successfully",
// 						ResponseStatus.SUCCESS,
// 					);
// 					setIsProcessingWithdrawal(false);
// 					setCurrentStep("success");
// 				},
// 				onError: (error) => {
// 					console.error("Withdrawal creation failed:", error);
// 					notify(
// 						`Withdrawal creation failed - ${error.message}`,
// 						ResponseStatus.ERROR,
// 					);
// 					setIsProcessingWithdrawal(false);
// 				},
// 			});
// 		} catch (error) {
// 			console.error("Withdrawal failed:", error);
// 			notify("Something went wrong", ResponseStatus.ERROR);
// 			setIsProcessingWithdrawal(false);
// 		}
// 	};

// 	return (
// 		<PinManager
// 			onComplete={handleComplete}
// 			isProcessing={isProcessingWithdrawal}
// 		/>
// 	);
// };

// export default WithdrawalPinManager;

import { PinManager } from "@/components/transaction/transaction-pin-manager";
import { ResponseStatus } from "@/config/enums";
import { useCreateWithdrawal } from "@/hooks/api/fiat-banking";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import { notify } from "@/utils/notify";
import { useState } from "react";

const WithdrawalPinManager = () => {
  const { withdrawFiatPayload, setCurrentStep } = useWithdrawalFiatStore();
  const [isProcessingWithdrawal, setIsProcessingWithdrawal] = useState(false);
  const { mutateAsync: createWithdrawalMutation } = useCreateWithdrawal();

  const handleComplete = async () => {
    if (!withdrawFiatPayload) {
      notify("Missing withdrawal information", ResponseStatus.ERROR);
      return;
    }

    const { payload } = withdrawFiatPayload;

    if (
      !payload ||
      !payload.coin?.account_id ||
      !payload.method?.name ||
      !payload.amount ||
      !payload.feeInfo?.fee
    ) {
      notify("Incomplete withdrawal data", ResponseStatus.ERROR);
      return;
    }

    let payloadData;
    try {
      const today = new Date().toISOString().split("T")[0];

      payloadData =
        payload.type === "internal"
          ? {
              account_id: payload.coin.account_id,
              transaction_method: payload.method.name,
              amount: payload.amount,
              receiver: payload.identifier,
              fee: payload.feeInfo.fee.toString(),
              note: payload.note,
              recurrsion: {
                start_date: today,
                end_date: today,
                frequency: "ONCE",
                day_of_week: null,
                date_of_month: null,
              },
            }
          : {
              account_id: payload.coin.account_id,
              transaction_method: payload.method.name,
              amount: payload.amount,
			  accountNumber: payload.accountNumber,
              bankCode: payload.bankCode,
              fee: payload.feeInfo.fee.toString(),
              note: payload.note,
            //   recurrsion: {
            //     start_date: today,
            //     end_date: today,
            //     frequency: "ONCE",
            //     day_of_week: null,
            //     date_of_month: null,
            //   },
             //   accountName: payload.accountName,
            };

      console.log("Constructed withdrawal payloadData:", payloadData);

    } catch (err) {
      console.error("Payload construction failed:", err);
      notify("Invalid withdrawal data", ResponseStatus.ERROR);
      setIsProcessingWithdrawal(false);
      return;
    }

    setIsProcessingWithdrawal(true);

    try {
      await createWithdrawalMutation(payloadData, {
        onSuccess: () => {
          notify(
            payload.type === "internal"
              ? "Transfer completed successfully"
              : "Withdrawal created successfully",
            ResponseStatus.SUCCESS
          );
          setIsProcessingWithdrawal(false);
          setCurrentStep("success");
        },
        onError: (error) => {
          console.error("Withdrawal creation failed:", error);
          notify(
            `Withdrawal creation failed - ${error.message}`,
            ResponseStatus.ERROR
          );
          setIsProcessingWithdrawal(false);
        },
      });
    } catch (error) {
      console.error("Withdrawal failed (outer catch):", error);
      notify(
        `Unexpected error: ${
          error instanceof Error ? error.message : JSON.stringify(error)
        }`,
        ResponseStatus.ERROR
      );
      setIsProcessingWithdrawal(false);
    }
  };

  return (
    <PinManager
      onComplete={handleComplete}
      isProcessing={isProcessingWithdrawal}
    />
  );
};

export default WithdrawalPinManager;

