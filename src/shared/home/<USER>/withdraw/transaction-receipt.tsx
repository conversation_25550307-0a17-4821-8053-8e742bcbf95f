import { Button } from "@/components/custom/button";
import { TransactionSummaryCard } from "@/components/transaction-summary-card";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";
import { format } from "date-fns";

export function TransactionReceipt() {
	const { withdrawFiatPayload, selectedCoin } = useWithdrawalFiatStore();

	const onShareReceipt = () => {
		
		alert("Receipt shared!");
	};

	if (!withdrawFiatPayload || !selectedCoin) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<p className="text-gray-500">
					Transaction information not available.
				</p>
			</div>
		);
	}

	const { payload } = withdrawFiatPayload;
	const isInternalTransfer = payload.type === "internal";
	const currencySymbol = parseCurrencySymbol(selectedCoin.currency || "");
	const amount = payload.amount;
	const fee = payload.feeInfo.fee.toString();
	const date = format(new Date(), "dd-MM-yyyy | HH:mm:ss");

	
	const details = [
		{
			label: "Transaction Type",
			value: isInternalTransfer ? "Internal Transfer" : "Bank Withdrawal",
		},
		{
			label: "From",
			value: `${selectedCoin.currency_name} Account`,
		},
		{
			label: "To",
			value: isInternalTransfer
				? `Clyp User (${payload.identifier})`
				: `${payload.bankName} (${payload.accountName})`,
		},
		{
			label: "Amount",
			value: `${currencySymbol}${amount}`,
		},
		{
			label: "Fee",
			value: `${currencySymbol}${fee}`,
		},
		{
			label: "Total",
			value: `${currencySymbol}${payload.feeInfo.amount}`,
		},
		{
			label: "Date",
			value: date,
		},
	];

	
	if (payload.note) {
		details.push({
			label: "Note",
			value: payload.note,
		});
	}

	return (
		<div className="flex flex-col px-6 space-y-6">
			<TransactionSummaryCard
				amount={`${currencySymbol}${amount}`}
				currency={selectedCoin.currency}
				status="successful"
				details={details}
			/>

			{/* Share Receipt Button */}
			<Button
				className="w-full py-6 text-base bg-primary hover:bg-primary/80 text-white rounded-full mt-2"
				onClick={onShareReceipt}
			>
				Share Receipt
			</Button>
		</div>
	);
}
