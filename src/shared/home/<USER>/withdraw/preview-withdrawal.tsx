import { Button } from "@/components/custom/button";
import { motion } from "framer-motion";
import { format } from "date-fns";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import { parseCurrencySymbol } from "@/utils/parse-currency-symbol";

const PreviewWithdrawal = () => {
	const {
		withdrawFiatPayload,
		selectedWithdrawMethod,
		selectedCoin: selectedCurrency,
		setCurrentStep,
	} = useWithdrawalFiatStore();

	const handleConfirm = () => {
		setCurrentStep("pin");
	};

	if (
		!withdrawFiatPayload ||
		!selectedCurrency ||
		!selectedWithdrawMethod?.raw
	) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<p className="text-gray-500">
					No transaction data available. Please go back and try again.
				</p>
			</div>
		);
	}

	const { payload } = withdrawFiatPayload;
	const currencySymbol = parseCurrencySymbol(selectedCurrency.currency || "");
	const isInternalTransfer = payload.type === "internal";

	return (
		<div className="flex flex-col p-6 space-y-6">
			{/* Amount Display */}
			<motion.div
				className="text-center my-4"
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ delay: 0.1 }}
			>
				<h2 className="text-xl font-bold">
					You are about to{" "}
					{isInternalTransfer ? "transfer" : "withdraw"}{" "}
					{currencySymbol}
					{payload.amount}{" "}
					{isInternalTransfer
						? "to another Clyp account"
						: "to your bank account"}
				</h2>
			</motion.div>

			{/* Transaction Details */}
			<motion.div
				className="space-y-4"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
			>
				<div className="flex justify-between">
					<span className="text-gray-500">From</span>
					<span className="font-medium">
						{selectedCurrency.currency_name} Account
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Currency</span>
					<span className="font-medium">
						{selectedCurrency.currency}
					</span>
				</div>

				{/* Recipient details - different for each type */}
				<div className="flex justify-between">
					<span className="text-gray-500">
						{isInternalTransfer ? "To Clyp ID" : "To Bank"}
					</span>
					<span className="font-medium">
						{isInternalTransfer
							? payload.identifier
							: payload.bankName}
					</span>
				</div>

				{/* Show account name for bank transfers */}
				{!isInternalTransfer && (
					<div className="flex justify-between">
						<span className="text-gray-500">Account Name</span>
						<span className="font-medium">
							{payload.accountName}
						</span>
					</div>
				)}

				<div className="flex justify-between">
					<span className="text-gray-500">Amount</span>
					<span className="font-medium">
						{currencySymbol}
						{payload.amount}
					</span>
				</div>

				<div className="flex justify-between">
					<span className="text-gray-500">Transaction Fee</span>
					<span className="font-medium">
						{currencySymbol}
						{payload.feeInfo.fee}
					</span>
				</div>

				<div className="flex justify-between">
					<span className="text-gray-500">Total</span>
					<span className="font-medium">
						{currencySymbol}
						{payload.feeInfo.amount}
					</span>
				</div>

				<div className="flex justify-between">
					<span className="text-gray-500">Date</span>
					<span className="font-medium">
						{format(new Date(), "dd-MM-yyyy | HH:mm:ss")}
					</span>
				</div>

				{/* Show note if provided */}
				{payload.note && (
					<div className="flex justify-between">
						<span className="text-gray-500">Note</span>
						<span className="font-medium">{payload.note}</span>
					</div>
				)}
			</motion.div>

			{/* Confirm Button */}
			<motion.div
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.5 }}
			>
				<Button
					className="bg-primary hover:bg-primary/90 text-white rounded-full py-6 mt-6 w-full"
					onClick={handleConfirm}
				>
					Confirm {isInternalTransfer ? "Transfer" : "Withdrawal"}
				</Button>
			</motion.div>
		</div>
	);
};

export default PreviewWithdrawal;
