// import EnhancedSelect from "@/components/enhanced-select";
// import { Button } from "@/components/ui/button";
// import { useGetFiatWalletWithdrawalMethod } from "@/hooks/api/fiat-banking";
// import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
// import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
// import { IFiatWithdrawalMethod } from "@/types/fiat-banking";
// import { IFiatWallet } from "@/types/fiat-wallets";
// import { IOption } from "@/types/general";
// import { formatAmount } from "@/utils/format-amount";
// import { Plus } from "lucide-react";
// import { useState } from "react";

// const SelectWithdrawalMethod = () => {
// 	const {
// 		setSelectedWithdrawMethod,
// 		setSelectedCoin: setSelectedCurrency,
// 		setCurrentStep,
// 	} = useWithdrawalFiatStore();

// 	const [selectedFiat, setSelectedFiat] = useState<IFiatWallet | null>(null);

// 	const [selectedWithdrawFiatMethod, setSelectedWithdrawFiatMethod] =
// 		useState<IOption<IFiatWithdrawalMethod> | null>(null);

// 	const {
// 		data: withdrawFiatMethods = [],
// 		isLoading: isLoadingCryptoReceiveMethod,
// 	} = useGetFiatWalletWithdrawalMethod(selectedFiat?.currency ?? "");

// 	const { data: fiatWallets = [], isLoading: fiatLoading } =
// 		useFiatUserWallets();

// 	const fiatOptions: IOption<IFiatWallet>[] = fiatWallets.map((wallet) => ({
// 		id: `fiat-${wallet.currency}`,
// 		value: wallet.currency,
// 		label: wallet.currency,
// 		icon: wallet.image,
// 		raw: wallet,
// 	}));

// 	const withrawFiatMethodOptions: IOption<IFiatWithdrawalMethod>[] =
// 		withdrawFiatMethods?.map(generateOption).filter((x) => !x.hidden) || [];

// 	const selectedFiatOption =
// 		fiatOptions.find((option) => option.value === selectedFiat?.currency) ||
// 		null;

// 	function generateOption(
// 		item: IFiatWithdrawalMethod,
// 	): IOption<IFiatWithdrawalMethod> {
// 		if (item.name.includes("INTERNAL_TRANSFER")) {
// 			return {
// 				label: "Clyp P2P Transfer",
// 				icon: <ArrowDataTransferHorizontalIcon />,
// 				value: "INTERNAL_TRANSFER",
// 				raw: item as IFiatWithdrawalMethod,
// 			};
// 		} else if (item.name.includes("INTERNAL TRANSFER")) {
// 			return {
// 				label: "Clyp Internal Transfer",
// 				icon: <ArrowDataTransferHorizontalIcon />,
// 				value: "INTERNAL_TRANSFER",
// 				raw: item as IFiatWithdrawalMethod,
// 			};
// 		} else if (item.name.includes("WITHDRAWAL")) {
// 			return {
// 				label: "Withdraw to Bank",
// 				icon: <BankIcon />,
// 				value: "WITHDRAWAL",
// 				raw: item as IFiatWithdrawalMethod,
// 			};
// 		} else if (item.name.includes("INSTANT SWAP DEBIT")) {
// 			return {
// 				label: "Instant Swap Debit",
// 				icon: <FlashIcon />,
// 				value: "SWAP",
// 				hidden: true,
// 				raw: item as IFiatWithdrawalMethod,
// 			};
// 		} else
// 			return {
// 				label: "-",
// 				value: "-",
// 				hidden: true,
// 			};
// 	}

// 	const handleFiatChange = (option: IOption<IFiatWallet> | null) => {
// 		if (option && option.raw) {
// 			setSelectedFiat(option.raw);

// 			setSelectedWithdrawFiatMethod(null);
// 		} else {
// 			setSelectedFiat(null);
// 			setSelectedWithdrawFiatMethod(null);
// 		}
// 	};

// 	const handleWithdrawFiatMethodChange = (
// 		option: IOption<IFiatWithdrawalMethod> | null,
// 	) => {
// 		if (option && option.raw) {
// 			setSelectedWithdrawMethod(option);
// 			if (selectedFiat) {
// 				setSelectedCurrency(selectedFiat);
// 			}
// 			setSelectedWithdrawFiatMethod(option);

// 			if (
// 				option.raw.name.includes("INTERNAL_TRANSFER") ||
// 				option.raw.name.includes("INTERNAL TRANSFER")
// 			) {
// 				setCurrentStep("internalTransfer");
// 			} else if (option.raw.name.includes("WITHDRAWAL")) {
// 				setCurrentStep("bankWithdrawal");
// 			}
// 		}
// 	};

// 	const handleAddNew = () => {};

// 	const walletHeader = (
// 		<div className="py-2 flex justify-between items-center">
// 			<h3 className="text-2xl font-bold font-body">Wallets</h3>
// 			<Button
// 				onClick={handleAddNew}
// 				className="text-sm flex items-center rounded-full"
// 			>
// 				Add New
// 				<Plus className="h-3 w-3 ml-2" />
// 			</Button>
// 		</div>
// 	);

// 	return (
// 		<div className="p-6 space-y-6">
// 			{/* Coin selection */}
// 			<div className="relative">
// 				<EnhancedSelect<IFiatWallet>
// 					header={walletHeader}
// 					options={fiatOptions}
// 					value={selectedFiatOption}
// 					onChange={handleFiatChange}
// 					placeholder="Choose Fiat Account"
// 					isLoading={fiatLoading}
// 					className="w-full mb-6"
// 					displayClassName="p-4"
// 					renderSelected={(option) => (
// 						<div className="flex items-center gap-2">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<span>{option.value}</span>
// 						</div>
// 					)}
// 					renderOption={(option) => (
// 						<div className="flex items-center gap-2 w-full">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<div className="flex flex-col">
// 								<span>{option.label}</span>
// 								<span className="text-xs text-gray-500 group-hover:text-white">
// 									Balance:{" "}
// 									{formatAmount(
// 										option.raw?.available_balance,
// 										option.value,
// 									)}
// 								</span>
// 							</div>
// 						</div>
// 					)}
// 				/>
// 			</div>

// 			{/* Receive method selection */}
// 			<div className="relative">
// 				<EnhancedSelect<IFiatWithdrawalMethod>
// 					options={withrawFiatMethodOptions}
// 					value={selectedWithdrawFiatMethod}
// 					onChange={handleWithdrawFiatMethodChange}
// 					placeholder="Choose Receive Method"
// 					isLoading={isLoadingCryptoReceiveMethod}
// 					className="w-full mb-6"
// 					displayClassName="p-4"
// 					isSearchable={false}
// 					disabled={!selectedFiat}
// 					renderSelected={(option) => (
// 						<div className="flex items-center gap-2">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<span>{option.label}</span>
// 						</div>
// 					)}
// 					renderOption={(option) => (
// 						<div className="flex items-center gap-2 w-full">
// 							<div className="w-6 h-6 flex items-center justify-center">
// 								{typeof option.icon === "string" ? (
// 									<img
// 										src={option.icon}
// 										alt={option.value}
// 										className="w-full h-full object-contain rounded-full"
// 									/>
// 								) : option.icon ? (
// 									option.icon
// 								) : (
// 									<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 								)}
// 							</div>
// 							<div className="flex flex-col">
// 								<span>{option.label}</span>
// 							</div>
// 						</div>
// 					)}
// 				/>
// 			</div>
// 		</div>
// 	);
// };

// export default SelectWithdrawalMethod;

// const ArrowDataTransferHorizontalIcon = (
// 	props: React.SVGProps<SVGSVGElement>,
// ) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M19 9H6.65856C5.65277 9 5.14987 9 5.02472 8.69134C4.89957 8.38268 5.25517 8.01942 5.96637 7.29289L8.21091 5"
// 			stroke="#000000"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		></path>
// 		<path
// 			d="M5 15H17.3414C18.3472 15 18.8501 15 18.9753 15.3087C19.1004 15.6173 18.7448 15.9806 18.0336 16.7071L15.7891 19"
// 			stroke="#000000"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		></path>
// 	</svg>
// );

// export const BankIcon = (props: React.SVGProps<SVGSVGElement>) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M2 8.56907C2 7.37289 2.48238 6.63982 3.48063 6.08428L7.58987 3.79744C9.7431 2.59915 10.8197 2 12 2C13.1803 2 14.2569 2.59915 16.4101 3.79744L20.5194 6.08428C21.5176 6.63982 22 7.3729 22 8.56907C22 8.89343 22 9.05561 21.9646 9.18894C21.7785 9.88945 21.1437 10 20.5307 10H3.46928C2.85627 10 2.22152 9.88944 2.03542 9.18894C2 9.05561 2 8.89343 2 8.56907Z"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 		<path
// 			d="M11.9959 7H12.0049"
// 			stroke="currentColor"
// 			strokeWidth="2"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		/>
// 		<path
// 			d="M4 10V18.5M8 10V18.5"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 		<path
// 			d="M16 10V18.5M20 10V18.5"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 		<path
// 			d="M19 18.5H5C3.34315 18.5 2 19.8431 2 21.5C2 21.7761 2.22386 22 2.5 22H21.5C21.7761 22 22 21.7761 22 21.5C22 19.8431 20.6569 18.5 19 18.5Z"
// 			stroke="currentColor"
// 			strokeWidth="1.5"
// 		/>
// 	</svg>
// );

// const FlashIcon = (props: React.SVGProps<SVGSVGElement>) => (
// 	<svg
// 		xmlns="http://www.w3.org/2000/svg"
// 		viewBox="0 0 24 24"
// 		width={24}
// 		height={24}
// 		color={"#000000"}
// 		fill={"none"}
// 		{...props}
// 	>
// 		<path
// 			d="M5.22576 11.3294L12.224 2.34651C12.7713 1.64397 13.7972 2.08124 13.7972 3.01707V9.96994C13.7972 10.5305 14.1995 10.985 14.6958 10.985H18.0996C18.8729 10.985 19.2851 12.0149 18.7742 12.6706L11.776 21.6535C11.2287 22.356 10.2028 21.9188 10.2028 20.9829V14.0301C10.2028 13.4695 9.80048 13.015 9.3042 13.015H5.90035C5.12711 13.015 4.71494 11.9851 5.22576 11.3294Z"
// 			stroke="#000000"
// 			strokeWidth="1.5"
// 			strokeLinecap="round"
// 			strokeLinejoin="round"
// 		></path>
// 	</svg>
// );


import EnhancedSelect from "@/components/enhanced-select";
import { Button } from "@/components/ui/button";
import { useGetFiatWalletWithdrawalMethod } from "@/hooks/api/fiat-banking";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import useFiatSelectionStore from "@/store/fiat-selection-store";
import { IFiatWithdrawalMethod } from "@/types/fiat-banking";
import { IFiatWallet } from "@/types/fiat-wallets";
import { IOption } from "@/types/general";
import { formatAmount } from "@/utils/format-amount";
import { Plus } from "lucide-react";
import { useState, useEffect } from "react";
const SelectWithdrawalMethod = () => {
	const {
		setSelectedWithdrawMethod,
		setSelectedCoin: setSelectedCurrency,
		setCurrentStep,
	} = useWithdrawalFiatStore();

	const { selectedFiat } = useFiatSelectionStore(); // Get selected fiat from store
	const [localSelectedFiat, setLocalSelectedFiat] = useState<IFiatWallet | null>(null);

	const [selectedWithdrawFiatMethod, setSelectedWithdrawFiatMethod] =
		useState<IOption<IFiatWithdrawalMethod> | null>(null);

	// Auto-select the fiat from the store when component mounts or selectedFiat changes
	useEffect(() => {
		if (selectedFiat) {
			setLocalSelectedFiat(selectedFiat);
			setSelectedCurrency(selectedFiat);
			// Reset withdrawal method selection when fiat changes
			setSelectedWithdrawFiatMethod(null);
		}
	}, [selectedFiat, setSelectedCurrency]);

	const {
		data: withdrawFiatMethods = [],
		isLoading: isLoadingCryptoReceiveMethod,
	} = useGetFiatWalletWithdrawalMethod(localSelectedFiat?.currency ?? "");

	const { data: fiatWallets = [], isLoading: fiatLoading } =
		useFiatUserWallets();

	const fiatOptions: IOption<IFiatWallet>[] = fiatWallets.map((wallet) => ({
		id: `fiat-${wallet.currency}`,
		value: wallet.currency,
		label: wallet.currency,
		icon: wallet.image,
		raw: wallet,
	}));

	const withrawFiatMethodOptions: IOption<IFiatWithdrawalMethod>[] =
		withdrawFiatMethods?.map(generateOption).filter((x) => !x.hidden) || [];

	const selectedFiatOption =
		fiatOptions.find((option) => option.value === localSelectedFiat?.currency) ||
		null;

	function generateOption(
		item: IFiatWithdrawalMethod,
	): IOption<IFiatWithdrawalMethod> {
		if (item.name.includes("INTERNAL_TRANSFER")) {
			return {
				label: "Clyp P2P Transfer",
				icon: <ArrowDataTransferHorizontalIcon />,
				value: "INTERNAL_TRANSFER",
				raw: item as IFiatWithdrawalMethod,
			};
		} else if (item.name.includes("INTERNAL TRANSFER")) {
			return {
				label: "Clyp Internal Transfer",
				icon: <ArrowDataTransferHorizontalIcon />,
				value: "INTERNAL_TRANSFER",
				raw: item as IFiatWithdrawalMethod,
			};
		} else if (item.name.includes("WITHDRAWAL")) {
			return {
				label: "Withdraw to Bank",
				icon: <BankIcon />,
				value: "WITHDRAWAL",
				raw: item as IFiatWithdrawalMethod,
			};
		} else if (item.name.includes("INSTANT SWAP DEBIT")) {
			return {
				label: "Instant Swap Debit",
				icon: <FlashIcon />,
				value: "SWAP",
				hidden: true,
				raw: item as IFiatWithdrawalMethod,
			};
		} else
			return {
				label: "-",
				value: "-",
				hidden: true,
			};
	}

	const handleFiatChange = (option: IOption<IFiatWallet> | null) => {
		if (option && option.raw) {
			setLocalSelectedFiat(option.raw);
			setSelectedWithdrawFiatMethod(null);
		} else {
			setLocalSelectedFiat(null);
			setSelectedWithdrawFiatMethod(null);
		}
	};

	const handleWithdrawFiatMethodChange = (
		option: IOption<IFiatWithdrawalMethod> | null,
	) => {
		if (option && option.raw) {
			setSelectedWithdrawMethod(option);
			if (localSelectedFiat) {
				setSelectedCurrency(localSelectedFiat);
			}
			setSelectedWithdrawFiatMethod(option);

			if (
				option.raw.name.includes("INTERNAL_TRANSFER") ||
				option.raw.name.includes("INTERNAL TRANSFER")
			) {
				setCurrentStep("internalTransfer");
			} else if (option.raw.name.includes("WITHDRAWAL")) {
				setCurrentStep("bankWithdrawal");
			}
		}
	};

	const handleAddNew = () => {};

	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	return (
		<div className="p-6 space-y-6">
			{/* Coin selection */}
			<div className="relative">
				<EnhancedSelect<IFiatWallet>
					header={walletHeader}
					options={fiatOptions}
					value={selectedFiatOption}
					onChange={handleFiatChange}
					placeholder="Choose Fiat Account"
					isLoading={fiatLoading}
					className="w-full mb-6"
					displayClassName="p-4"
					renderSelected={(option) => (
						<div className="flex items-center gap-2">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<span>{option.value}</span>
						</div>
					)}
					renderOption={(option) => (
						<div className="flex items-center gap-2 w-full">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<div className="flex flex-col">
								<span>{option.label}</span>
								<span className="text-xs text-gray-500 group-hover:text-white">
									Balance:{" "}
									{formatAmount(
										option.raw?.available_balance,
										option.value,
									)}
								</span>
							</div>
						</div>
					)}
				/>
			</div>

			{/* Receive method selection */}
			<div className="relative">
				<EnhancedSelect<IFiatWithdrawalMethod>
					options={withrawFiatMethodOptions}
					value={selectedWithdrawFiatMethod}
					onChange={handleWithdrawFiatMethodChange}
					placeholder="Choose Receive Method"
					isLoading={isLoadingCryptoReceiveMethod}
					className="w-full mb-6"
					displayClassName="p-4"
					isSearchable={false}
					disabled={!localSelectedFiat}
					renderSelected={(option) => (
						<div className="flex items-center gap-2">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<span>{option.label}</span>
						</div>
					)}
					renderOption={(option) => (
						<div className="flex items-center gap-2 w-full">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<div className="flex flex-col">
								<span>{option.label}</span>
							</div>
						</div>
					)}
				/>
			</div>
		</div>
	);
};

export default SelectWithdrawalMethod;

const ArrowDataTransferHorizontalIcon = (
	props: React.SVGProps<SVGSVGElement>,
) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M19 9H6.65856C5.65277 9 5.14987 9 5.02472 8.69134C4.89957 8.38268 5.25517 8.01942 5.96637 7.29289L8.21091 5"
			stroke="#000000"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		></path>
		<path
			d="M5 15H17.3414C18.3472 15 18.8501 15 18.9753 15.3087C19.1004 15.6173 18.7448 15.9806 18.0336 16.7071L15.7891 19"
			stroke="#000000"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		></path>
	</svg>
);

export const BankIcon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M2 8.56907C2 7.37289 2.48238 6.63982 3.48063 6.08428L7.58987 3.79744C9.7431 2.59915 10.8197 2 12 2C13.1803 2 14.2569 2.59915 16.4101 3.79744L20.5194 6.08428C21.5176 6.63982 22 7.3729 22 8.56907C22 8.89343 22 9.05561 21.9646 9.18894C21.7785 9.88945 21.1437 10 20.5307 10H3.46928C2.85627 10 2.22152 9.88944 2.03542 9.18894C2 9.05561 2 8.89343 2 8.56907Z"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
		<path
			d="M11.9959 7H12.0049"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M4 10V18.5M8 10V18.5"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
		<path
			d="M16 10V18.5M20 10V18.5"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
		<path
			d="M19 18.5H5C3.34315 18.5 2 19.8431 2 21.5C2 21.7761 2.22386 22 2.5 22H21.5C21.7761 22 22 21.7761 22 21.5C22 19.8431 20.6569 18.5 19 18.5Z"
			stroke="currentColor"
			strokeWidth="1.5"
		/>
	</svg>
);

const FlashIcon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M5.22576 11.3294L12.224 2.34651C12.7713 1.64397 13.7972 2.08124 13.7972 3.01707V9.96994C13.7972 10.5305 14.1995 10.985 14.6958 10.985H18.0996C18.8729 10.985 19.2851 12.0149 18.7742 12.6706L11.776 21.6535C11.2287 22.356 10.2028 21.9188 10.2028 20.9829V14.0301C10.2028 13.4695 9.80048 13.015 9.3042 13.015H5.90035C5.12711 13.015 4.71494 11.9851 5.22576 11.3294Z"
			stroke="#000000"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		></path>
	</svg>
);