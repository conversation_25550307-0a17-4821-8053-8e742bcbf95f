import {  useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { X } from "lucide-react";
import { useWithdrawSavingsGoalStore } from "@/store/useWithdrawGoalStore";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { WithdrawSavingsAmountForm } from "./withdraw-savings-amount-form";
import { EnterTransactionPin } from "../fund/enter-transaction-pin";
import { WithdrawSavingsSuccess } from "./withdraw-savings-success";
import { SavingsDetails } from "@/types/fiat-savings";
import { IOption } from "@/components/enhanced-select";
import { IFiatWallet } from "@/types/fiat-wallets";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { WithdrawCryptoSavingsPayload } from "@/types/crypto-savings";
import { useWithdrawFiatSavings } from "@/hooks/api/fiat-savings";
import {useWithdrawCryptoSavings} from "@/hooks/api/crypto-savings";

interface WithdrawSavingsGoalFlowProps {
  goal: SavingsDetails;
}

export const WithdrawSavingsGoalFlow = ({ goal }: WithdrawSavingsGoalFlowProps) => {
  const { closeDrawer } = useDrawer();
  const { currentStep, amount, fundingSource, selectedAccount, setStep, reset } = useWithdrawSavingsGoalStore();
  
  
  // Account data
  const { data: fiatWallets = [] } = useFiatUserWallets();
  const { data: cryptoWallets = [] } = useCryptoUserWallets();

  // Transform wallet data to options
  const fiatAccounts: IOption<string>[] = fiatWallets.map((wallet: IFiatWallet) => ({
    id: wallet.id,
    label: `${wallet.currency} - ${wallet.available_balance}`,
    value: wallet.id,
  }));

  const cryptoAccounts: IOption<string>[] = cryptoWallets.map((wallet: ICryptoWallet) => ({
    id: wallet.id,
    label: `${wallet.currency} - ${wallet.balance}`,
    value: wallet.id,
  }));

  const handleClose = () => {
    reset();
    closeDrawer();
  };

    const withdrawCryptoMutation = useWithdrawCryptoSavings();
    const withdrawFiatMutation = useWithdrawFiatSavings();

  const handlePinSubmit = (pin: string) => {
    if (!amount || !fundingSource || !selectedAccount) return;

    const withdrawalPayload: WithdrawCryptoSavingsPayload = {
      user_id: goal.user_id,
      savingsGoalId: goal.id,
      amount: amount,
     
    };

    // Choose the right mutation based on funding source
    if (fundingSource === "crypto") {
      withdrawCryptoMutation.mutate(withdrawalPayload);
    } else {
      withdrawFiatMutation.mutate(withdrawalPayload);
    }
  };

  // Handle mutation success
  useEffect(() => {
    if (withdrawCryptoMutation.isSuccess || withdrawFiatMutation.isSuccess) {
      setStep("success");
    }
  }, [withdrawCryptoMutation.isSuccess, withdrawFiatMutation.isSuccess, setStep]);

  // Handle mutation errors
  useEffect(() => {
    if (withdrawCryptoMutation.isError || withdrawFiatMutation.isError) {
      // Error handling is done in the mutation hooks via notify
      // You could add additional error handling here if needed
    }
  }, [withdrawCryptoMutation.isError, withdrawFiatMutation.isError]);

  // Animation variants
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? "100%" : "-100%",
      opacity: 0,
    }),
    center: {
      x: "0%",
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? "100%" : "-100%",
      opacity: 0,
    }),
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h1 className="text-lg font-semibold text-foreground">Withdraw from Savings Goal</h1>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleClose}
          className="h-8 w-8 rounded-full"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 relative overflow-hidden">
        <AnimatePresence mode="wait" custom={0}>
          {currentStep === "amount" && (
            <motion.div
              key="amount"
              custom={0}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="absolute inset-0 w-full h-full"
            >
              <WithdrawSavingsAmountForm
                goal={goal}
                fiatAccounts={fiatAccounts}
                cryptoAccounts={cryptoAccounts}
              />
            </motion.div>
          )}

          {currentStep === "pin" && (
            <motion.div
              key="pin"
              custom={1}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="absolute inset-0 w-full h-full"
            >              <EnterTransactionPin
                title="Enter Transaction PIN"
                buttonText="Withdraw from Savings Goal"
                onComplete={handlePinSubmit}
                onBack={() => setStep("amount")}
                loading={withdrawCryptoMutation.isPending || withdrawFiatMutation.isPending}
                error={
                  withdrawCryptoMutation.error?.message || 
                  withdrawFiatMutation.error?.message
                }
              />
            </motion.div>
          )}

          {currentStep === "success" && (
            <motion.div
              key="success"
              custom={1}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="absolute inset-0 w-full h-full"
            >
              <WithdrawSavingsSuccess
                amount={amount || 0}
                currency={goal.currency}
                goalName={goal.title}
                onViewReceipt={() => setStep("receipt")}
                onClose={handleClose}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
