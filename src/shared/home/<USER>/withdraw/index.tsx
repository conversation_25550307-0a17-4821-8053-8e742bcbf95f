"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { X } from "lucide-react";
import { Icon } from "@/components/icons";
import useWithdrawalFiatStore from "@/store/withdrawal-fiat-store";
import SelectWithdrawalMethod from "./select-withdrawal-method";
import InternalTransferMethod from "./internal-transfer";
import BankWithdrawalMethod from "./bank-withdrawal";
import PreviewWithdrawal from "./preview-withdrawal";
import WithdrawalPinManager from "./withdrawal-pin-manager";
import WithdrawalTransactionSuccess from "./transaction-success";
import { TransactionReceipt } from "./transaction-receipt";

export function WithdrawFiatFlow() {
	const { closeDrawer } = useDrawer();

	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);

	const { currentStep, previousStep, setCurrentStep } =
		useWithdrawalFiatStore();

	const handleClose = () => {
		closeDrawer();

		setTimeout(() => {
			useWithdrawalFiatStore.getState().reset();
			setDirection("forward");
		}, 300);
	};

	const handleStepChange = (
		step: typeof currentStep,
		dir: "forward" | "backward" = "forward",
	) => {
		setDirection(dir);
		setCurrentStep(step);
	};

	const handleBack = () => {
		switch (currentStep) {
			case "internalTransfer":
			case "bankWithdrawal":
				handleStepChange("select", "backward");
				break;
			case "previewWithdrawal":
				if (
					previousStep === "internalTransfer" ||
					previousStep === "bankWithdrawal"
				) {
					handleStepChange(previousStep, "backward");
				} else {
					handleStepChange("select", "backward");
				}
				break;
			case "pin":
				handleStepChange("previewWithdrawal", "backward");
				break;
			default:
				break;
		}
	};

	const getStepTitle = () => {
		switch (currentStep) {
			case "select":
				return "Withdraw Fiat";
			case "internalTransfer":
				return "Clyp P2P Transfer";
			case "bankWithdrawal":
				return "Bank Withdrawal";
			case "previewWithdrawal":
				return "Preview Withdrawal";
			case "pin":
				return null;
			case "success":
				return null;
			default:
				return "";
		}
	};

	const showBackButton = [
		"internalTransfer",
		"bankWithdrawal",
		"previewWithdrawal",
		"pin",
	].includes(currentStep);

	const horizontalVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex flex-col mt-10 relative px-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>
				{(getStepTitle() || showBackButton) && (
					<div className="flex items-center justify-center relative">
						{showBackButton && (
							<span
								onClick={handleBack}
								className="absolute left-0 bg-primary-light text-primary rounded-full p-2 cursor-pointer"
							>
								<Icon name="arrow-left" className="size-6" />
							</span>
						)}
						{getStepTitle() && (
							<h2 className="text-2xl font-semibold w-full text-center">
								{getStepTitle()}
							</h2>
						)}
					</div>
				)}
			</div>

			<div className="p-0 h-full overflow-hidden relative flex-grow">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{currentStep === "select" && (
						<motion.div
							key="select"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<SelectWithdrawalMethod />
						</motion.div>
					)}

					{currentStep === "internalTransfer" && (
						<motion.div
							key="internalTransfer"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<InternalTransferMethod />
						</motion.div>
					)}

					{currentStep === "bankWithdrawal" && (
						<motion.div
							key="bankWithdrawal"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<BankWithdrawalMethod />
						</motion.div>
					)}

					{currentStep === "previewWithdrawal" && (
						<motion.div
							key="previewWithdrawal"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<PreviewWithdrawal />
						</motion.div>
					)}

					{currentStep === "pin" && (
						<motion.div
							key="pin"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							{/* <PinManager onComplete={handleBuyCrypto} /> */}
							<WithdrawalPinManager />
						</motion.div>
					)}

					{currentStep === "success" && (
						<motion.div
							key="success"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<WithdrawalTransactionSuccess />
						</motion.div>
					)}

					{currentStep === "receipt" && (
						<motion.div
							key="receipt"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<TransactionReceipt />
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}
