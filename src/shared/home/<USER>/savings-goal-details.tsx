import { format } from "date-fns";
import { SavingsDetails } from "@/types/fiat-savings";
import { Button } from "@/components/custom/button";
import useUserStore from "@/store/user-store";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { FundSavingsGoalFlow } from "./fund/index.tsx";
import { WithdrawSavingsGoalFlow } from "./withdraw/index.tsx";
import TransactionItem from "../transaction-item";
import { GenericList } from "@/components/generic-list";
import { useEffect, useState } from "react";

interface SavingsHistoryEntry {
    id: number;
    createdAt: string;
    account: string;
    title: string;
    amount: number;
    currency: string;
    status: "Success" | "Pending" | "Failed";
}

interface SavingsGoalDetailsProps {
    goal: SavingsDetails;
    onBack?: () => void;
}

export const SavingsGoalDetails = ({ goal, onBack }: SavingsGoalDetailsProps) => {
    const { user } = useUserStore();
    const { openDrawer } = useDrawer();
    const progress = ((goal.amountSaved / goal.targetAmount) * 100 + 3);
    const [isLoading, setIsLoading] = useState(true);
    
    const [history, setHistory] = useState<SavingsHistoryEntry[]>([]);
    
    useEffect(() => {
       
        const timer = setTimeout(() => {
            const sampleHistory: SavingsHistoryEntry[] = [
                {
                    id: 1,
                    createdAt: new Date().toISOString(),
                    account: goal.accountName || "Main Account",
                    title: "Initial funding",
                    amount: goal.amountSaved * 0.4,
                    currency: goal.currency,
                    status: "Success"
                },
                {
                    id: 2,
                    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), 
                    account: goal.accountName || "Main Account",
                    title: "Regular contribution",
                    amount: goal.amountSaved * 0.6,
                    currency: goal.currency,
                    status: "Success"
                }
            ];
            
            setHistory(sampleHistory);
            setIsLoading(false);
        }, 1000);
        
        return () => clearTimeout(timer);
    }, [goal]);    const transactionItems = history.map((transaction) => ({
        id: transaction.id,
        type: transaction.title,
        time: format(new Date(transaction.createdAt), "hh:mm a"),
        date: format(new Date(transaction.createdAt), "MM/dd/yyyy"),
        account: transaction.account,
        amount: `${transaction.currency} ${transaction.amount.toFixed(2)}`,
        status: (
            transaction.status === "Success"
                ? "Successful"
                : transaction.status === "Pending"
                ? "Pending"
                : transaction.status === "Failed"
                ? "Failed"
                : "Processing"
        ) as "Successful" | "Failed" | "Processing" | "Pending"
    }));

    const handleFundGoal = () => {
        openDrawer({
            view: <FundSavingsGoalFlow goal={goal} />,
            placement: "right",
            customSize: "480px"
        });
    };

    const handleWithdrawGoal = () => {
        openDrawer({
            view: <WithdrawSavingsGoalFlow goal={goal} />,
            placement: "right",
            customSize: "480px"
        });
    };

  

    return (
        <div className="max-w-5xl mx-auto px-8 py-8">
            <div className="flex items-center mb-6">
                <button onClick={onBack} className="text-primary text-4xl hover:underline mr-4">
                    &larr;
                </button>
                <h1 className="text-2xl font-semibold">{goal.title}</h1>
            </div>

            <div className=" gap-8 mb-8">
              
                <div>
                    <div className="mb-6 bg-card rounded-xl p-6 shadow-md border">
                        <h3 className="text-lg font-semibold mb-4">Goal Amount</h3>
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600 text-sm">Amount Saved</span>
                            <span className="text-gray-600 text-sm">Target Amount</span>
                        </div>
                        <div className="flex justify-between items-center mb-2">
                            <span className="font-bold text-lg text-primary">
                                {goal.amountSaved ? goal.amountSaved : 0} {goal.currency}
                            </span>
                            <span className="font-bold text-lg text-primary">
                                {goal.targetAmount} {goal.currency}
                            </span>
                        </div>
                        <div className="w-full bg-[#FFE9C7] flex items-center h-4 p-1 rounded-full  mb-4">
                            <div
                                className="bg-[#EA9924] h-3 border border-orange-400 rounded-full"
                                style={{ width: `${progress}%` }}
                            ></div>
                        </div>                        <div className="flex gap-4">
                            {goal.amountSaved < goal.targetAmount ? (
                                <Button
                                    onClick={handleFundGoal}
                                    className="bg-[#EA9924] cursor-pointer text-white rounded-full px-8 py-3 w-fit">
                                    Fund Savings Goal
                                </Button>
                            ) : (
                                <Button
                                    onClick={handleWithdrawGoal}
                                    className="bg-primary cursor-pointer text-white rounded-full px-8 py-3 w-fit">
                                    Withdraw from Goal
                                </Button>
                            )}
                        </div>
                    </div>
                </div>

                {/* Right: Details */}
                <div>
                    <div className="bg-card rounded-xl p-6 border shadow-md">
                        <h3 className="text-lg font-semibold mb-4">Details</h3>
                        <div className="grid grid-cols-2 gap-y-3 gap-x-4">
                            <span className="text-gray-500 font-medium">Goal Savings Title</span>
                            <span className="font-semibold">{goal.title}</span>

                            <span className="text-gray-500 font-medium">Currency</span>
                            <span className="font-semibold">{goal.currency}</span>

                            <span className="text-gray-500 font-medium">Start Date</span>
                            <span className="font-semibold">
                                {format(new Date(goal.startDate), "MM/dd/yyyy")}
                            </span>

                            <span className="text-gray-500 font-medium">End Date</span>
                            <span className="font-semibold">
                                {format(new Date(goal.endDate), "MM/dd/yyyy")}
                            </span>

                            <span className="text-gray-500 font-medium">Funding Source</span>
                            <span className="font-semibold">
                                {goal.fundingSource || "Fiat Account"}
                            </span>

                            <span className="text-gray-500 font-medium"> Account Name</span>
                            <span className="font-semibold">
                                {goal.accountName || "American Dollar"}
                            </span>
                        </div>
                    </div>
                </div>
            </div>            {/* Goal History */}
            <div className="bg-card rounded-xl p-6 shadow-sm border mt-8">
                <h3 className="text-lg font-semibold mb-4">Goal History</h3>
                
                {isLoading ? (
                    <div className="py-8 flex justify-center">
                        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                    </div>
                ) : (
                    <GenericList
                        items={transactionItems}
                        renderItem={(transaction) => (
                            <TransactionItem transaction={transaction} />
                        )}
                        spacing="normal"
                        emptyMessage={
                            <div className="mt-8 text-center">
                                <p className="text-gray-500">
                                    Hi {user?.first_name || user?.username || 'User'}, you currently do not have any history. Start funding your savings goal today.
                                </p>
                            </div>
                        }
                        className="rounded-lg space-y-6"
                    />
                )}
                
                {!isLoading && transactionItems.length > 0 && (
                    <div className="flex justify-start mt-6">
                        <button className="bg-gray-900 text-white rounded-full px-8 py-3 cursor-pointer font-semibold shadow hover:bg-gray-800 transition-all">
                            See all
                        </button>
                    </div>
                )}
            </div>

        
        </div>
    );
};