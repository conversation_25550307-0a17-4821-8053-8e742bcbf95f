import { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/custom/button";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { X } from "lucide-react";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { useFundCard } from "@/hooks/api/cards";

const fundCardSchema = z.object({
	amount: z.string().min(1, "Please enter an amount"),
});

type FundCardData = z.infer<typeof fundCardSchema>;

interface FundCardProps {
	onClose: () => void;
	cardId: string;
}

export const FundCard = ({ onClose, cardId }: FundCardProps) => {
	const [step, setStep] = useState<"form" | "pin" | "success">("form");
	const [pin, setPin] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const { mutateAsync: fundCardMutation } = useFundCard();
	const form = useForm<FundCardData>({
		resolver: zodResolver(fundCardSchema),
		defaultValues: { amount: "" },
	});

	const handleFormSubmit = (data: FundCardData) => {
		form.setValue("amount", data.amount); 
		setStep("pin");
	};

	const handlePinSubmit = async () => {
		if (pin.length !== 4) return;
		setIsLoading(true);
		try {
			await fundCardMutation({
				amount: form.getValues().amount,
				card_id: cardId,
			});
			setStep("success");
		} finally {
			setIsLoading(false);
		}
	};

	if (step === "success") {
		return (
			<div className="flex flex-col h-full p-6">
				<TransactionSuccess
					title="Card Funded!"
					message={`Your card has been funded with ₦${
						form.getValues().amount
					}.`}
					buttonText="Done"
					onNextAction={onClose}
				/>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full p-6">
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h2 className="text-xl font-bold text-foreground">
						Fund Card
					</h2>
					<p className="text-sm text-muted-foreground mt-1">
						Enter the amount you want to fund your card with
					</p>
				</div>
				<button
					onClick={onClose}
					className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
				>
					<X className="w-5 h-5" />
				</button>
			</div>

			{step === "form" && (
				<form
					onSubmit={form.handleSubmit(handleFormSubmit)}
					className="space-y-6 flex-1"
				>
					<Controller
						name="amount"
						control={form.control}
						render={({ field, fieldState: { error } }) => (
							<div>
								<input
									{...field}
									type="number"
									placeholder="Enter amount"
									className="w-full h-[48px] rounded-full bg-background dark:bg-input text-base text-foreground border border-border px-4"
								/>
								{error?.message && (
									<p className="text-red-500 text-sm mt-1">
										{error.message}
									</p>
								)}
							</div>
						)}
					/>
					<div className="flex gap-3 pt-4">
						<Button
							type="button"
							variant="secondary"
							onClick={onClose}
							className="flex-1"
						>
							Cancel
						</Button>
						<Button
							type="submit"
							disabled={isLoading}
							className="flex-1"
						>
							Continue
						</Button>
					</div>
				</form>
			)}

			{step === "pin" && (
				<div className="flex flex-col flex-1 justify-between">
					<div className="flex-1 flex flex-col items-center justify-center gap-6">
						<div className="text-center">
							<p className="text-gray-600 dark:text-gray-400">
								Enter your 4-digit transaction PIN to confirm
								funding
							</p>
						</div>
						<div className="flex justify-center">
							<InputOTP
								pattern={REGEXP_ONLY_DIGITS}
								value={pin}
								onChange={setPin}
								onComplete={handlePinSubmit}
								maxLength={4}
								autoFocus
								containerClassName="gap-3"
							>
								<InputOTPGroup>
									<InputOTPSlot
										index={0}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={1}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={2}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={3}
										className="size-16 text-lg border border-border rounded-md"
									/>
								</InputOTPGroup>
							</InputOTP>
						</div>
					</div>
					<div className="w-full space-y-4 pt-4">
						<Button
							onClick={handlePinSubmit}
							disabled={pin.length !== 4 || isLoading}
							className="w-full py-6 rounded-full bg-primary text-primary-foreground font-medium"
						>
							{isLoading ? "Funding..." : "Confirm Funding"}
						</Button>
						<Button
							type="button"
							variant="secondary"
							onClick={() => setStep("form")}
							className="w-full py-6 rounded-full"
						>
							Back
						</Button>
					</div>
				</div>
			)}
		</div>
	);
};

export default FundCard;
