import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/custom/button";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { X, AlertTriangle } from "lucide-react";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { useFreezeOrUnfreezeCard } from "@/hooks/api/cards";
import { EnhancedSelect, IOption } from "@/components/enhanced-select"; // Added import

interface BlockCardProps {
	onClose: () => void;
	cardId: string;
}

const actionOptions: IOption<"block" | "unblock">[] = [
	{ label: "Block Card", value: "block", id: "block" },
	{ label: "Unblock Card", value: "unblock", id: "unblock" },
];

export const BlockCard = ({ onClose, cardId }: BlockCardProps) => {
	const [step, setStep] = useState<"action" | "pin" | "success">("action");
	const [selectedAction, setSelectedAction] = useState<"block" | "unblock">(
		"block",
	);
	const [pin, setPin] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const { mutateAsync: freezeOrUnfreezeCardMutation } =
		useFreezeOrUnfreezeCard();

	const handleActionSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		setStep("pin");
	};

	const handlePinSubmit = async () => {
		if (pin.length !== 4) return;
		setIsLoading(true);
		try {
			await freezeOrUnfreezeCardMutation({
				card_id: cardId,
				type:
					selectedAction === "block"
						? "FREEZE CARD"
						: "UNFREEZE CARD",
			});
			setStep("success");
		} catch (error) {
			console.error("Failed to update card status:", error);
		} finally {
			setIsLoading(false);
		}
	};

	if (step === "success") {
		return (
			<div className="flex flex-col h-full p-6">
				<TransactionSuccess
					title={
						selectedAction === "block"
							? "Card Blocked Successfully!"
							: "Card Unblocked Successfully!"
					}
					message={
						selectedAction === "block"
							? "Your card has been permanently blocked. You can order a new card from the cards section."
							: "Your card has been successfully unblocked."
					}
					buttonText="Done"
					onNextAction={onClose}
				/>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full p-6">
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h2 className="text-xl font-bold text-foreground">
						{selectedAction === "block"
							? "Block Card"
							: "Unblock Card"}
					</h2>
					<p className="text-sm text-muted-foreground mt-1">
						{selectedAction === "block"
							? "Permanently block this card"
							: "Reactivate this card"}
					</p>
				</div>
				<button
					onClick={onClose}
					className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
				>
					<X className="w-5 h-5" />
				</button>
			</div>

			{step === "action" && (
				<form
					onSubmit={handleActionSubmit}
					className="space-y-8 flex-1 flex flex-col justify-between"
				>
					<div className="space-y-6">
						<div>
							<label
								// htmlFor="cardAction" // EnhancedSelect doesn't use a native input id directly
								className="block text-sm font-medium text-muted-foreground mb-1"
							>
								Select Action
							</label>
							<EnhancedSelect<"block" | "unblock">
								className="w-full"
								options={actionOptions}
								value={
									actionOptions.find(
										(option) =>
											option.value === selectedAction,
									)!
								}
								onChange={(option) => {
									if (option) {
										setSelectedAction(option.value);
									}
								}}
								isSearchable={false}
								isClearable={false}
								displayClassName="h-[48px] px-4 !py-0 items-center text-base bg-background dark:bg-input border-border rounded-full"
							/>
						</div>

						{selectedAction === "block" && (
							<div className="bg-primary/10 border border-primary/30 rounded-lg p-4 dark:bg-primary/20 dark:border-primary/40">
								<div className="flex items-start space-x-3">
									<AlertTriangle className="w-5 h-5 text-primary mt-0.5" />
									<div>
										<h3 className="text-sm font-medium text-primary dark:text-primary">
											Warning: This action cannot be
											undone
										</h3>
										<p className="text-sm text-primary/80 dark:text-primary/70 mt-1">
											Once blocked, this card cannot be
											reactivated. You'll need to order a
											new card.
										</p>
									</div>
								</div>
							</div>
						)}
					</div>
					<div className="flex justify-center pt-4 gap-3">
						<Button
							type="button"
							variant="secondary"
							onClick={onClose}
							className="flex-1"
						>
							Cancel
						</Button>
						<Button
							type="submit"
							className="flex-1 bg-primary cursor-pointer hover:bg-primary/90 text-white"
						>
							Continue
						</Button>
					</div>
				</form>
			)}

			{step === "pin" && (
				<div className="flex flex-col flex-1 justify-between">
					<div className="flex-1 flex flex-col items-center justify-center gap-6">
						<div className="text-center">
							<p className="text-gray-600 dark:text-gray-400">
								Enter your 4-digit transaction PIN to confirm
								blocking your card
							</p>
						</div>
						<div className="flex justify-center">
							<InputOTP
								pattern={REGEXP_ONLY_DIGITS}
								value={pin}
								onChange={setPin}
								onComplete={handlePinSubmit}
								maxLength={4}
								autoFocus
								containerClassName="gap-3"
							>
								<InputOTPGroup>
									<InputOTPSlot
										index={0}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={1}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={2}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={3}
										className="size-16 text-lg border border-border rounded-md"
									/>
								</InputOTPGroup>
							</InputOTP>
						</div>
					</div>
					<div className="w-full space-y-4 pt-4">
						<Button
							onClick={handlePinSubmit}
							disabled={pin.length !== 4 || isLoading}
							className="w-full py-6 rounded-full bg-primary text-primary-foreground font-medium"
						>
							{isLoading ? "Blocking Card..." : "Confirm Block"}
						</Button>
						<Button
							type="button"
							variant="secondary"
							onClick={() => setStep("action")}
							className="w-full py-6 rounded-full"
						>
							Back
						</Button>
					</div>
				</div>
			)}
		</div>
	);
};

export default BlockCard;
