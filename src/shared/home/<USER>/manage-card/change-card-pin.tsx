import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/custom/button";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { Eye, EyeOff, X } from "lucide-react";

// Define the steps
type Step = "current-pin" | "new-pin" | "confirm-pin" | "otp" | "success";

// Validation schemas
const currentPinSchema = z.object({
	currentPin: z
		.string()
		.min(4, "PIN must be 4 digits")
		.max(4, "PIN must be 4 digits"),
});

const newPinSchema = z.object({
	newPin: z
		.string()
		.min(4, "PIN must be 4 digits")
		.max(4, "PIN must be 4 digits"),
});

const confirmPinSchema = z.object({
	confirmPin: z
		.string()
		.min(4, "PIN must be 4 digits")
		.max(4, "PIN must be 4 digits"),
});

const otpSchema = z.object({
	otp: z
		.string()
		.min(4, "OTP must be 4 digits")
		.max(4, "OTP must be 4 digits"),
});

type CurrentPinData = z.infer<typeof currentPinSchema>;
type NewPinData = z.infer<typeof newPinSchema>;
type ConfirmPinData = z.infer<typeof confirmPinSchema>;
type OtpData = z.infer<typeof otpSchema>;

interface ChangeCardPinProps {
	onClose: () => void;
}

export const ChangeCardPin = ({ onClose }: ChangeCardPinProps) => {
	const [currentStep, setCurrentStep] = useState<Step>("current-pin");
	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);
	const [showCurrentPin, setShowCurrentPin] = useState(false);
	const [showNewPin, setShowNewPin] = useState(false);
	const [showConfirmPin, setShowConfirmPin] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	// Store pin data across steps
	const [pinData, setPinData] = useState({
		currentPin: "",
		newPin: "",
		confirmPin: "",
	});

	// Form configurations for each step
	const currentPinForm = useForm<CurrentPinData>({
		resolver: zodResolver(currentPinSchema),
		defaultValues: { currentPin: "" },
	});

	const newPinForm = useForm<NewPinData>({
		resolver: zodResolver(newPinSchema),
		defaultValues: { newPin: "" },
	});

	const confirmPinForm = useForm<ConfirmPinData>({
		resolver: zodResolver(confirmPinSchema),
		defaultValues: { confirmPin: "" },
	});

	const otpForm = useForm<OtpData>({
		resolver: zodResolver(otpSchema),
		defaultValues: { otp: "" },
	});

	// Navigation functions
	const goToNextStep = () => {
		setDirection("forward");
		switch (currentStep) {
			case "current-pin":
				setCurrentStep("new-pin");
				break;
			case "new-pin":
				setCurrentStep("confirm-pin");
				break;
			case "confirm-pin":
				setCurrentStep("otp");
				break;
			case "otp":
				setCurrentStep("success");
				break;
		}
	};

	const goToPrevStep = () => {
		setDirection("backward");
		switch (currentStep) {
			case "new-pin":
				setCurrentStep("current-pin");
				break;
			case "confirm-pin":
				setCurrentStep("new-pin");
				break;
			case "otp":
				setCurrentStep("confirm-pin");
				break;
		}
	};

	// Step handlers
	const handleCurrentPinSubmit = async (data: CurrentPinData) => {
		setIsLoading(true);
		try {
			// Simulate API call to verify current PIN
			await new Promise((resolve) => setTimeout(resolve, 1000));
			setPinData((prev) => ({ ...prev, currentPin: data.currentPin }));
			goToNextStep();
		} catch (error) {
			console.error("Failed to verify current PIN:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const handleNewPinSubmit = async (data: NewPinData) => {
		setPinData((prev) => ({ ...prev, newPin: data.newPin }));
		goToNextStep();
	};

	const handleConfirmPinSubmit = async (data: ConfirmPinData) => {
		if (data.confirmPin !== pinData.newPin) {
			confirmPinForm.setError("confirmPin", {
				type: "manual",
				message: "PINs do not match",
			});
			return;
		}
		setPinData((prev) => ({ ...prev, confirmPin: data.confirmPin }));
		goToNextStep();
	};

	const handleOtpSubmit = async () => {
		setIsLoading(true);
		try {
			// Simulate API call to verify OTP and change PIN
			await new Promise((resolve) => setTimeout(resolve, 2000));
			goToNextStep();
		} catch (error) {
			console.error("Failed to verify OTP:", error);
		} finally {
			setIsLoading(false);
		}
	};

	// Animation variants
	const slideVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	const getStepTitle = () => {
		switch (currentStep) {
			case "current-pin":
				return "Enter Current PIN";
			case "new-pin":
				return "Enter New PIN";
			case "confirm-pin":
				return "Confirm New PIN";
			case "otp":
				return "Verify OTP";
			case "success":
				return "PIN Changed Successfully";
			default:
				return "";
		}
	};

	const getStepDescription = () => {
		switch (currentStep) {
			case "current-pin":
				return "Please enter your current card PIN to continue";
			case "new-pin":
				return "Create a new 4-digit PIN for your card";
			case "confirm-pin":
				return "Re-enter your new PIN to confirm";
			case "otp":
				return "Enter the OTP sent to your registered phone number";
			case "success":
				return "Your card PIN has been successfully changed";
			default:
				return "";
		}
	};

	return (
		<div className="flex flex-col h-full p-6 relative overflow-hidden">
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h2 className="text-xl font-bold text-foreground">
						{getStepTitle()}
					</h2>
					<p className="text-sm text-muted-foreground mt-1">
						{getStepDescription()}
					</p>
				</div>
				<button
					onClick={onClose}
					className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
				>
					<X className="w-5 h-5" />
				</button>
			</div>

			{/* Progress indicator */}
			{currentStep !== "success" && (
				<div className="flex items-center mb-8">
					{["current-pin", "new-pin", "confirm-pin", "otp"].map(
						(step, index) => (
							<div key={step} className="flex items-center">
								<div
									className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
										[
											"current-pin",
											"new-pin",
											"confirm-pin",
											"otp",
										].indexOf(currentStep) >= index
											? "bg-primary text-white"
											: "bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400"
									}`}
								>
									{index + 1}
								</div>
								{index < 3 && (
									<div
										className={`w-12 h-1 mx-2 ${
											[
												"current-pin",
												"new-pin",
												"confirm-pin",
												"otp",
											].indexOf(currentStep) > index
												? "bg-primary"
												: "bg-gray-200 dark:bg-gray-700"
										}`}
									/>
								)}
							</div>
						),
					)}
				</div>
			)}

			{/* Step Content */}
			<div className="flex-1 relative">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{/* Current PIN Step */}
					{currentStep === "current-pin" && (
						<motion.div
							key="current-pin"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full"
						>
							<form
								onSubmit={currentPinForm.handleSubmit(
									handleCurrentPinSubmit,
								)}
								className="space-y-6"
							>
								<div className="flex flex-col items-center">
									<Controller
										name="currentPin"
										control={currentPinForm.control}
										render={({ field }) => (
											<InputOTP
												pattern={REGEXP_ONLY_DIGITS}
												maxLength={4}
												value={field.value}
												onChange={field.onChange}
												autoFocus
												containerClassName="gap-3"
											>
												{[0, 1, 2, 3].map((index) => (
													<InputOTPGroup key={index}>
														<InputOTPSlot
															index={index}
															className="size-16 text-lg"
															mask={
																!showCurrentPin
															}
														/>
													</InputOTPGroup>
												))}
											</InputOTP>
										)}
									/>
									{currentPinForm.formState.errors
										.currentPin && (
										<p className="text-red-500 text-sm mt-2">
											{
												currentPinForm.formState.errors
													.currentPin.message
											}
										</p>
									)}
									<button
										type="button"
										onClick={() =>
											setShowCurrentPin(!showCurrentPin)
										}
										className="mt-3 text-primary hover:text-primary/80 text-sm font-medium flex items-center gap-2"
									>
										{showCurrentPin ? (
											<>
												<EyeOff className="w-4 h-4" />{" "}
												Hide PIN
											</>
										) : (
											<>
												<Eye className="w-4 h-4" /> Show
												PIN
											</>
										)}
									</button>
								</div>

								<div className="flex gap-3 pt-4">
									<Button
										type="button"
										variant="secondary"
										onClick={onClose}
										className="flex-1"
									>
										Cancel
									</Button>
									<Button
										type="submit"
										disabled={isLoading}
										className="flex-1"
									>
										{isLoading
											? "Verifying..."
											: "Continue"}
									</Button>
								</div>
							</form>
						</motion.div>
					)}

					{/* New PIN Step */}
					{currentStep === "new-pin" && (
						<motion.div
							key="new-pin"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full"
						>
							<form
								onSubmit={newPinForm.handleSubmit(
									handleNewPinSubmit,
								)}
								className="space-y-6"
							>
								<div className="flex flex-col items-center">
									<Controller
										name="newPin"
										control={newPinForm.control}
										render={({ field }) => (
											<InputOTP
												pattern={REGEXP_ONLY_DIGITS}
												maxLength={4}
												value={field.value}
												onChange={field.onChange}
												autoFocus
												containerClassName="gap-3"
											>
												{[0, 1, 2, 3].map((index) => (
													<InputOTPGroup key={index}>
														<InputOTPSlot
															index={index}
															className="size-16 text-lg"
															mask={!showNewPin}
														/>
													</InputOTPGroup>
												))}
											</InputOTP>
										)}
									/>
									{newPinForm.formState.errors.newPin && (
										<p className="text-red-500 text-sm mt-2">
											{
												newPinForm.formState.errors
													.newPin.message
											}
										</p>
									)}
									<button
										type="button"
										onClick={() =>
											setShowNewPin(!showNewPin)
										}
										className="mt-3 text-primary hover:text-primary/80 text-sm font-medium flex items-center gap-2"
									>
										{showNewPin ? (
											<>
												<EyeOff className="w-4 h-4" />{" "}
												Hide PIN
											</>
										) : (
											<>
												<Eye className="w-4 h-4" /> Show
												PIN
											</>
										)}
									</button>
								</div>

								<div className="flex gap-3 pt-4">
									<Button
										type="button"
										variant="secondary"
										onClick={goToPrevStep}
										className="flex-1"
									>
										Back
									</Button>
									<Button type="submit" className="flex-1">
										Continue
									</Button>
								</div>
							</form>
						</motion.div>
					)}

					{/* Confirm PIN Step */}
					{currentStep === "confirm-pin" && (
						<motion.div
							key="confirm-pin"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full"
						>
							<form
								onSubmit={confirmPinForm.handleSubmit(
									handleConfirmPinSubmit,
								)}
								className="space-y-6"
							>
								<div className="flex flex-col items-center">
									<Controller
										name="confirmPin"
										control={confirmPinForm.control}
										render={({ field }) => (
											<InputOTP
												pattern={REGEXP_ONLY_DIGITS}
												maxLength={4}
												value={field.value}
												onChange={field.onChange}
												autoFocus
												containerClassName="gap-3"
											>
												{[0, 1, 2, 3].map((index) => (
													<InputOTPGroup key={index}>
														<InputOTPSlot
															index={index}
															className="size-16 text-lg"
															mask={
																!showConfirmPin
															}
														/>
													</InputOTPGroup>
												))}
											</InputOTP>
										)}
									/>
									{confirmPinForm.formState.errors
										.confirmPin && (
										<p className="text-red-500 text-sm mt-2">
											{
												confirmPinForm.formState.errors
													.confirmPin.message
											}
										</p>
									)}
									<button
										type="button"
										onClick={() =>
											setShowConfirmPin(!showConfirmPin)
										}
										className="mt-3 text-primary hover:text-primary/80 text-sm font-medium flex items-center gap-2"
									>
										{showConfirmPin ? (
											<>
												<EyeOff className="w-4 h-4" />{" "}
												Hide PIN
											</>
										) : (
											<>
												<Eye className="w-4 h-4" /> Show
												PIN
											</>
										)}
									</button>
								</div>

								<div className="flex gap-3 pt-4">
									<Button
										type="button"
										variant="secondary"
										onClick={goToPrevStep}
										className="flex-1"
									>
										Back
									</Button>
									<Button type="submit" className="flex-1">
										Continue
									</Button>
								</div>
							</form>
						</motion.div>
					)}

					{/* OTP Verification Step */}
					{currentStep === "otp" && (
						<motion.div
							key="otp"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full"
						>
							<form
								onSubmit={otpForm.handleSubmit(() =>
									handleOtpSubmit(),
								)}
								className="space-y-6"
							>
								<div className="flex flex-col items-center">
									<Controller
										name="otp"
										control={otpForm.control}
										render={({ field }) => (
											<InputOTP
												pattern={REGEXP_ONLY_DIGITS}
												maxLength={4}
												value={field.value}
												onChange={field.onChange}
												onComplete={() =>
													otpForm.handleSubmit(() =>
														handleOtpSubmit(),
													)()
												}
												autoFocus
												containerClassName="gap-3"
											>
												{[0, 1, 2, 3].map((index) => (
													<InputOTPGroup key={index}>
														<InputOTPSlot
															index={index}
															className="size-16 text-lg"
														/>
													</InputOTPGroup>
												))}
											</InputOTP>
										)}
									/>
									{otpForm.formState.errors.otp && (
										<p className="text-red-500 text-sm mt-2">
											{
												otpForm.formState.errors.otp
													.message
											}
										</p>
									)}
								</div>

								<div className="text-center">
									<button
										type="button"
										className="text-primary hover:text-primary/80 text-sm font-medium"
									>
										Resend OTP
									</button>
								</div>

								<div className="flex gap-3 pt-4">
									<Button
										type="button"
										variant="secondary"
										onClick={goToPrevStep}
										className="flex-1"
									>
										Back
									</Button>
									<Button
										type="submit"
										disabled={isLoading}
										className="flex-1"
									>
										{isLoading
											? "Verifying..."
											: "Verify OTP"}
									</Button>
								</div>
							</form>
						</motion.div>
					)}

					{/* Success Step */}
					{currentStep === "success" && (
						<motion.div
							key="success"
							custom={direction}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full"
						>
							<TransactionSuccess
								title="PIN Changed Successfully!"
								message="Your card PIN has been updated successfully. You can now use your new PIN for transactions."
								buttonText="Done"
								onNextAction={onClose}
							/>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
};

export default ChangeCardPin;
