import { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/custom/button";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { X } from "lucide-react";
import { useWithdrawFromCard } from "@/hooks/api/cards"; 
import { ICard } from "@/types/cards";


const withdrawCardSchema = z.object({
	amount: z
		.string()
		.min(1, "Please enter an amount")
		.refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, {
			message: "Amount must be a positive number",
		}),
});

type WithdrawCardData = z.infer<typeof withdrawCardSchema>;
type Step = "form" | "pin" | "success";

interface WithdrawCardProps {
	onClose: () => void;
	card: ICard; 
}

export const WithdrawCard = ({ onClose, card }: WithdrawCardProps) => {
	const [currentStep, setCurrentStep] = useState<Step>("form");
	const [pin, setPin] = useState<string>("");
	const [isProcessing, setIsProcessing] = useState<boolean>(false); 
	const { mutateAsync: withdrawFromCardMutation } = useWithdrawFromCard();

	const form = useForm<WithdrawCardData>({
		resolver: zodResolver(withdrawCardSchema),
		defaultValues: {
			amount: "",
		},
	});

	const handleFormSubmit = (data: WithdrawCardData) => {
		setCurrentStep("pin");
	};

	const handlePinSubmit = async () => {
		if (pin.length !== 4) return;

		setIsProcessing(true);
		try {
			await withdrawFromCardMutation({
				amount: form.getValues().amount,
				card_id: card.id,
			});
			setCurrentStep("success");
		} catch (error) {
			console.error("Failed to withdraw from card:", error);
		} finally {
			setIsProcessing(false);
		}
	};

	const slideVariants = {
		enter: (direction: number) => ({
			x: direction > 0 ? 300 : -300,
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: number) => ({
			x: direction < 0 ? 300 : -300,
			opacity: 0,
		}),
	};

	const getStepTitle = () => {
		switch (currentStep) {
			case "form":
				return "Withdraw from Card";
			case "pin":
				return "Enter Transaction PIN";
			case "success":
				return "";
			default:
				return "Withdraw from Card";
		}
	};

	const getStepDescription = () => {
		switch (currentStep) {
			case "form":
				return "Enter the amount you want to withdraw from your card";
			case "pin":
				return "Enter your 4-digit transaction PIN to confirm withdrawal";
			case "success":
				return "";
			default:
				return "";
		}
	};

	if (currentStep === "success") {
		return (
			<div className="flex flex-col h-full p-6">
				<TransactionSuccess
					title="Withdrawal Successful!"
					message={`You have successfully withdrawn ₦${
						form.getValues().amount
					} from your card.`}
					buttonText="Done"
					onNextAction={() => {
						form.reset();
						setCurrentStep("form");
						onClose();
					}}
				/>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full p-6">
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h2 className="text-xl font-bold text-foreground">
						{getStepTitle()}
					</h2>
					{getStepDescription() && (
						<p className="text-sm text-muted-foreground mt-1">
							{getStepDescription()}
						</p>
					)}
				</div>
				<button
					onClick={onClose}
					className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
				>
					<X className="w-5 h-5" />
				</button>
			</div>

			{/* Animated Steps */}
			<div className="flex-1 relative overflow-hidden">
				<AnimatePresence mode="wait" custom={1}>
					{currentStep === "form" && (
						<motion.div
							key="form"
							custom={1}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 flex flex-col"
						>
							<form
								onSubmit={form.handleSubmit(handleFormSubmit)}
								className="space-y-8 flex-1 flex flex-col justify-between"
							>
								<div className="space-y-6">
									<Controller
										name="amount"
										control={form.control}
										render={({
											field,
											fieldState: { error },
										}) => (
											<div>
												<input
													{...field}
													id="amount"
													type="number"
													placeholder="Enter amount to withdraw"
													className="w-full h-[48px] my-4 rounded-full bg-background dark:bg-input text-base text-foreground border border-border px-4 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
												/>
												{error?.message && (
													<p className="text-red-500 text-sm mt-1 ml-2">
														{error.message}
													</p>
												)}
											</div>
										)}
									/>
								</div>
								<div className="flex justify-center pt-4 gap-3">
									<Button
										type="button"
										variant="secondary"
										onClick={onClose}
										className="flex-1"
									>
										Cancel
									</Button>
									<Button
										type="submit"
										className="flex-1 bg-primary cursor-pointer hover:bg-primary/90 text-white"
										disabled={isProcessing}
									>
										Continue
									</Button>
								</div>
							</form>
						</motion.div>
					)}

					{currentStep === "pin" && (
						<motion.div
							key="pin"
							custom={1}
							variants={slideVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 flex flex-col"
						>
							<div className="flex-1 flex flex-col items-center justify-center gap-6">
								<div className="text-center">
									<p className="text-gray-600 dark:text-gray-400">
										Enter your 4-digit transaction PIN to
										confirm withdrawal
									</p>
								</div>

								<div className="flex justify-center">
									<InputOTP
										pattern={REGEXP_ONLY_DIGITS}
										value={pin}
										onChange={setPin}
										onComplete={handlePinSubmit}
										maxLength={4}
										autoFocus
										containerClassName="gap-3"
									>
										<InputOTPGroup>
											<InputOTPSlot
												index={0}
												className="size-16 text-lg border border-border rounded-md"
											/>
											<InputOTPSlot
												index={1}
												className="size-16 text-lg border border-border rounded-md"
											/>
											<InputOTPSlot
												index={2}
												className="size-16 text-lg border border-border rounded-md"
											/>
											<InputOTPSlot
												index={3}
												className="size-16 text-lg border border-border rounded-md"
											/>
										</InputOTPGroup>
									</InputOTP>
								</div>
							</div>
							<div className="w-full space-y-4 pt-4">
								<Button
									onClick={handlePinSubmit}
									disabled={pin.length !== 4 || isProcessing}
									className="w-full py-6 rounded-full bg-primary text-primary-foreground font-medium"
								>
									{isProcessing
										? "Processing..."
										: "Confirm Withdrawal"}
								</Button>

								<Button
									type="button"
									variant="secondary"
									onClick={() => setCurrentStep("form")}
									className="w-full py-6 rounded-full"
								>
									Back
								</Button>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
};

export default WithdrawCard;
