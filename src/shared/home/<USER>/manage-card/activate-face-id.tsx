import { useState } from "react";
import { But<PERSON> } from "@/components/custom/button";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { X, Fingerprint } from "lucide-react";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";

interface ActivateFaceIdProps {
	onClose: () => void;
}

type Step = "toggle" | "pin" | "success";

export const ActivateFaceId = ({ onClose }: ActivateFaceIdProps) => {
	const [currentStep, setCurrentStep] = useState<Step>("toggle");
	const [isFaceIdEnabled, setIsFaceIdEnabled] = useState(false);
	const [pin, setPin] = useState("");
	const [isLoading, setIsLoading] = useState(false);

	const handleToggle = () => {
		setIsFaceIdEnabled((prev) => !prev);
	};

	const handleContinue = () => {
		if (isFaceIdEnabled) setCurrentStep("pin");
	};

	const handlePinSubmit = async () => {
		if (pin.length !== 4) return;
		setIsLoading(true);
		try {
			await new Promise((resolve) => setTimeout(resolve, 1500));
			setCurrentStep("success");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="flex flex-col h-full p-6">
			{/* Header */}
			{currentStep !== "success" && (
				<div className="flex items-center justify-between mb-6">
					<div>
						<h2 className="text-xl font-bold text-foreground">
							Activate Face ID
						</h2>
						<p className="text-sm text-muted-foreground mt-1">
							Enable facial recognition for enhanced security
						</p>
					</div>
					<button
						onClick={onClose}
						className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
					>
						<X className="w-5 h-5" />
					</button>
				</div>
			)}

			{currentStep === "toggle" && (
				<div className="space-y-8 flex-1 flex flex-col justify-center items-center">
					<div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto dark:bg-primary/20">
						<Fingerprint className="w-10 h-10 text-primary" />
					</div>
					<h3 className="text-lg font-semibold text-foreground mb-2 text-center">
						Activate Face ID for Your Card
					</h3>
					<p className="text-sm text-muted-foreground text-center">
						Add an extra layer of security to your transactions with
						Face ID authentication.
					</p>
					<div className="flex items-center gap-4 mt-6">
						<span className="text-base font-medium text-foreground">
							Face ID
						</span>
						<button
							type="button"
							className={`w-12 h-6 flex items-center rounded-full p-1 transition-colors duration-200 ${
								isFaceIdEnabled ? "bg-primary" : "bg-gray-300"
							}`}
							onClick={handleToggle}
							aria-pressed={isFaceIdEnabled}
						>
							<span
								className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-200 ${
									isFaceIdEnabled ? "translate-x-6" : ""
								}`}
							></span>
						</button>
					</div>
					<div className="flex gap-3 pt-8 w-full max-w-xs mx-auto">
						<Button
							type="button"
							variant="secondary"
							onClick={onClose}
							className="flex-1"
						>
							Cancel
						</Button>
						<Button
							type="button"
							disabled={!isFaceIdEnabled}
							onClick={handleContinue}
							className="flex-1 bg-primary hover:bg-primary/90 text-white"
						>
							Continue
						</Button>
					</div>
				</div>
			)}

			{currentStep === "pin" && (
				<div className="flex flex-col flex-1 justify-between">
					<div className="flex-1 flex flex-col items-center justify-center gap-6">
						<div className="text-center">
							<p className="text-gray-600 dark:text-gray-400">
								Enter your 4-digit transaction PIN to confirm
								Face ID activation
							</p>
						</div>
						<div className="flex justify-center">
							<InputOTP
								pattern={REGEXP_ONLY_DIGITS}
								value={pin}
								onChange={setPin}
								onComplete={handlePinSubmit}
								maxLength={4}
								autoFocus
								containerClassName="gap-3"
							>
								<InputOTPGroup>
									<InputOTPSlot
										index={0}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={1}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={2}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={3}
										className="size-16 text-lg border border-border rounded-md"
									/>
								</InputOTPGroup>
							</InputOTP>
						</div>
					</div>
					<div className="w-full space-y-4 pt-4">
						<Button
							onClick={handlePinSubmit}
							disabled={pin.length !== 4 || isLoading}
							className="w-full py-6 rounded-full bg-primary text-primary-foreground font-medium"
						>
							{isLoading ? "Activating..." : "Confirm Activation"}
						</Button>
						<Button
							type="button"
							variant="secondary"
							onClick={() => setCurrentStep("toggle")}
							className="w-full py-6 rounded-full"
						>
							Back
						</Button>
					</div>
				</div>
			)}

			{currentStep === "success" && (
				<div className="flex-1">
					<TransactionSuccess
						title="Face ID Activated!"
						message="Face ID has been successfully activated for your card. You can now use facial recognition for secure transactions."
						buttonText="Done"
						onNextAction={onClose}
					/>
				</div>
			)}
		</div>
	);
};

export default ActivateFaceId;
