import { Button } from "@/components/custom/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { Card } from "../card-item";
import { useMediaQuery } from "@/hooks/use-media-query";

interface ManageCardSectionProps {
	onBack: () => void;
}

const menuItems = [
	{ label: "Change Card Pin", action: "change-pin" },
	{ label: "Rename Card", action: "rename" },
	{ label: "Block Card", action: "block" },
	{ label: "Pause Transactions", action: "pause" },
	{ label: "Manage Card", action: "manage" },
	{ label: "Change Funding Source", action: "funding" },
	{ label: "Resume Transactions", action: "resume" },
];

export const ManageCardSection = ({ onBack }: ManageCardSectionProps) => {
	const { openDrawer } = useDrawer();
	const isMobile = useMediaQuery("(max-width: 768px)");

	const drawerContent: Record<string, React.ReactNode> = {
		"change-pin": (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">Change Card PIN</h2>
				<p>Change your card's PIN securely.</p>
			</div>
		),
		rename: (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">Rename Card</h2>
				<p>Give your card a new name for easy identification.</p>
			</div>
		),
		block: (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">Block Card</h2>
				<p>
					Permanently block this card. This action cannot be undone.
				</p>
				<Button variant="secondary" className="mt-4">
					Block Card
				</Button>
			</div>
		),
		pause: (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">Pause Transactions</h2>
				<p>Temporarily pause all transactions  this card.</p>
				<Button variant="secondary" className="mt-4">
					Pause Card
				</Button>
			</div>
		),
		manage: (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">Manage Card</h2>
				<p>Access advanced card management features.</p>
			</div>
		),
		funding: (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">
					Change Funding Source
				</h2>
				<p>Change the account or wallet funding this card.</p>
			</div>
		),
		resume: (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">Resume Transactions</h2>
				<p>Resume all transactions on this card.</p>
				<Button className="mt-4">Resume Card</Button>
			</div>
		),
		faceid: (
			<div className="p-8">
				<h2 className="text-xl font-bold mb-4">Activate Face ID</h2>
				<p>Enable Face ID for extra security on this card.</p>
				<Button className="mt-4">Activate Face ID</Button>
			</div>
		),
	};

	const handleMenuClick = (action: string) => {
		openDrawer({
			view: drawerContent[action] || (
				<div className="p-8">No content</div>
			),
			placement: isMobile ? "bottom" : "right",
			customSize: isMobile ? undefined : "480px",
		});
	};

	const handleFaceId = () => {
		openDrawer({
			view: drawerContent["faceid"],
			placement: isMobile ? "bottom" : "right",
			customSize: isMobile ? undefined : "480px",
		});
	};

	return (
		<div className="container mx-auto p-6">
			<div className="flex items-center gap-3 mb-8">
				<button
					onClick={onBack}
					className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
				>
					<ChevronLeft className="w-5 h-5 text-primary" />
				</button>
				<h3 className="text-2xl font-bold text-foreground">
					Manage Card
				</h3>
			</div>
			<div className="flex flex-col md:flex-row gap-8 items-start">
				<div className="w-full max-w-sm xl:max-w-none xl:w-88 mx-auto xl:mx-0 relative">
					<Card className="w-full h-48" />
					<div className="text-lg font-semibold text-foreground mb-2 mt-4">
						Nigerian Naira Card
					</div>
				</div>
				{/* Menu Actions */}
				<div className="w-full md:w-1/2 flex flex-col gap-3">
					{menuItems.map((item) => (
						<button
							key={item.action}
							onClick={() => handleMenuClick(item.label)}
							className="flex justify-between items-center w-full bg-white border border-border rounded-full px-6 py-4 text-left text-foreground hover:bg-gray-50 transition group dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white dark:border-gray-700"
						>
							<span className="text-base font-medium">
								{item.label}
							</span>
							<ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-primary" />
						</button>
					))}
					<div className="mt-6">
						<button
							onClick={handleFaceId}
							className="flex justify-between items-center w-full border-2 border-dotted border-[#a259ff] rounded-xl px-6 py-4 text-left text-[#a259ff] font-semibold bg-white hover:bg-[#f6f0ff] transition group dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-[#a259ff] dark:border-[#a259ff]"
						>
							<span>Activate Face ID</span>
							<ChevronRight className="w-5 h-5 text-[#a259ff] group-hover:text-primary" />
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};
