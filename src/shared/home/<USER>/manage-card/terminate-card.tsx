import { useState } from "react";
import { But<PERSON> } from "@/components/custom/button";
import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";
import { X } from "lucide-react";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { ICard } from "@/types/cards";
import { useTerminateCard } from "@/hooks/api/cards";

interface PauseTransactionsProps {
	onClose: () => void;
	card?:ICard
}

export const TerminateCard = ({ onClose,card }: PauseTransactionsProps) => {
	const [step, setStep] = useState<"reason" | "date" | "pin" | "success">(
		"reason",
	);
	const [reason, setReason] = useState("");
	const [reasonError, setReasonError] = useState("");
	const [pauseDate, setPauseDate] = useState("");
	const [dateError, setDateError] = useState("");
	const [pin, setPin] = useState("");
	const [isLoading, setIsLoading] = useState(false);

	const handleReasonContinue = (e: React.FormEvent) => {
		e.preventDefault();
		if (!reason.trim()) {
			setReasonError("Please provide a reason for terminating card.");
			return;
		}
		setReasonError("");
		setStep("date");
	};

	const handleDateContinue = (e: React.FormEvent) => {
		e.preventDefault();
		if (!pauseDate) {
			setDateError("Please select a date to unterminate.");
			return;
		}
		setDateError("");
		setStep("pin");
	};

	const handlePinSubmit = async () => {
		if (pin.length !== 4) return;
		setIsLoading(true);
		try {
			await new Promise((resolve) => setTimeout(resolve, 1500));
			setStep("success");
		} finally {
			setIsLoading(false);
		}
	};

	if (step === "success") {
		return (
			<div className="flex flex-col h-full p-6">
				<TransactionSuccess
					title="Transactions Paused!"
					message={`All transactions on this card have been paused until ${pauseDate}. You can resume them anytime.`}
					buttonText="Done"
					onNextAction={onClose}
				/>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full p-6">
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h2 className="text-xl font-bold text-foreground">
						Pause Transactions
					</h2>
					<p className="text-sm text-muted-foreground mt-1">
						Temporarily pause all transactions on this card
					</p>
				</div>
				<button
					onClick={onClose}
					className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
				>
					<X className="w-5 h-5" />
				</button>
			</div>

			{step === "reason" && (
				<form
					onSubmit={handleReasonContinue}
					className="flex flex-col flex-1 justify-between"
				>
					<div className="flex-1 flex flex-col items-center justify-center gap-8">
						<FloatingLabelInput
							id="pauseReason"
							label="Reason for pausing transactions"
							type="text"
							value={reason}
							onChange={(e) => setReason(e.target.value)}
							error={reasonError}
							className="w-full"
							autoFocus
						/>
					</div>
					<div className="flex gap-3 pt-8 w-full max-w-xs mx-auto">
						<Button
							type="button"
							variant="secondary"
							onClick={onClose}
							className="flex-1"
						>
							Cancel
						</Button>
						<Button
							type="submit"
							className="flex-1 bg-primary hover:primary/90 text-white"
						>
							Continue
						</Button>
					</div>
				</form>
			)}

			{step === "date" && (
				<form
					onSubmit={handleDateContinue}
					className="flex flex-col flex-1 justify-between"
				>
					<div className="flex-1 flex flex-col items-center justify-center gap-8">
						<div className="w-full max-w-xs mx-auto">
							<label
								htmlFor="pauseDate"
								className="block text-sm font-medium text-foreground mb-2"
							>
								Select Resume Date
							</label>
							<input
								id="pauseDate"
								type="date"
								min={new Date().toISOString().split("T")[0]}
								value={pauseDate}
								onChange={(e) => setPauseDate(e.target.value)}
								className="w-full rounded-full border border-gray-300 px-4 py-3 text-base text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400 placeholder:text-gray-400"
							/>
							{dateError && (
								<p className="text-red-500 text-sm mt-2">
									{dateError}
								</p>
							)}
						</div>
					</div>
					<div className="flex gap-3 pt-8 w-full max-w-xs mx-auto">
						<Button
							type="button"
							variant="secondary"
							onClick={() => setStep("reason")}
							className="flex-1"
						>
							Back
						</Button>
						<Button
							type="submit"
							className="flex-1 bg-primary hover:primary/90 text-white"
						>
							Continue
						</Button>
					</div>
				</form>
			)}

			{step === "pin" && (
				<div className="flex flex-col flex-1 justify-between">
					<div className="flex-1 flex flex-col items-center justify-center gap-6">
						<div className="text-center">
							<p className="text-gray-600 dark:text-gray-400">
								Enter your 4-digit transaction PIN to confirm
								card termination
							</p>
						</div>
						<div className="flex justify-center">
							<InputOTP
								pattern={REGEXP_ONLY_DIGITS}
								value={pin}
								onChange={setPin}
								onComplete={handlePinSubmit}
								maxLength={4}
								autoFocus
								containerClassName="gap-3"
							>
								<InputOTPGroup>
									<InputOTPSlot
										index={0}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={1}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={2}
										className="size-16 text-lg border border-border rounded-md"
									/>
									<InputOTPSlot
										index={3}
										className="size-16 text-lg border border-border rounded-md"
									/>
								</InputOTPGroup>
							</InputOTP>
						</div>
					</div>
					<div className="w-full space-y-4 pt-4">
						<Button
							onClick={handlePinSubmit}
							disabled={pin.length !== 4 || isLoading}
							className="w-full py-6 rounded-full bg-primary text-primary-foreground font-medium"
						>
							{isLoading ? "Pausing..." : "Confirm Pause"}
						</Button>
						<Button
							type="button"
							variant="secondary"
							onClick={() => setStep("date")}
							className="w-full py-6 rounded-full"
						>
							Back
						</Button>
					</div>
				</div>
			)}
		</div>
	);
};

export default TerminateCard;
