import { useState } from "react";
import { But<PERSON> } from "@/components/custom/button";
import { <PERSON>, <PERSON>tings, CreditCard, Shield, Bell, Eye, Lock } from "lucide-react";

interface ManageCardSettingsProps {
	onClose: () => void;
}

interface SettingItem {
	id: string;
	title: string;
	description: string;
	icon: React.ReactNode;
	enabled: boolean;
	action: () => void;
}

export const ManageCardSettings = ({ onClose }: ManageCardSettingsProps) => {
	const [settings, setSettings] = useState<SettingItem[]>([
		{
			id: "notifications",
			title: "Transaction Notifications",
			description: "Get notified for all card transactions",
			icon: <Bell className="w-5 h-5" />,
			enabled: true,
			action: () => toggleSetting("notifications"),
		},
		{
			id: "contactless",
			title: "Contactless Payments",
			description: "Enable tap-to-pay functionality",
			icon: <CreditCard className="w-5 h-5" />,
			enabled: true,
			action: () => toggleSetting("contactless"),
		},
		{
			id: "online",
			title: "Online Purchases",
			description: "Allow online and e-commerce transactions",
			icon: <Shield className="w-5 h-5" />,
			enabled: true,
			action: () => toggleSetting("online"),
		},
		{
			id: "international",
			title: "International Transactions",
			description: "Enable transactions outside Nigeria",
			icon: <Shield className="w-5 h-5" />,
			enabled: false,
			action: () => toggleSetting("international"),
		},
		{
			id: "spending-limits",
			title: "Spending Limits",
			description: "Set daily and monthly spending limits",
			icon: <Lock className="w-5 h-5" />,
			enabled: true,
			action: () => handleSpendingLimits(),
		},
		{
			id: "transaction-history",
			title: "Transaction Visibility",
			description: "Show detailed transaction information",
			icon: <Eye className="w-5 h-5" />,
			enabled: true,
			action: () => toggleSetting("transaction-history"),
		},
	]);

	const [isLoading, setIsLoading] = useState<string | null>(null);

	const toggleSetting = async (settingId: string) => {
		setIsLoading(settingId);
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 1000));

			setSettings((prev) =>
				prev.map((setting) =>
					setting.id === settingId
						? { ...setting, enabled: !setting.enabled }
						: setting,
				),
			);
		} catch (error) {
			console.error(`Failed to toggle ${settingId}:`, error);
		} finally {
			setIsLoading(null);
		}
	};

	const handleSpendingLimits = () => {
		console.log("Open spending limits configuration");
	};

	return (
		<div className="flex flex-col h-full p-6">
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h2 className="text-xl font-bold text-foreground">
						Manage Card Settings
					</h2>
					<p className="text-sm text-muted-foreground mt-1">
						Configure security and transaction preferences
					</p>
				</div>
				<button
					onClick={onClose}
					className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
				>
					<X className="w-5 h-5" />
				</button>
			</div>

			<div className="space-y-6 flex-1">
				{/* Card Information */}
				<div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800">
					<div className="flex items-center space-x-3">
						<div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center dark:bg-blue-900/30">
							<CreditCard className="w-5 h-5 text-blue-600" />
						</div>
						<div>
							<h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
								Nigerian Naira Card
							</h3>
							<p className="text-sm text-blue-600 dark:text-blue-400">
								**** **** **** 1234 • Active
							</p>
						</div>
					</div>
				</div>

				{/* Settings List */}
				<div className="space-y-4">
					<h3 className="font-semibold text-foreground flex items-center">
						<Settings className="w-5 h-5 mr-2" />
						Security & Preferences
					</h3>

					<div className="space-y-3">
						{settings.map((setting) => (
							<div
								key={setting.id}
								className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-750"
							>
								<div className="flex items-center space-x-4">
									<div className="text-primary">
										{setting.icon}
									</div>
									<div className="flex-1">
										<h4 className="text-sm font-medium text-foreground">
											{setting.title}
										</h4>
										<p className="text-xs text-muted-foreground mt-1">
											{setting.description}
										</p>
									</div>
								</div>

								<div className="flex items-center space-x-3">
									{setting.id === "spending-limits" ? (
										<Button
											variant="secondary"
											onClick={setting.action}
											className="text-xs px-3 py-1 h-8"
										>
											Configure
										</Button>
									) : (
										<button
											onClick={setting.action}
											disabled={isLoading === setting.id}
											className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
												setting.enabled
													? "bg-primary"
													: "bg-gray-200 dark:bg-gray-600"
											} ${
												isLoading === setting.id
													? "opacity-50"
													: ""
											}`}
										>
											<span
												className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
													setting.enabled
														? "translate-x-6"
														: "translate-x-1"
												}`}
											/>
										</button>
									)}
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Quick Actions */}
				<div className="space-y-4">
					<h3 className="font-semibold text-foreground">
						Quick Actions
					</h3>
					<div className="grid grid-cols-2 gap-3">
						<Button
							variant="secondary"
							className="flex flex-col items-center p-4 h-auto"
							onClick={() =>
								console.log("View transaction history")
							}
						>
							<Eye className="w-5 h-5 mb-2" />
							<span className="text-xs">Transaction History</span>
						</Button>
						<Button
							variant="secondary"
							className="flex flex-col items-center p-4 h-auto"
							onClick={() => console.log("Download statements")}
						>
							<Shield className="w-5 h-5 mb-2" />
							<span className="text-xs">Security Settings</span>
						</Button>
					</div>
				</div>

				<div className="flex gap-3 pt-4">
					<Button type="button" onClick={onClose} className="flex-1">
						Done
					</Button>
				</div>
			</div>
		</div>
	);
};

export default ManageCardSettings;
