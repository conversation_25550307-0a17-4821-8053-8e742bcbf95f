import { ChevronLeft } from "lucide-react";
import { useCardColor } from "@/store/create-card-store";
import whitecard from "@/assets/images/whitecard.png";
import blackcard from "@/assets/images/blackcard.png";
import yellowcard from "@/assets/images/yellowcard.png";

interface ChangeDesignSectionProps {
	onBack: () => void;
}

export const ChangeDesignSection = ({ onBack }: ChangeDesignSectionProps) => {
	const { currentColor, setColor } = useCardColor();

	const designs = {
		yellow: { src: yellowcard, alt: "Yellow Card design" },
		white: { src: whitecard, alt: "White Card design" },
		black: { src: blackcard, alt: "Black Card design" },
	} as const;
	const otherDesigns = (
		Object.keys(designs) as Array<keyof typeof designs>
	).filter((color) => color !== currentColor);

	const handleDesignSelect = (color: keyof typeof designs) => {
		setColor(color);
	};

	return (
		<div className="container mx-auto p-6">
			<div className="flex items-center gap-3 mb-8">
				<button
					onClick={onBack}
					className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
				>
					<ChevronLeft className="w-5 h-5 text-primary" />
				</button>
				<h3 className="text-2xl font-bold text-foreground">
					Change Card Design
				</h3>
			</div>

			<div className="mb-8">
				<h4 className="text-lg font-semibold mb-4">
					Current Card Design
				</h4>
				<div className=" w-[300px] md:w-[400px] overflow-auto ">
					<img
						src={designs[currentColor]?.src || designs.white.src}
						alt={designs[currentColor]?.alt || "Card design"}
						className="w-full h-full object-cover"
					/>
				</div>
			</div>

			<div>
				<h3 className="m-5 font-bold">Select new card design</h3>

				<div>
					<div className=" flex flex-col md:flex-row gap-10">
						{otherDesigns.map((design) => (
							<div
								className="w-[300px] md:w-[400px] overflow-auto   relative"
								key={design}
							>
								<img src={designs[design].src} alt="" />

								<p
									onClick={() => handleDesignSelect(design)}
									className="text-primary cursor-pointer m-5 text-center hover:text-primary/80 transition-all"
								>
									Select Design
								</p>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
};
