import { TransactionSuccess } from "@/components/crypto-purchase/transaction-success";

	const firstname = localStorage.getItem("firstName")
	const lastname= localStorage.getItem("lastName")

interface SwapTransactionSuccessProps {
	amount: string;
	symbol: string;
	onViewReceipt: () => void;
}

export function SwapTransactionSuccess({
	amount,
	symbol,
	onViewReceipt,
}: SwapTransactionSuccessProps) {
	const message = (
		<p className="text-center text-gray-600">
			Hi {firstname} {lastname}, you have successfully swapped <br />
			{amount} {symbol}
		</p>
	);
	return (
		<div className="flex flex-col items-center justify-center gap-4 p-4 flex-grow">
		{/* <div className="flex flex-col items-center justify-center h-full p-6 space-y-6"> */}
			<TransactionSuccess
				title="Transaction Successful!"
				message={message}
				buttonText="View Receipt"
				onNextAction={onViewReceipt}
			/>
		</div>
	);
}
