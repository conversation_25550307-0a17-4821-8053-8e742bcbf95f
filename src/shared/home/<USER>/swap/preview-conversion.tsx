"use client";

import { TransactionSummaryCard } from "@/components/transaction-summary-card";
import { Button } from "@/components/ui/button";

interface PreviewConversionProps {
	fromSymbol: string;
	toSymbol: string;
	fromAmount: string;
	toAmount: string;
	fee: string;
	date: string;
	onSendCrypto: () => void;
}

export function PreviewConversion({
	fromSymbol,
	toSymbol,
	fromAmount,
	toAmount,
	fee,
	date,
	onSendCrypto,
}: PreviewConversionProps) {
	return (
		<div className="flex flex-col p-6 space-y-6">
			<TransactionSummaryCard
				details={[
					{ label: "From", value: fromSymbol },
					{ label: "to", value: toSymbol },
					{
						label: "Amount(From)",
						value: fromAmount,
					},
					{ label: "Amount(To)", value: toAmount },
					{ label: "fee", value: fee },
					{
						label: "Date",
						value: date,
					},
				]}
			/>

			{/* Send Crypto Button */}
			<Button
				className="w-full py-6 text-base bg-primary hover:bg-primary/80 text-white rounded-full"
				onClick={onSendCrypto}
			>
				Send Crypto
			</Button>
		</div>
	);
}
