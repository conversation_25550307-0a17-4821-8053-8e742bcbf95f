
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { IOption } from "@/types/general";
import { InternalSwapCryptoFlow } from "./internal-swap";


export const ArrowDataTransferVerticalIcon = (
	props: React.SVGProps<SVGSVGElement>,
) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={24}
		height={24}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M15 19L15 6.65856C15 5.65277 15 5.14987 15.3087 5.02472C15.6173 4.89956 15.9806 5.25517 16.7071 5.96637L19 8.21091"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M9 5L9 17.3414C9 18.3472 9 18.8501 8.69134 18.9753C8.38268 19.1004 8.01942 18.7448 7.29289 18.0336L5 15.7891"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

const Exchange03Icon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={28}
		height={28}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M16.125 20.5L16.125 14.5M18 14.5V13M18 22V20.5M16.125 17.5H19.875M19.875 17.5C20.4963 17.5 21 18.0037 21 18.625V19.375C21 19.9963 20.4963 20.5 19.875 20.5H15M19.875 17.5C20.4963 17.5 21 16.9963 21 16.375V15.625C21 15.0037 20.4963 14.5 19.875 14.5H15"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M11 5C13.8284 5 16.2426 5 17.1213 5.7988C18 6.5976 18 7.4287 18 10L16 9"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M12 20C9.17157 20 6.75736 20 5.87868 19.2012C5 18.4024 5 17.5713 5 15L7 16"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M5.5 3.25C4.11929 3.25 3 4.08947 3 5.125C3 6.16053 4.11929 7 5.5 7C6.88071 7 8 7.83947 8 8.875C8 9.91053 6.88071 10.75 5.5 10.75M5.5 3.25C6.58852 3.25 7.51455 3.77175 7.85775 4.5M5.5 3.25V2M5.5 10.75C4.41148 10.75 3.48545 10.2282 3.14225 9.5M5.5 10.75V12"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
		/>
	</svg>
);

const CoinsSwapIcon = (props: React.SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		width={28}
		height={28}
		color={"#000000"}
		fill={"none"}
		{...props}
	>
		<path
			d="M10 7.0268C10.483 4.17323 12.9665 2 15.9575 2C19.2947 2 22 4.70532 22 8.0425C22 11.0335 19.8268 13.517 16.9732 14"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M8 22C11.3137 22 14 19.3137 14 16C14 12.6863 11.3137 10 8 10C4.68629 10 2 12.6863 2 16C2 19.3137 4.68629 22 8 22Z"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M2 9C2 5.68286 4.68286 3 8 3L7.14286 4.71429"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M22 15C22 18.3171 19.3171 21 16 21L16.8571 19.2857"
			stroke="currentColor"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);


type SwapFlowStep = "selection" | "internal" | "web3" | "other";


export function SwapCryptoFlow() {
	const { closeDrawer } = useDrawer();

	
	const [currentStep, setCurrentStep] = useState<SwapFlowStep>("selection");
	const [direction, setDirection] = useState<"forward" | "backward">(
		"forward",
	);

	
	const swapTypes: IOption[] = [
		{
			label: "Internal Swap",
			value: "internal",
			icon: <Exchange03Icon className="text-primary" />,
		},
		{
			label: "Web3 Swap",
			value: "web3",
			hidden: true,
			icon: <CoinsSwapIcon className="text-primary" />,
		},
		
	].filter((option) => !option.hidden);

	
	const [selectedOption, setSelectedOption] = useState<string | null>(null);

	
	const handleOptionSelect = (value: string) => {
		setSelectedOption(value);
	};

	
	const handleContinue = () => {
		if (selectedOption) {
			setDirection("forward");
			setCurrentStep(selectedOption as SwapFlowStep);
		}
	};

	
	const handleClose = () => {
		
		closeDrawer();

		
		setTimeout(() => {
			setCurrentStep("selection");
			setSelectedOption(null);
			setDirection("forward");
		}, 300);
	};

	
	const handleReturnToSelection = () => {
		setDirection("backward");
		setCurrentStep("selection");
	};

	
	const getStepTitle = () => {
		switch (currentStep) {
			case "selection":
				return "Choose Swap";
			default:
				return null;
		}
	};

	
	const horizontalVariants = {
		enter: (direction: string) => ({
			x: direction === "forward" ? "100%" : "-100%",
			opacity: 0,
		}),
		center: {
			x: 0,
			opacity: 1,
		},
		exit: (direction: string) => ({
			x: direction === "forward" ? "-100%" : "100%",
			opacity: 0,
		}),
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex flex-col mt-10 relative px-6">
				<Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>

				{getStepTitle() && (
					<h2 className="text-2xl font-semibold text-center mb-6">
						{getStepTitle()}
					</h2>
				)}
			</div>

			<div className="p-0 h-full overflow-hidden relative flex-grow">
				<AnimatePresence initial={false} mode="wait" custom={direction}>
					{currentStep === "selection" && (
						<motion.div
							key="selection"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<div className="flex flex-col h-full">
								<div className="flex-1 p-6">
									{/* Swap options list */}
									<div className="space-y-4">
										{swapTypes.map((option) => (
											<div
												key={option.value}
												className={`flex items-center p-4 rounded-full border-2 cursor-pointer transition-colors ${
													selectedOption ===
													option.value
														? "border-primary bg-primary-light/10"
														: "border-gray-200 dark:border-gray-700"
												}`}
												onClick={() =>
													handleOptionSelect(
														option.value,
													)
												}
											>
												{/* Radio button */}
												<div
													className={`w-6 h-6 rounded-full border-2 flex items-center justify-center mr-3 ${
														selectedOption ===
														option.value
															? "border-primary"
															: "border-gray-400"
													}`}
												>
													{selectedOption ===
														option.value && (
														<div className="w-3 h-3 rounded-full bg-primary"></div>
													)}
												</div>

												{/* Icon */}
												<div className="flex-shrink-0 mr-3">
													{option.icon}
												</div>

												{/* Label */}
												<span className="text-lg font-medium">
													{option.label}
												</span>
											</div>
										))}
									</div>
								</div>

								{/* Continue button */}
								<div className="p-6 pb-8">
									<Button
										onClick={handleContinue}
										disabled={!selectedOption}
										className="w-full h-14 rounded-full text-lg font-medium"
									>
										Continue
									</Button>
								</div>
							</div>
						</motion.div>
					)}

					{currentStep === "internal" && (
						<motion.div
							key="internal"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<InternalSwapCryptoFlow
								hideCloseButton={true}
								onReturnToSelection={handleReturnToSelection}
								
							/>
						</motion.div>
					)}

					{currentStep === "web3" && (
						<motion.div
							key="web3"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							{/* Web3 Swap Flow would go here */}
							<div className="flex items-center justify-center h-full">
								<p>Web3 Swap Flow (Coming Soon)</p>
							</div>
						</motion.div>
					)}

					{/* Placeholder for future swap types */}
					{currentStep === "other" && (
						<motion.div
							key="other"
							custom={direction}
							variants={horizontalVariants}
							initial="enter"
							animate="center"
							exit="exit"
							transition={{
								x: {
									type: "spring",
									stiffness: 300,
									damping: 30,
								},
								opacity: { duration: 0.2 },
							}}
							className="absolute inset-0 w-full h-full overflow-hidden"
						>
							<div className="flex items-center justify-center h-full">
								<p>Other Swap Type Flow</p>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	);
}
