import { TransactionSummaryCard } from "@/components/transaction-summary-card";
import { Button } from "@/components/ui/button";

interface TransactionReceiptProps {
	fromSymbol: string;
	toSymbol: string;
	fromAmount: string;
	toAmount: string;
	fee: string;
	date: string;
	onShareReceipt: () => void;
}

export function TransactionReceipt({
	fromSymbol,
	toSymbol,
	fromAmount,
	toAmount,
	fee,
	date,
	onShareReceipt,
}: TransactionReceiptProps) {
	return (
		<div className="flex flex-col px-6 space-y-6">
			<TransactionSummaryCard
				amount={fromAmount}
				currency={fromSymbol}
				status="successful"
				details={[
					{
						label: "From",
						value: fromSymbol,
					},
					{
						label: "to",
						value: toSymbol,
					},
					{
						label: "Amount(From)",
						value: fromAmount,
					},
					{
						label: "AmAmount(To)",
						value: toAmount,
					},
					{
						label: "fee",
						value: fee,
					},
					{
						label: "Date",
						value: date,
					},
				]}
			/>

			{/* Share Receipt Button */}
			<Button
				className="w-full py-6 text-base bg-primary hover:bg-primary/80 text-white rounded-full mt-2"
				onClick={onShareReceipt}
			>
				Share Receipt
			</Button>
		</div>
	);
}
