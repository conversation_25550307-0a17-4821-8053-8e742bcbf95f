import { Pin<PERSON>anager } from "@/components/transaction/transaction-pin-manager";
import { ResponseStatus } from "@/config/enums";
import {
	useCreateSwap,
	useGetSwapPrice,
	useGetSwapRates,
} from "@/hooks/api/swaps";
import useSwapCryptoTransactionStore from "@/store/swap-crypto-store";
import { notify } from "@/utils/notify";
import { useState } from "react";

const CONVERSION_TYPE = "INSTANT SWAP CRYPTO TO CRYPTO";

export function SwapPinManager({ onComplete }: { onComplete: () => void }) {
	const { transaction } = useSwapCryptoTransactionStore();

	const [isProcessingSwap, setIsProcessingSwap] = useState(false);

	const { data: priceData } = useGetSwapPrice(
		transaction?.from?.currency || "",
		transaction?.to?.currency || "",
		CONVERSION_TYPE,
	);
	const { mutateAsync: getSwapRateMutation } = useGetSwapRates();
	const { mutateAsync: createSwapMutation } = useCreateSwap();

	const handleComplete = async () => {
		if (
			!transaction ||
			!transaction.from ||
			!transaction.to ||
			!transaction.amount
		) {
			notify("Transaction details are incomplete", ResponseStatus.ERROR);
			return;
		}

		setIsProcessingSwap(true);
		try {
			if (!priceData?.price_id) {
				notify("Price data is unavailable", ResponseStatus.ERROR);
				return;
			}

			const {
				swap_rate_data: { rate_id },
			} = await getSwapRateMutation(priceData.price_id);

			const fromAccountId = transaction.from.account_id;
			const toAccountId = transaction.to.account_id;
			const amount = transaction.amount;

			await createSwapMutation(
				{
					rate_id,
					amount,
					from_account: fromAccountId,
					to_account: toAccountId,
					type: "internal",
				},
				{
					onSuccess: () => {
						notify(
							"Swap created successfully",
							ResponseStatus.SUCCESS,
						);
						setIsProcessingSwap(false);
						onComplete();
					},
					onError: (error) => {
						console.error("Swap creation failed:", error);
						notify("Swap creation failed", ResponseStatus.ERROR);
						setIsProcessingSwap(false);
					},
				},
			);
		} catch (error) {
			console.error("Swap failed:", error);
			notify("Something went wrong", ResponseStatus.ERROR);
			setIsProcessingSwap(false);
		}
	};

	return (
		<PinManager
			onComplete={handleComplete}
			isProcessing={isProcessingSwap}
		/>
	);
}
