// import { useState, useEffect } from "react";
// import { z } from "zod";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Loader2, AlertCircle, Plus } from "lucide-react";
// import { ICryptoWallet } from "@/types/crypto-wallets";
// import { IOption } from "@/types/general";
// import { formatAmount } from "@/utils/format-amount";
// import { parseFormattedNumber } from "@/utils/parse-formatted-number";
// import EnhancedSelect from "@/components/enhanced-select";
// import { Button } from "@/components/custom/button";
// import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
// import { useGetSwapPrice } from "@/hooks/api/swaps";
// import useSwapCryptoTransactionStore from "@/store/swap-crypto-store";
// import { ArrowDataTransferVerticalIcon } from "..";

// const swapSchema = z.object({
// 	amount: z.number().min(0, "Amount must be greater than 0"),
// 	to_amount: z.number().optional(),
// });

// type SwapFormValues = z.infer<typeof swapSchema>;

// const CONVERSION_TYPE = "INSTANT SWAP CRYPTO TO CRYPTO";

// interface SwapCryptoProps {
// 	onProceed: () => void;
// }

// export function SwapCrypto({ onProceed }: SwapCryptoProps) {
// 	const { setTransaction } = useSwapCryptoTransactionStore();

// 	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
// 		useCryptoUserWallets();

// 	const form = useForm<SwapFormValues>({
// 		resolver: zodResolver(swapSchema),
// 		defaultValues: {
// 			amount: undefined,
// 			to_amount: undefined,
// 		},
// 		mode: "onChange",
// 	});

// 	const { watch, setValue, handleSubmit, formState, trigger } = form;
// 	const amount = watch("amount");
// 	const toAmount = watch("to_amount");

// 	const [fromCoin, setFromCoin] = useState<ICryptoWallet | null>(null);
// 	const [toCoin, setToCoin] = useState<ICryptoWallet | null>(null);

// 	const [fromDisplay, setFromDisplay] = useState("");
// 	const [toDisplay, setToDisplay] = useState("");
// 	const [activeField, setActiveField] = useState<"from" | "to">();

// 	form.register("amount", {
// 		validate: (value) => {
// 			if (value) {
// 				if (value > (fromCoin?.balance || 0))
// 					return `Insufficient balance`;
// 			}
// 			return true;
// 		},
// 	});

// 	const { data: swapPrice, isLoading: rateLoading } = useGetSwapPrice(
// 		fromCoin?.currency || "",
// 		toCoin?.currency || "",
// 		CONVERSION_TYPE,
// 	);

// 	const fromCryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
// 		(wallet) => ({
// 			id: `crypto-${wallet.currency}`,
// 			value: wallet.currency,
// 			label: wallet.currency,
// 			icon: wallet.image,
// 			raw: wallet,
// 		}),
// 	);

// 	const toCryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets
// 		.filter((wallet) => wallet.currency !== fromCoin?.currency)
// 		.map((wallet) => ({
// 			id: `crypto-${wallet.currency}`,
// 			value: wallet.currency,
// 			label: wallet.currency,
// 			icon: wallet.image,
// 			raw: wallet,
// 		}));

// 	const selectedFromCryptoOption =
// 		fromCryptoOptions.find(
// 			(option) => option.value === fromCoin?.currency,
// 		) || null;

// 	const selectedToCryptoOption =
// 		toCryptoOptions.find((option) => option.value === toCoin?.currency) ||
// 		null;

// 	useEffect(() => {
// 		if (fromCoin && toCoin && fromCoin.currency === toCoin.currency) {
// 			setToCoin(null);
// 			setValue("to_amount", undefined);
// 			setToDisplay("");
// 		}
// 	}, [fromCoin, toCoin, setValue]);

// 	useEffect(() => {
// 		if (!swapPrice?.rate || !activeField) return;

// 		if (activeField === "from" && amount !== undefined) {
// 			const calculatedTo = amount * swapPrice.rate;
// 			setValue("to_amount", calculatedTo, { shouldValidate: true });
// 			setToDisplay(
// 				formatAmount(calculatedTo, toCoin?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		} else if (activeField === "to" && toAmount !== undefined) {
// 			const calculatedFrom = toAmount / swapPrice.rate;
// 			setValue("amount", calculatedFrom, { shouldValidate: true });
// 			setFromDisplay(
// 				formatAmount(calculatedFrom, fromCoin?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		}
// 	}, [
// 		amount,
// 		toAmount,
// 		swapPrice?.rate,
// 		activeField,
// 		setValue,
// 		toCoin,
// 		fromCoin,
// 	]);

// 	useEffect(() => {
// 		if (amount)
// 			setFromDisplay(
// 				formatAmount(amount, fromCoin?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		if (toAmount)
// 			setToDisplay(
// 				formatAmount(toAmount, toCoin?.currency, { rawFormat: true }),
// 			);
// 	}, []);

// 	useEffect(() => {
// 		if (swapPrice?.rate && activeField === "from" && amount) {
// 			const calculatedTo = amount * swapPrice.rate;
// 			setValue("to_amount", calculatedTo);
// 			setToDisplay(
// 				formatAmount(calculatedTo, toCoin?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		}
// 	}, [swapPrice?.rate, activeField, amount, setValue, toCoin]);

// 	const handleFromAmountChange = (text: string) => {
// 		if (!fromCoin) return;

// 		setActiveField("from");
// 		const parsed = parseFormattedNumber(text);
// 		setFromDisplay(text);
// 		setValue("amount", parsed);
// 		trigger("amount");
// 	};

// 	const handleToAmountChange = (text: string) => {
// 		if (!toCoin || !fromCoin) return;

// 		setActiveField("to");
// 		const parsed = parseFormattedNumber(text);
// 		setToDisplay(text);
// 		setValue("to_amount", parsed);
// 	};

// 	const handleFromBlur = () => {
// 		if (!fromCoin) return;

// 		setFromDisplay(
// 			formatAmount(amount, fromCoin?.currency, { rawFormat: true }),
// 		);
// 	};

// 	const handleToBlur = () => {
// 		if (!toCoin) return;

// 		setToDisplay(
// 			formatAmount(toAmount, toCoin?.currency, { rawFormat: true }),
// 		);
// 	};

// 	const handleFromCryptoChange = (option: IOption<ICryptoWallet> | null) => {
// 		if (!option) return;

// 		if (option.raw) {
// 			setFromCoin(option.raw);
// 		} else {
// 			const selectedWallet = cryptoWallets.find(
// 				(wallet) => wallet.currency === option.value,
// 			);
// 			setFromCoin(selectedWallet || null);
// 		}

// 		setValue("amount", 0);
// 		setValue("to_amount", 0);
// 		setFromDisplay("");
// 		setToDisplay("");
// 		setActiveField("from");
// 	};

// 	const handleToCryptoChange = (option: IOption<ICryptoWallet> | null) => {
// 		if (!option) return;

// 		if (option.raw) {
// 			setToCoin(option.raw);
// 		} else {
// 			const selectedWallet = cryptoWallets.find(
// 				(wallet) => wallet.currency === option.value,
// 			);
// 			setToCoin(selectedWallet || null);
// 		}

// 		if (amount && fromCoin) {
// 			setActiveField("from");
// 			trigger("amount");
// 		}
// 	};

// 	const handleReviewOrder = () => {
// 		if (fromCoin && toCoin && swapPrice) {
// 			const payload = {
// 				from: fromCoin,
// 				to: toCoin,
// 				amount: Number(amount),
// 				method: CONVERSION_TYPE,
// 				price_data: swapPrice,
// 			};

// 			setTransaction(payload);

// 			onProceed();
// 		}
// 	};

// 	const handleAddNew = () => {};

// 	const walletHeader = (
// 		<div className="py-2 flex justify-between items-center">
// 			<h3 className="text-2xl font-bold font-body">Wallets</h3>
// 			<Button
// 				onClick={handleAddNew}
// 				className="text-sm flex items-center rounded-full"
// 			>
// 				Add New
// 				<Plus className="h-3 w-3 ml-2" />
// 			</Button>
// 		</div>
// 	);

// 	const isLoading = cryptoLoading || rateLoading;
// 	const isButtonDisabled =
// 		!fromCoin || !toCoin || !swapPrice || !amount || !formState.isValid;

// 	return (
// 		<div className="flex flex-col h-full">
// 			<div className="flex-1 p-6">
// 				{/* Source Crypto Selection */}
// 				<div className="flex flex-col items-center mb-8">
// 					<EnhancedSelect<ICryptoWallet>
// 						header={walletHeader}
// 						options={fromCryptoOptions}
// 						value={selectedFromCryptoOption}
// 						onChange={handleFromCryptoChange}
// 						placeholder="Select source cryptocurrency"
// 						isLoading={cryptoLoading}
// 						className="w-full mb-6"
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>From {option.value}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<div className="flex flex-col">
// 									<span>{option.label}</span>
// 									<span className="text-xs text-gray-500 group-hover:text-white">
// 										Balance:{" "}
// 										{formatAmount(
// 											option.raw?.balance,
// 											option.value,
// 										)}
// 									</span>
// 								</div>
// 							</div>
// 						)}
// 					/>

// 					{/* From Amount Input */}
// 					<div className="flex flex-col items-center w-full">
// 						<div className="flex items-end justify-center">
// 							<input
// 								type="text"
// 								inputMode="decimal"
// 								value={fromDisplay}
// 								onChange={(e) =>
// 									handleFromAmountChange(e.target.value)
// 								}
// 								onBlur={handleFromBlur}
// 								placeholder="0.00"
// 								disabled={!fromCoin}
// 								className={`text-6xl text-center bg-transparent outline-none ${
// 									formState.errors?.amount
// 										? "text-red-500"
// 										: ""
// 								} ${
// 									!fromCoin
// 										? "opacity-50 cursor-not-allowed"
// 										: ""
// 								}`}
// 							/>
// 							{/* <span className="text-2xl ml-2 mb-1">
// 								{fromCoin?.currency || ""}
// 							</span> */}
// 						</div>

// 						<div className="text-center mt-2 w-full">
// 							{cryptoLoading ? (
// 								<div className="flex items-center justify-center gap-2">
// 									<Loader2 className="h-4 w-4 animate-spin" />
// 									<span>Loading balance...</span>
// 								</div>
// 							) : fromCoin ? (
// 								<p className="text-sm text-gray-600">
// 									Available {fromCoin.currency} balance:{" "}
// 									{formatAmount(
// 										fromCoin.balance,
// 										fromCoin.currency,
// 									)}
// 								</p>
// 							) : (
// 								<div className="flex items-center justify-center gap-1 text-amber-500">
// 									<AlertCircle className="h-4 w-4" />
// 									<p className="text-sm">
// 										Please select a source cryptocurrency
// 									</p>
// 								</div>
// 							)}

// 							{formState.errors?.amount?.message ? (
// 								<p className="text-sm text-red-500 font-medium mt-1">
// 									{formState.errors.amount.message}
// 								</p>
// 							) : (
// 								swapPrice?.rate &&
// 								fromCoin &&
// 								toCoin && (
// 									<p className="text-sm text-gray-600 mt-1">
// 										1 {fromCoin.currency} ≈{" "}
// 										{formatAmount(
// 											swapPrice.rate,
// 											toCoin.currency,
// 											{ overrideMinDecimalPlaces: 4 },
// 										)}{" "}
// 										{toCoin.currency}
// 									</p>
// 								)
// 							)}
// 						</div>
// 					</div>
// 				</div>

// 				{/* Conversion Arrow */}
// 				<div className="flex justify-center my-6">
// 					<div
// 						className={`bg-amber-100 rounded-full p-2 ${
// 							!fromCoin || !toCoin ? "opacity-50" : ""
// 						}`}
// 					>
// 						<ArrowDataTransferVerticalIcon
// 							className={`h-6 w-6 ${
// 								fromCoin && toCoin
// 									? "text-amber-500"
// 									: "text-amber-300"
// 							}`}
// 						/>
// 					</div>
// 				</div>

// 				{/* Destination Crypto Selection */}
// 				<div className="flex flex-col items-center">
// 					{/* To Amount Input */}
// 					<div className="flex flex-col items-center w-full mb-6">
// 						<div className="flex items-end justify-center">
// 							<input
// 								type="text"
// 								inputMode="decimal"
// 								value={toDisplay}
// 								onChange={(e) =>
// 									handleToAmountChange(e.target.value)
// 								}
// 								onBlur={handleToBlur}
// 								placeholder="0.00"
// 								disabled={!toCoin || !fromCoin}
// 								className={`text-6xl text-center bg-transparent outline-none ${
// 									!toCoin || !fromCoin
// 										? "opacity-50 cursor-not-allowed"
// 										: ""
// 								}`}
// 							/>
// 							{/* <span className="text-2xl ml-2 mb-1">
// 								{toCoin?.currency || ""}
// 							</span> */}
// 						</div>

// 						{!toCoin && !cryptoLoading ? (
// 							<div className="flex items-center justify-center gap-1 text-amber-500 mt-1">
// 								<AlertCircle className="h-4 w-4" />
// 								<p className="text-sm">
// 									Please select a destination cryptocurrency
// 								</p>
// 							</div>
// 						) : (
// 							swapPrice?.rate &&
// 							fromCoin &&
// 							toCoin && (
// 								<p className="text-sm text-gray-600 mt-1">
// 									1 {toCoin.currency} ≈{" "}
// 									{formatAmount(
// 										1 / swapPrice.rate,
// 										fromCoin.currency,
// 										{ overrideMinDecimalPlaces: 4 },
// 									)}{" "}
// 									{fromCoin.currency}
// 								</p>
// 							)
// 						)}
// 					</div>

// 					<EnhancedSelect<ICryptoWallet>
// 						options={toCryptoOptions}
// 						value={selectedToCryptoOption}
// 						onChange={handleToCryptoChange}
// 						placeholder="Select destination cryptocurrency"
// 						isLoading={cryptoLoading}
// 						className="w-full mb-6"
// 						isSearchable={false}
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>To {option.value}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>{option.label}</span>
// 							</div>
// 						)}
// 					/>
// 				</div>
// 			</div>

// 			{/* Review Order Button */}
// 			<div className="p-6 border-t">
// 				<Button
// 					className="w-full h-14 rounded-full text-lg font-medium"
// 					disabled={isButtonDisabled || isLoading}
// 					onClick={handleSubmit(handleReviewOrder)}
// 				>
// 					{isLoading ? (
// 						<div className="flex items-center gap-2">
// 							<Loader2 className="h-5 w-5 animate-spin" />
// 							<span>Loading...</span>
// 						</div>
// 					) : !fromCoin ? (
// 						"Select a source cryptocurrency"
// 					) : !toCoin ? (
// 						"Select a destination cryptocurrency"
// 					) : (
// 						`Review Swap Order`
// 					)}
// 				</Button>
// 			</div>
// 		</div>
// 	);
// }


import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, AlertCircle, Plus } from "lucide-react";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { IOption } from "@/types/general";
import { formatAmount } from "@/utils/format-amount";
import { parseFormattedNumber } from "@/utils/parse-formatted-number";
import EnhancedSelect from "@/components/enhanced-select";
import { Button } from "@/components/custom/button";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { useGetSwapPrice } from "@/hooks/api/swaps";
import useSwapCryptoTransactionStore from "@/store/swap-crypto-store";
import useCryptoSelectionStore from "@/store/crypto-selection-store";
import { ArrowDataTransferVerticalIcon } from "..";

const swapSchema = z.object({
	amount: z.number().min(0, "Amount must be greater than 0"),
	to_amount: z.number().optional(),
});

type SwapFormValues = z.infer<typeof swapSchema>;

const CONVERSION_TYPE = "INSTANT SWAP CRYPTO TO CRYPTO";

interface SwapCryptoProps {
	onProceed: () => void;
}

export function SwapCrypto({ onProceed }: SwapCryptoProps) {
	const { setTransaction } = useSwapCryptoTransactionStore();
	const { selectedCrypto } = useCryptoSelectionStore();

	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
		useCryptoUserWallets();

	const form = useForm<SwapFormValues>({
		resolver: zodResolver(swapSchema),
		defaultValues: {
			amount: undefined,
			to_amount: undefined,
		},
		mode: "onChange",
	});

	const { watch, setValue, handleSubmit, formState, trigger } = form;
	const amount = watch("amount");
	const toAmount = watch("to_amount");

	const [fromCoin, setFromCoin] = useState<ICryptoWallet | null>(null);
	const [toCoin, setToCoin] = useState<ICryptoWallet | null>(null);
	
	// Auto/manual selection tracking for "from" crypto only
	const [manuallySelectedFromCrypto, setManuallySelectedFromCrypto] = useState<string | null>(null);
	const [lastStoreSelectionFrom, setLastStoreSelectionFrom] = useState<string | null>(null);

	const [fromDisplay, setFromDisplay] = useState("");
	const [toDisplay, setToDisplay] = useState("");
	const [activeField, setActiveField] = useState<"from" | "to">();

	form.register("amount", {
		validate: (value) => {
			if (value) {
				if (value > (fromCoin?.balance || 0))
					return `Insufficient balance`;
			}
			return true;
		},
	});

	const { data: swapPrice, isLoading: rateLoading } = useGetSwapPrice(
		fromCoin?.currency || "",
		toCoin?.currency || "",
		CONVERSION_TYPE,
	);

	const fromCryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
		(wallet) => ({
			id: `crypto-${wallet.currency}`,
			value: wallet.currency,
			label: wallet.currency,
			icon: wallet.image,
			raw: wallet,
		}),
	);

	const toCryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets
		.filter((wallet) => wallet.currency !== fromCoin?.currency)
		.map((wallet) => ({
			id: `crypto-${wallet.currency}`,
			value: wallet.currency,
			label: wallet.currency,
			icon: wallet.image,
			raw: wallet,
		}));

	const selectedFromCryptoOption =
		fromCryptoOptions.find(
			(option) => option.value === fromCoin?.currency,
		) || null;

	const selectedToCryptoOption =
		toCryptoOptions.find((option) => option.value === toCoin?.currency) ||
		null;

	// Auto-select "from" crypto from store when component mounts or when data changes
	useEffect(() => {
		if (cryptoWallets.length === 0) return;

		const currentStoreSelection = selectedCrypto?.currency || null;
		
		// Check if store selection has changed
		const storeSelectionChanged = currentStoreSelection !== lastStoreSelectionFrom;
		
		if (storeSelectionChanged) {
			setLastStoreSelectionFrom(currentStoreSelection);
			
			// If store has a new selection, use it (this allows store updates to work)
			if (currentStoreSelection) {
				const matchingWallet = cryptoWallets.find(
					wallet => wallet.currency === currentStoreSelection
				);
				
				if (matchingWallet) {
					setFromCoin(matchingWallet);
					// Clear manual selection since store provided a new selection
					setManuallySelectedFromCrypto(null);
					return;
				}
			}
		}

		// If we have a manual selection and store hasn't changed, respect manual selection
		if (manuallySelectedFromCrypto) {
			const manualWallet = cryptoWallets.find(
				wallet => wallet.currency === manuallySelectedFromCrypto
			);
			
			if (manualWallet) {
				setFromCoin(manualWallet);
				return;
			} else {
				// Manual selection is no longer available, clear it
				setManuallySelectedFromCrypto(null);
			}
		}

		// Fallback: if no manual selection and no store selection, use first available
		if (!fromCoin && cryptoWallets.length > 0) {
			setFromCoin(cryptoWallets[0]);
		}
	}, [selectedCrypto, cryptoWallets, manuallySelectedFromCrypto, lastStoreSelectionFrom, fromCoin]);

	// Handle conflicts when fromCoin and toCoin are the same
	useEffect(() => {
		if (fromCoin && toCoin && fromCoin.currency === toCoin.currency) {
			// If there's a conflict, clear the "to" selection
			setToCoin(null);
			setValue("to_amount", undefined);
			setToDisplay("");
		}
	}, [fromCoin, toCoin, setValue]);

	useEffect(() => {
		if (!swapPrice?.rate || !activeField) return;

		if (activeField === "from" && amount !== undefined) {
			const calculatedTo = amount * swapPrice.rate;
			setValue("to_amount", calculatedTo, { shouldValidate: true });
			setToDisplay(
				formatAmount(calculatedTo, toCoin?.currency, {
					rawFormat: true,
				}),
			);
		} else if (activeField === "to" && toAmount !== undefined) {
			const calculatedFrom = toAmount / swapPrice.rate;
			setValue("amount", calculatedFrom, { shouldValidate: true });
			setFromDisplay(
				formatAmount(calculatedFrom, fromCoin?.currency, {
					rawFormat: true,
				}),
			);
		}
	}, [
		amount,
		toAmount,
		swapPrice?.rate,
		activeField,
		setValue,
		toCoin,
		fromCoin,
	]);

	useEffect(() => {
		if (amount)
			setFromDisplay(
				formatAmount(amount, fromCoin?.currency, {
					rawFormat: true,
				}),
			);
		if (toAmount)
			setToDisplay(
				formatAmount(toAmount, toCoin?.currency, { rawFormat: true }),
			);
	}, []);

	useEffect(() => {
		if (swapPrice?.rate && activeField === "from" && amount) {
			const calculatedTo = amount * swapPrice.rate;
			setValue("to_amount", calculatedTo);
			setToDisplay(
				formatAmount(calculatedTo, toCoin?.currency, {
					rawFormat: true,
				}),
			);
		}
	}, [swapPrice?.rate, activeField, amount, setValue, toCoin]);

	const handleFromAmountChange = (text: string) => {
		if (!fromCoin) return;

		setActiveField("from");
		const parsed = parseFormattedNumber(text);
		setFromDisplay(text);
		setValue("amount", parsed);
		trigger("amount");
	};

	const handleToAmountChange = (text: string) => {
		if (!toCoin || !fromCoin) return;

		setActiveField("to");
		const parsed = parseFormattedNumber(text);
		setToDisplay(text);
		setValue("to_amount", parsed);
	};

	const handleFromBlur = () => {
		if (!fromCoin) return;

		setFromDisplay(
			formatAmount(amount, fromCoin?.currency, { rawFormat: true }),
		);
	};

	const handleToBlur = () => {
		if (!toCoin) return;

		setToDisplay(
			formatAmount(toAmount, toCoin?.currency, { rawFormat: true }),
		);
	};

	const handleFromCryptoChange = (option: IOption<ICryptoWallet> | null) => {
		if (!option) {
			setManuallySelectedFromCrypto(null);
			setFromCoin(null);
			return;
		}

		// Mark that user has manually selected a "from" crypto
		setManuallySelectedFromCrypto(option.value);

		if (option.raw) {
			setFromCoin(option.raw);
		} else {
			const selectedWallet = cryptoWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setFromCoin(selectedWallet || null);
		}

		setValue("amount", 0);
		setValue("to_amount", 0);
		setFromDisplay("");
		setToDisplay("");
		setActiveField("from");
	};

	const handleToCryptoChange = (option: IOption<ICryptoWallet> | null) => {
		if (!option) {
			setToCoin(null);
			return;
		}

		// Simple manual selection for "to" crypto - no auto-selection logic
		if (option.raw) {
			setToCoin(option.raw);
		} else {
			const selectedWallet = cryptoWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setToCoin(selectedWallet || null);
		}

		if (amount && fromCoin) {
			setActiveField("from");
			trigger("amount");
		}
	};

	const handleReviewOrder = () => {
		if (fromCoin && toCoin && swapPrice) {
			const payload = {
				from: fromCoin,
				to: toCoin,
				amount: Number(amount),
				method: CONVERSION_TYPE,
				price_data: swapPrice,
			};

			setTransaction(payload);

			onProceed();
		}
	};

	const handleAddNew = () => {};

	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	const isLoading = cryptoLoading || rateLoading;
	const isButtonDisabled =
		!fromCoin || !toCoin || !swapPrice || !amount || !formState.isValid;

	return (
		<div className="flex flex-col h-full">
			<div className="flex-1 p-6">
				{/* Source Crypto Selection */}
				<div className="flex flex-col items-center mb-8">
					<EnhancedSelect<ICryptoWallet>
						header={walletHeader}
						options={fromCryptoOptions}
						value={selectedFromCryptoOption}
						onChange={handleFromCryptoChange}
						placeholder="Select source cryptocurrency"
						isLoading={cryptoLoading}
						className="w-full mb-6"
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>From {option.value}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<div className="flex flex-col">
									<span>{option.label}</span>
									<span className="text-xs text-gray-500 group-hover:text-white">
										Balance:{" "}
										{formatAmount(
											option.raw?.balance,
											option.value,
										)}
									</span>
								</div>
							</div>
						)}
					/>

					{/* From Amount Input */}
					<div className="flex flex-col items-center w-full">
						<div className="flex items-end justify-center">
							<input
								type="text"
								inputMode="decimal"
								value={fromDisplay}
								onChange={(e) =>
									handleFromAmountChange(e.target.value)
								}
								onBlur={handleFromBlur}
								placeholder="0.00"
								disabled={!fromCoin}
								className={`text-6xl text-center bg-transparent outline-none ${
									formState.errors?.amount
										? "text-red-500"
										: ""
								} ${
									!fromCoin
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
						</div>

						<div className="text-center mt-2 w-full">
							{cryptoLoading ? (
								<div className="flex items-center justify-center gap-2">
									<Loader2 className="h-4 w-4 animate-spin" />
									<span>Loading balance...</span>
								</div>
							) : fromCoin ? (
								<p className="text-sm text-gray-600">
									Available {fromCoin.currency} balance:{" "}
									{formatAmount(
										fromCoin.balance,
										fromCoin.currency,
									)}
								</p>
							) : (
								<div className="flex items-center justify-center gap-1 text-amber-500">
									<AlertCircle className="h-4 w-4" />
									<p className="text-sm">
										Please select a source cryptocurrency
									</p>
								</div>
							)}

							{formState.errors?.amount?.message ? (
								<p className="text-sm text-red-500 font-medium mt-1">
									{formState.errors.amount.message}
								</p>
							) : (
								swapPrice?.rate &&
								fromCoin &&
								toCoin && (
									<p className="text-sm text-gray-600 mt-1">
										1 {fromCoin.currency} ≈{" "}
										{formatAmount(
											swapPrice.rate,
											toCoin.currency,
											{ overrideMinDecimalPlaces: 4 },
										)}{" "}
										{toCoin.currency}
									</p>
								)
							)}
						</div>
					</div>
				</div>

				{/* Conversion Arrow */}
				<div className="flex justify-center my-6">
					<div
						className={`bg-amber-100 rounded-full p-2 ${
							!fromCoin || !toCoin ? "opacity-50" : ""
						}`}
					>
						<ArrowDataTransferVerticalIcon
							className={`h-6 w-6 ${
								fromCoin && toCoin
									? "text-amber-500"
									: "text-amber-300"
							}`}
						/>
					</div>
				</div>

				{/* Destination Crypto Selection */}
				<div className="flex flex-col items-center">
					{/* To Amount Input */}
					<div className="flex flex-col items-center w-full mb-6">
						<div className="flex items-end justify-center">
							<input
								type="text"
								inputMode="decimal"
								value={toDisplay}
								onChange={(e) =>
									handleToAmountChange(e.target.value)
								}
								onBlur={handleToBlur}
								placeholder="0.00"
								disabled={!toCoin || !fromCoin}
								className={`text-6xl text-center bg-transparent outline-none ${
									!toCoin || !fromCoin
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
						</div>

						{!toCoin && !cryptoLoading ? (
							<div className="flex items-center justify-center gap-1 text-amber-500 mt-1">
								<AlertCircle className="h-4 w-4" />
								<p className="text-sm">
									Please select a destination cryptocurrency
								</p>
							</div>
						) : (
							swapPrice?.rate &&
							fromCoin &&
							toCoin && (
								<p className="text-sm text-gray-600 mt-1">
									1 {toCoin.currency} ≈{" "}
									{formatAmount(
										1 / swapPrice.rate,
										fromCoin.currency,
										{ overrideMinDecimalPlaces: 4 },
									)}{" "}
									{fromCoin.currency}
								</p>
							)
						)}
					</div>

					<EnhancedSelect<ICryptoWallet>
						options={toCryptoOptions}
						value={selectedToCryptoOption}
						onChange={handleToCryptoChange}
						placeholder="Select destination cryptocurrency"
						isLoading={cryptoLoading}
						className="w-full mb-6"
						isSearchable={false}
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>To {option.value}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>{option.label}</span>
							</div>
						)}
					/>
				</div>
			</div>

			{/* Review Order Button */}
			<div className="p-6 border-t">
				<Button
					className="w-full h-14 rounded-full text-lg font-medium"
					disabled={isButtonDisabled || isLoading}
					onClick={handleSubmit(handleReviewOrder)}
				>
					{isLoading ? (
						<div className="flex items-center gap-2">
							<Loader2 className="h-5 w-5 animate-spin" />
							<span>Loading...</span>
						</div>
					) : !fromCoin ? (
						"Select a source cryptocurrency"
					) : !toCoin ? (
						"Select a destination cryptocurrency"
					) : (
						`Review Swap Order`
					)}
				</Button>
			</div>
		</div>
	);
}