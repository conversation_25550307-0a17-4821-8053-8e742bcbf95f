import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import useBuyCryptoTransactionStore from "@/store/buy-crypto-store";
import { formatAmount } from "@/utils/format-amount";
import { format } from "date-fns/format";

interface ReceiptStepProps {
	onShareReceipt: () => void;
}

export function ReceiptStep({ onShareReceipt }: ReceiptStepProps) {
	const { transaction } = useBuyCryptoTransactionStore();

	if (!transaction) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<p className="text-gray-500">No transaction data available</p>
			</div>
		);
	}

	return (
		<div className="flex flex-col p-6 space-y-6">
			<motion.div
				className="text-center mb-2"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
			>
				<p className="text-gray-600">
					This may take up to 2 mins to confirm transaction
				</p>
				<div className="inline-block bg-green-100 text-green-800 px-4 py-1 rounded-full mt-2">
					Successful
				</div>
			</motion.div>

			{/* Amount Display */}
			<motion.div
				className="text-center my-4"
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ delay: 0.2 }}
			>
				<h2 className="text-4xl font-bold">{transaction?.amount}</h2>
			</motion.div>

			{/* Transaction Details */}
			<motion.div
				className="space-y-4"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3, staggerChildren: 0.05 }}
			>
				<div className="flex justify-between">
					<span className="text-gray-500">Coin</span>
					<span className="font-medium">
						{transaction?.to.currency}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Rate</span>
					<span className="font-medium">{`1 ${
						transaction?.from.currency
					} ≈ ${formatAmount(
						transaction?.price_data.rate,
						transaction?.to.currency,
						{ overrideMinDecimalPlaces: 4 },
					)} ${transaction?.to.currency}`}</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Amount</span>
					<span className="font-medium">{`${formatAmount(
						Number(transaction?.amount) *
							transaction?.price_data?.rate,
						transaction?.to.currency,
						{ overrideMinDecimalPlaces: 4 },
					)} ${transaction?.to.currency}`}</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Fee</span>
					<span className="font-medium">{`${formatAmount(
						0.********,
						transaction.to.currency,
						{
							overrideMinDecimalPlaces: 8,
						},
					)} ${transaction.to.currency}`}</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Payment</span>
					<span className="font-medium">
						{transaction.from.currency} Fiat Account
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Date</span>
					<span className="font-medium">
						{format(new Date(), "dd-MM-yyyy | HH:mm:ss")}
					</span>
				</div>
			</motion.div>

			{/* Share Button */}
			<motion.div
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.5 }}
			>
				<Button
					className="text-white rounded-full py-6 mt-8 w-full"
					onClick={onShareReceipt}
				>
					Share Receipt
				</Button>
			</motion.div>
		</div>
	);
}
