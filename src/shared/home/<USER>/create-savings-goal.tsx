import { useF<PERSON>, Controller, FieldErrors } from "react-hook-form";
import { But<PERSON> } from "@/components/custom/button";
import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import { useCreateFiatSavingGoal } from "@/hooks/api/fiat-savings";
import { useCreateCryptoSavingGoal } from "@/hooks/api/crypto-savings";
import { CreateFiatSavingGoalPayload, SavingsGoal } from "@/types/fiat-savings";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useEffect, useState } from "react";
import { formatAmount } from "@/utils/format-amount";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import EnhancedSelect, { IOption } from "@/components/enhanced-select";
import { SavingsSuccess } from "@/shared/home/<USER>/savings-success";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import { useSavingsStore } from "@/store/useGoalStore";

interface CreateGoal {
    onClose: () => void;
    onGoalCreated: (goal: EnrichedSavingsGoal) => void;
}

export interface EnrichedSavingsGoal extends SavingsGoal {
    fundingSource: string;
    accountName: string;
}

export const CreateSavingsGoalForm = ({
    onClose,
    onGoalCreated,
}: CreateGoal) => {
    const { mutate: createFiatGoal, isPending: isFiatSaving } = useCreateFiatSavingGoal();
    const { mutate: createCryptoGoal, isPending: isCryptoSaving } = useCreateCryptoSavingGoal();
    const { data: fiatWallets = [] } = useFiatUserWallets();
    const { data: cryptoWallets = [] } = useCryptoUserWallets();
    const [showSuccess, setShowSuccess] = useState(false);
    const [localGoal, setLocalGoal] = useState<EnrichedSavingsGoal | null>(null);

    const setCreatedGoal = useSavingsStore((state) => state.setCreatedGoal);

    const {
        control,
        handleSubmit,
        watch,
        setValue,
        // formState: {},
    } = useForm<CreateFiatSavingGoalPayload & { funding_source_type: string }>({
        defaultValues: {
            title: "",
            startDate: "",
            endDate: "",
            currency: "",
            targetAmount: 0,
            wallet_id: "",
            funding_source_type: "",
        },
        mode: "onSubmit",
        reValidateMode: "onChange",
    });

    const rawFundingSource = watch("funding_source_type");
    const selectedFundingSource: "fiat" | "crypto" | undefined =
        typeof rawFundingSource === "string" && (rawFundingSource === "fiat" || rawFundingSource === "crypto")
            ? rawFundingSource
            : undefined;

    useEffect(() => {
        setValue("wallet_id", "");
    }, [selectedFundingSource, setValue]);

    const onSubmit = (data: CreateFiatSavingGoalPayload & { funding_source_type: string }) => {
        const { funding_source_type, ...restOfData } = data;
        const payloadForApi = {
            ...restOfData,
            targetAmount: Number(restOfData.targetAmount),
        };

        type ApiResponse = { savingsGoal?: SavingsGoal; data?: SavingsGoal };
        const handleSuccess = (response: ApiResponse) => {
            const goal: SavingsGoal = response.savingsGoal || response.data;

            if (goal?.id) {
                const fundingSourceOption = enhancedFundingSourceOptions.find(
                    (opt) => opt.value === rawFundingSource
                );
                const accountOption = accountOptions.find(
                    (opt) => opt.value === goal.wallet_id
                );

                const enrichedGoal: EnrichedSavingsGoal = {
                    ...goal,
                    fundingSource: fundingSourceOption?.label || "",
                    accountName: accountOption?.label || "",
                };
                setCreatedGoal(enrichedGoal);
                setShowSuccess(true);
                setLocalGoal(enrichedGoal);
            } else {
                notify("Something went wrong", ResponseStatus.ERROR);
            }
        };

        if (funding_source_type === "crypto") {
            createCryptoGoal(payloadForApi, { onSuccess: handleSuccess });
        } else {
            createFiatGoal(payloadForApi, { onSuccess: handleSuccess });
        }
    };

    const onInvalid = (errors: FieldErrors<CreateFiatSavingGoalPayload>) => {
        console.error("Form validation errors:", errors);
        notify("Please fill all required fields correctly", ResponseStatus.ERROR);
    };

    const fundingSourceOptionsOriginal: IOption<string>[] = [
        { id: "fiat", label: "Fiat Account", value: "fiat" },
        { id: "crypto", label: "Crypto Wallet", value: "crypto" },
    ];

    const enhancedFundingSourceOptions: IOption<string>[] = fundingSourceOptionsOriginal.map((option) => ({
        id: option.id,
        label: String(option.label),
        value: option.value,
    }));

    // Define wallet types for better type safety
    type FiatWallet = typeof fiatWallets extends (infer U)[] ? U : never;
    type CryptoWallet = typeof cryptoWallets extends (infer U)[] ? U : never;
    type Wallet = FiatWallet | CryptoWallet;

    const fiatAccountOptions: IOption<FiatWallet>[] = fiatWallets
        .filter(Boolean)
        .map((wallet) => ({
            id: `fiat-${wallet.id}`,
            value: wallet.id,
            label: wallet.currency_name,
            icon: wallet.image,
            raw: wallet,
        }));

    const cryptoAccountOptions: IOption<CryptoWallet>[] = cryptoWallets
        .filter(Boolean)
        .map((wallet) => ({
            id: `crypto-${wallet.id}`,
            value: wallet.id,
            label: wallet.currency + " Wallet",
            icon: wallet.image,
            raw: wallet,
        }));

    const accountOptions: IOption<Wallet>[] =
        selectedFundingSource === "fiat"
            ? (fiatAccountOptions as IOption<Wallet>[])
            : selectedFundingSource === "crypto"
            ? (cryptoAccountOptions as IOption<Wallet>[])
            : [];

    const fiatCurrencyOptions = fiatWallets.map((wallet) => ({
        id: wallet.currency,
        label: wallet.currency,
        value: wallet.currency,
        icon: wallet.image || undefined,
    }));
    const cryptoCurrencyOptions = cryptoWallets.map((wallet) => ({
        id: wallet.currency,
        label: wallet.currency,
        value: wallet.currency,
        icon: wallet.image || undefined,
    }));
    const dynamicCurrencyOptions =
        selectedFundingSource === "fiat"
            ? fiatCurrencyOptions
            : selectedFundingSource === "crypto"
            ? cryptoCurrencyOptions
            : [];

    const isSaving = isFiatSaving || isCryptoSaving;

    if (showSuccess && localGoal) {
        return (
            <SavingsSuccess
                goalName={localGoal.title}
                goalId={localGoal.id}
                onViewGoal={() => {
                    onClose();
                    onGoalCreated(localGoal);
                }}
            />
        );
    }

    return (
        <div className="min-h-screen flex flex-col items-center justify-center relative">
            <button
                onClick={onClose}
                className="absolute top-8 right-8 z-10 w-10 h-10 flex items-center justify-center rounded-full border-2 border-primary bg-background dark:bg-card shadow-sm hover:bg-orange-50 dark:hover:bg-muted transition-colors"
            >
                <X className="h-6 w-6 text-primary" />
            </button>
            <div className="w-full max-w-md bg-background dark:bg-card rounded-2xl px-8 py-8 flex flex-col items-center">
                <h2 className="text-2xl font-bold text-center mb-1 text-foreground">
                    Create Savings Goal
                </h2>
                <p className="text-muted-foreground text-center mb-6 text-base">
                    Create a savings goal and build wealth easily.
                </p>
                <form
                    onSubmit={handleSubmit(onSubmit, onInvalid)}
                    className="w-full flex flex-col gap-4 overflow-y-auto max-h-[70vh] pr-2 hide-scrollbar"
                >
                    <Controller
                        name="title"
                        control={control}
                        rules={{
                            required: "Please enter a title for your savings goal",
                            minLength: {
                                value: 3,
                                message: "Title must be at least 3 characters long",
                            },
                        }}
                        render={({ field, fieldState: { error } }) => (
                            <FloatingLabelInput
                                label="Title of Savings Goal"
                                placeholder=""
                                {...field}
                                error={error?.message}
                                className="w-full h-[48px] rounded-full bg-background dark:bg-input border border-border px-4 text-base text-foreground"
                            />
                        )}
                    />

                    <Controller
                        name="startDate"
                        control={control}
                        rules={{ required: "Start date is required" }}
                        render={({ field }) => (
                            <DatePicker
                                selected={field.value ? new Date(field.value) : null}
                                onChange={(date: Date) => field.onChange(date?.toISOString())}
                                minDate={new Date()}
                                placeholderText="Select start date"
                                className="w-full h-[48px] rounded-full border border-border px-4 bg-backgrounf dark:bg-input text-base text-foreground"
                                dateFormat="yyyy-MM-dd"
                            />
                        )}
                    />

                    <Controller
                        name="endDate"
                        control={control}
                        rules={{ required: "End date is required" }}
                        render={({ field }) => (
                            <DatePicker
                                selected={field.value ? new Date(field.value) : null}
                                onChange={(date: Date) => field.onChange(date?.toISOString())}
                                minDate={watch("startDate") ? new Date(watch("startDate")) : new Date()}
                                placeholderText="Select end date"
                                className="w-full h-[48px] rounded-full border border-border px-4 bg-background dark:bg-input text-base text-foreground"
                                dateFormat="yyyy-MM-dd"
                            />
                        )}
                    />

                    <Controller
                        name="currency"
                        control={control}
                        rules={{ required: "Currency is required" }}
                        render={({ field }) => (
                            <EnhancedSelect
                                {...field}
                                value={dynamicCurrencyOptions.find((option) => option.value === field.value) || null}
                                onChange={(selectedOption) => field.onChange(selectedOption?.value)}
                                options={dynamicCurrencyOptions}
                                placeholder="Select Currency"
                                className="w-full h-full rounded-full border border-border px-4 bg-background dark:bg-input text-base text-foreground"
                            />
                        )}
                    />

                    <Controller
                        name="targetAmount"
                        control={control}
                        rules={{
                            required: "Amount is required",
                            validate: {
                                positive: (value) => Number(value) > 0 || "Amount must be positive",
                            },
                        }}
                        render={({ field, fieldState: { error } }) => (
                            <FloatingLabelInput
                                label="Enter Target Amount"
                                type="number"
                                min="1"
                                step="0.01"
                                inputMode="decimal"
                                placeholder=" "
                                {...field}
                                className={cn(
                                    "border border-border focus:outline-none w-full h-[48px] rounded-full bg-background dark:bg-input px-4 text-base text-foreground",
                                    error && "border-red-500 focus:ring-red-200 focus:border-red-500"
                                )}
                                error={error?.message}
                            />
                        )}
                    />

                    <Controller
                        name="funding_source_type"
                        control={control}
                        rules={{ required: "Please select a funding source" }}
                        render={({ field, fieldState: { error } }) => (
                            <div>
                                <EnhancedSelect
                                    options={enhancedFundingSourceOptions}
                                    value={
                                        enhancedFundingSourceOptions.find(
                                            (option) => option.value === field.value
                                        ) || null
                                    }
                                    onChange={(selectedOption) =>
                                        field.onChange(selectedOption ? selectedOption.value : "")
                                    }
                                    placeholder="Choose Funding Source"
                                    className="w-full"
                                    displayClassName="h-[48px] rounded-full bg-background dark:bg-input text-base text-foreground border border-border"
                                    isSearchable={false}
                                    isClearable={false}
                                />
                                {error && (
                                    <p className="text-sm text-red-500 mt-1 pl-4">
                                        {error.message}
                                    </p>
                                )}
                            </div>
                        )}
                    />

                    {selectedFundingSource && (
                        <Controller
                            name="wallet_id"
                            control={control}
                            rules={{ required: "Please select an account" }}
                            render={({ field }) => {
                                if (accountOptions.length === 0 && selectedFundingSource) {
                                    return (
                                        <p className="text-sm text-muted-foreground mt-1 pl-4">
                                            No{" "}
                                            {selectedFundingSource === "fiat"
                                                ? "fiat accounts"
                                                : "crypto wallets"}{" "}
                                            available for the selected funding source.
                                        </p>
                                    );
                                }
                                return (
                                    <EnhancedSelect
                                        options={accountOptions}
                                        value={
                                            accountOptions.find(
                                                (option) => option.value === field.value
                                            ) || null
                                        }
                                        onChange={(option) =>
                                            field.onChange(option ? option.value : "")
                                        }
                                        placeholder={
                                            selectedFundingSource === "fiat"
                                                ? "Choose Fiat Account"
                                                : "Choose Crypto Wallet"
                                        }
                                        className="w-full"
                                        displayClassName="h-[48px] rounded-full border border-border bg-background dark:bg-input text-base text-foreground"
                                        isSearchable={false}
                                        isClearable={false}
                                        renderSelected={(option) => (
                                            <div className="flex items-center gap-2">
                                                {option.icon && (
                                                    <img
                                                        src={String(option.icon)}
                                                        alt={String(option.label)}
                                                        className="w-6 h-6 rounded-full"
                                                    />
                                                )}
                                                <span>{option.label}</span>
                                            </div>
                                        )}
                                        renderOption={(option) => (
                                            <div className="flex items-center gap-2 w-full">
                                                {option.icon && (
                                                    <img
                                                        src={String(option.icon)}
                                                        alt={String(option.label)}
                                                        className="w-6 h-6 rounded-full"
                                                    />
                                                )}
                                                <span>{option.label}</span>
                                                {option.raw && "available_balance" in option.raw && (
                                                    <span className="ml-auto text-base font-medium text-foreground">
                                                        {formatAmount(
                                                            option.raw.available_balance,
                                                            option.raw.currency
                                                        )}
                                                    </span>
                                                )}
                                            </div>
                                        )}
                                    />
                                );
                            }}
                        />
                    )}

                    <Button
                        type="submit"
                        disabled={isSaving}
                        className="w-full h-[60px] p-3 mt-2 rounded-full bg-primary text-primary-foreground font-semibold text-base shadow-sm hover:bg-primary/90 transition-colors"
                    >
                        {isSaving ? "Creating..." : "Create Savings Goal"}
                    </Button>
                </form>
            </div>
        </div>
    );
};
