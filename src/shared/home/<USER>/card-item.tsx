import whitecard from "@/assets/images/whitecard.png";
import blackcard from "@/assets/images/blackcard.png";
import yellowcard from "@/assets/images/yellowcard.png";
import { useCardColor } from "@/store/create-card-store";
import type { ICard } from "@/types/cards";

const overlayStyle = `
.whitecard-overlay {
  position: absolute;
  font-weight: 300;
  font-size: 0.875rem;
  pointer-events: none;
  letter-spacing: 0.5px;
}
`;

const firstname = localStorage.getItem("firstName");
const lastname = localStorage.getItem("lastName");

const cardDesigns = {
	white: whitecard,
	black: blackcard,
	yellow: yellowcard,
};

const overlayBackground = {
	white: { backgroundColor: "#f3f4f6" }, // bg-gray-100
	black: { backgroundColor: "#1f2937" }, // bg-gray-800
	yellow: { backgroundColor: "#de9801" }, // bg-[#de9801]
};

const textColors = {
	white: { color: "#111827" }, // text-gray-900
	black: { color: "#f3f4f6" }, // text-gray-100
	yellow: { color: "#f3f4f6" }, // text-gray-100
};

export const Card = ({
	item,
	className = "",
}: {
	item?: ICard;
	className?: string;
}) => {
	const { currentColor } = useCardColor();

	const currentBg =
		overlayBackground[currentColor] || overlayBackground.white;
	const currentTextColor = textColors[currentColor] || textColors.white;
	const currentCardDesign = cardDesigns[currentColor] || whitecard;

	return (
		<div className={`relative rounded-xl overflow-auto ${className}`}>
			<style>{overlayStyle}</style>
			<img
				src={currentCardDesign}
				alt="Card background"
				className="w-full h-full object-cover"
			/>
			<div className="absolute inset-0">
				<p
					className="whitecard-overlay text-center min-w-30 rounded px-2"
					style={{
						top: 73,
						left: 20,
						...currentBg,
						...currentTextColor,
					}}
				>
					{firstname} {lastname}
				</p>
				<p
					className="whitecard-overlay text-center min-w-38 rounded px-2"
					style={{
						bottom: 70,
						left: 20,
						...currentBg,
						...currentTextColor,
					}}
				>
					{item?.masked_pan || "•••• •••• •••• ••••"}
				</p>
			</div>
		</div>
	);
};
