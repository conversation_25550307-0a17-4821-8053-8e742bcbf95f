import { ChevronLeft } from "lucide-react";
import TransactionItem from "../transaction-item";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { X } from "lucide-react";
import html2canvas from "html2canvas";
import { ICard } from "@/types/cards";

interface Transaction {
	id: number;
	type: string;
	time: string;
	date: string;
	account: string;
	amount: string;
	status: "Successful" | "Failed" | "Processing" | "Pending";
}

interface ViewTransactionSectionProps {
	onBack: () => void;
	card : ICard;
}

const mockTransactionsData: Transaction[] = [
	{
		id: 1,
		type: "POS Payment",
		time: "10:30 AM",
		date: "12/05/2024",
		account: "",
		amount: "NGN 12,000.00",
		status: "Successful",
	},
	{
		id: 2,
		type: "ATM Withdrawal",
		time: "10:30 AM",
		date: "12/05/2024",
		account: "",
		amount: "NGN 12,000.00",
		status: "Failed",
	},
	{
		id: 3,
		type: "Fund Card",
		time: "10:30 AM",
		date: "12/05/2024",
		account: "",
		amount: "NGN 12,000.00",
		status: "Successful",
	},
	{
		id: 4,
		type: "Fund Wallet",
		time: "10:30 AM",
		date: "12/05/2024",
		account: "",
		amount: "NGN 12,000.00",
		status: "Pending",
	},
	{
		id: 5,
		type: "Fund Wallet",
		time: "10:30 AM",
		date: "11/05/2024",
		account: "",
		amount: "NGN 12,000.00",
		status: "Successful",
	},
	{
		id: 6,
		type: "Online Shopping",
		time: "03:45 PM",
		date: "10/05/2024",
		account: "",
		amount: "NGN 5,500.00",
		status: "Successful",
	},
];

export const ViewTransactionSection = ({
	onBack
}: ViewTransactionSectionProps) => {

// 	const {data:transactions} = useGetCardTransactions(card.id);
// console.log("Transactions data:", transactions);
	const { openDrawer, closeDrawer } = useDrawer();

	const handleTransactionClick = (transaction: Transaction) => {
		let receiptRef: HTMLDivElement | null = null;

		const handleShare = async () => {
			console.log("Share button clicked, receiptRef:", receiptRef);
			if (!receiptRef) {
				console.error("Receipt ref not found");
				return;
			}

			try {
				console.log("Starting html2canvas...");
				const canvas = await html2canvas(receiptRef, {
					backgroundColor: "#fff",
					useCORS: true,
					allowTaint: true,
					scale: 2, 
				});
				console.log("Canvas created:", canvas);

				canvas.toBlob(
					async (blob) => {
						if (!blob) {
							console.error("Failed to create blob");
							return;
						}
						console.log("Blob created:", blob);

						const file = new File([blob], "receipt.jpg", {
							type: "image/jpeg",
						});

						if (
							navigator.canShare &&
							navigator.canShare({ files: [file] })
						) {
							try {
								await navigator.share({
									files: [file],
									title: "Transaction Receipt",
								});
								console.log("Shared successfully");
							} catch (error) {
								console.log(
									"Share cancelled or failed:",
									error,
								);
								downloadImage(blob);
							}
						} else {
							console.log(
								"Web Share API not supported, downloading...",
							);
							downloadImage(blob);
						}
					},
					"image/jpeg",
					0.95,
				);
			} catch (error) {
				console.error("Error creating canvas:", error);
			}
		};

		const downloadImage = (blob: Blob) => {
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = "receipt.jpg";
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
			URL.revokeObjectURL(url);
			console.log("Image downloaded");
		};

		openDrawer({
			view: (
				<div
					ref={(el) => {
						receiptRef = el;
					}}
					id="transaction-receipt-share"
					className="relative rounded-xl shadow-lg p-6 w-[350px] bg-card text-foreground md:w-[420px] flex flex-col min-h-[600px] max-w-full"
					// style={{
					// 	backgroundColor: "#ffffff",
					// 	color: "#1f2937",
					// }}
				>
					<div className="flex justify-between items-center mb-2">
						<h4
							className="text-xl text-foregrounf font-semibold"
							// style={{ color: "#1f2937" }}
						>
							Transaction Receipt
						</h4>
						<button
							onClick={() => closeDrawer()}
							className="text-2xl rounded-full w-8 h-8 flex items-center justify-center transition-colors"
							style={{
								color: "#de9801",
								border: "1px solid #de9801",
								backgroundColor: "transparent",
							}}
							aria-label="Close"
						>
							<X className="w-5 h-5" />
						</button>
					</div>

					<div className="flex flex-col items-center mt-2 mb-4">
						<span
							className="text-xs font-medium px-4 py-1 rounded-full mb-2"
							style={{
								backgroundColor: "#dcfce7",
								color: "#16a34a",
							}}
						>
							{transaction.status}
						</span>
						<span
							className="text-4xl text-foreground font-bold mb-2"
							// style={{ color: "#1f2937" }}
						>
							{transaction.amount}
						</span>
					</div>

					<div
						className="flex flex-col gap-2 text-foreground text-[17px] mb-6"
						// style={{ color: "#374151" }}
					>
						<div className="flex justify-between w-full">
							<span className="font-medium">Type</span>
							<span>{transaction.type}</span>
						</div>
						<div className="flex justify-between w-full">
							<span className="font-medium">Date</span>
							<span>{transaction.date}</span>
						</div>
						<div className="flex justify-between w-full">
							<span className="font-medium">Time</span>
							<span>{transaction.time}</span>
						</div>
						<div className="flex justify-between w-full">
							<span className="font-medium">Account</span>
							<span>{transaction.account || "N/A"}</span>
						</div>
						<div className="flex justify-between w-full">
							<span className="font-medium">Amount</span>
							<span>{transaction.amount}</span>
						</div>
						<div className="flex justify-between w-full">
							<span className="font-medium">Status</span>
							<span>{transaction.status}</span>
						</div>
					</div>

					<div className="mt-auto">
						<button
							className="w-full text-lg font-medium py-3 rounded-full transition-colors shadow-md"
							style={{
								backgroundColor: "#de9801",
								color: "#ffffff",
							}}
							onClick={handleShare}
						>
							Share Receipt
						</button>
					</div>
				</div>
			),
			placement: "right",
			customSize: "430px",
		});
	};

	return (
		<div className="container mx-auto p-4 md:p-6">
			<div className="flex items-center gap-3 mb-6">
				<button
					onClick={onBack}
					className="p-2 rounded-full cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
				>
					<ChevronLeft className="w-6 h-6 text-primary" />
				</button>
				<h3 className="text-xl md:text-2xl font-bold text-foreground">
					Card Transactions
				</h3>
			</div>

			{/* Transaction List */}
			<div className="space-y-2">
				{mockTransactionsData.length > 0 ? (
					mockTransactionsData.map((transaction) => (
						<div
							key={transaction.id}
							onClick={() => handleTransactionClick(transaction)}
							className="cursor-pointer border rounded-md shadown-sm"
						>
							<TransactionItem transaction={transaction} />
						</div>
					))
				) : (
					<p className="p-6 text-center text-muted-foreground">
						You dont have any transactions for this card yet
					</p>
				)}
			</div>
		</div>
	);
};
