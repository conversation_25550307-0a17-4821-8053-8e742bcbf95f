
import { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { X } from "lucide-react";
import { useFundSavingsGoalStore } from "@/store/useGoalStore";
import { useFundSavingsManual } from "@/hooks/api/crypto-savings";
import { useFundFiatSavingsManual } from "@/hooks/api/fiat-savings";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { FundSavingsAmountForm } from "./fund-savings-amount-form";
import { EnterTransactionPin } from "./enter-transaction-pin";
import { FundSavingsSuccess } from "./fund-savings-success";
import { SavingsDetails } from "@/types/fiat-savings";
import { IOption } from "@/components/enhanced-select";
import { IFiatWallet } from "@/types/fiat-wallets";
import { ICryptoWallet } from "@/types/crypto-wallets";

interface FundSavingsGoalFlowProps {
  goal: SavingsDetails;
}

export const FundSavingsGoalFlow = ({ goal }: FundSavingsGoalFlowProps) => {
  const { closeDrawer } = useDrawer();
  const { currentStep, amount, fundingSource, selectedAccount, setStep, reset } = useFundSavingsGoalStore();
  
  const fundCryptoMutation = useFundSavingsManual();
  const fundFiatMutation = useFundFiatSavingsManual();
  
  const { data: fiatWallets = [] } = useFiatUserWallets();
  const { data: cryptoWallets = [] } = useCryptoUserWallets();
  const fiatAccounts: IOption<string>[] = fiatWallets.map((wallet: IFiatWallet) => ({
    id: wallet.id,
    label: `${wallet.currency} - ${wallet.available_balance}`,
    value: wallet.id,
  }));

  const cryptoAccounts: IOption<string>[] = cryptoWallets.map((wallet: ICryptoWallet) => ({
    id: wallet.id,
    label: `${wallet.currency} - ${wallet.balance}`,
    value: wallet.id,
  }));

  const handleClose = () => {
    reset();
    closeDrawer();
  };

  const handlePinSubmit = (pin: string) => {
    if (!amount || !fundingSource || !selectedAccount) return;

    const fundingPayload = {
      user_id:goal.user_id,
      savingsGoalId: goal.id,
      amount:amount

     
    };

    if (fundingSource === "crypto") {
      fundCryptoMutation.mutate(fundingPayload);
    } else {
      fundFiatMutation.mutate(fundingPayload);
    }
  };

  // Handle mutation success
  useEffect(() => {
    if (fundCryptoMutation.isSuccess || fundFiatMutation.isSuccess) {
      setStep("success");
    }
  }, [fundCryptoMutation.isSuccess, fundFiatMutation.isSuccess, setStep]);

  // // Handle mutation errors
  // useEffect(() => {
  //   if (fundCryptoMutation.isError || fundFiatMutation.isError) {
  //     // Error handling is done in the mutation hooks via notify
  //     // You could add additional error handling here if needed
  //   }
  // }, [fundCryptoMutation.isError, fundFiatMutation.isError]);

  // Animation variants
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? "100%" : "-100%",
      opacity: 0,
    }),
    center: {
      x: "0%",
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? "100%" : "-100%",
      opacity: 0,
    }),
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h1 className="text-lg font-semibold text-foreground">Fund Savings Goal</h1>
       <Button
					variant="ghost"
					size="icon"
					onClick={handleClose}
					className="ml-auto border-2 border-primary rounded-full mb-10 cursor-pointer"
				>
					<X className="size-6 text-primary" />
				</Button>
      </div>

      {/* Content */}
      <div className="flex-1 relative overflow-hidden">
        <AnimatePresence mode="wait" custom={0}>
          {currentStep === "amount" && (
            <motion.div
              key="amount"
              custom={0}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="absolute inset-0 w-full h-full"
            >
              <FundSavingsAmountForm
                goal={goal}
                fiatAccounts={fiatAccounts}
                cryptoAccounts={cryptoAccounts}
              />
            </motion.div>
          )}

          {currentStep === "pin" && (
            <motion.div
              key="pin"
              custom={1}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="absolute inset-0 w-full h-full"
            >
              <EnterTransactionPin
                title="Enter Transaction PIN"
                buttonText="Fund Savings Goal"
                onComplete={handlePinSubmit}
                loading={fundCryptoMutation.isPending || fundFiatMutation.isPending}
                error={
                  fundCryptoMutation.error?.message || 
                  fundFiatMutation.error?.message
                }
              />
            </motion.div>
          )}

          {currentStep === "success" && (
            <motion.div
              key="success"
              custom={1}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="absolute inset-0 w-full h-full"
            >
              <FundSavingsSuccess
                amount={amount || 0}
                currency={goal.currency}
                goalName={goal.title}
                onViewReceipt={() => setStep("receipt")}
                onClose={handleClose}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};