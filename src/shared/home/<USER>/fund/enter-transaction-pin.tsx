import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";

export interface PinSteps {
  onPinVerified: () => void;
}

export interface EnterTransactionPinProps {
  title?: string;
  buttonText?: string;
  onComplete: (pin: string) => void;
  onBack?: () => void;
  loading?: boolean;
  error?: string;
}

export const EnterTransactionPin: React.FC<EnterTransactionPinProps> = ({
  title = "Input transaction PIN to fund savings goal",
  buttonText = "Submit",
  onComplete,
  onBack,
  loading = false,
  error,
}) => {
  const [pin, setPin] = useState<string>("");

  const handleSubmit = () => {
    if (pin.length >= 4) {
      onComplete(pin);
    }
  };  return (
    <div className="p-6 flex flex-col items-center gap-6">
      <div className="w-full flex items-center justify-between">
        {onBack && (
          <Button
            variant="ghost"
            onClick={onBack}
            className="h-8 w-8 rounded-full text-primary"
          >
            ←
          </Button>
        )}
        {title && <h2 className="text-xl font-semibold flex-1 text-center">{title}</h2>}
        <div className="w-8" /> {/* Spacer for alignment */}
      </div>
      <div className="flex justify-center">
        <InputOTP
          value={pin}
          onChange={setPin}
          maxLength={4}
          containerClassName="gap-3"
        >
          <InputOTPGroup>
            <InputOTPSlot index={0} className="w-12 h-12 text-center text-lg border border-border rounded-md" />
            <InputOTPSlot index={1} className="w-12 h-12 text-center text-lg border border-border rounded-md" />
            <InputOTPSlot index={2} className="w-12 h-12 text-center text-lg border border-border rounded-md" />
            <InputOTPSlot index={3} className="w-12 h-12 text-center text-lg border border-border rounded-md" />
          </InputOTPGroup>
        </InputOTP>
      </div>
      {error && <p className="text-sm text-red-500">{error}</p>}
      <Button
        onClick={handleSubmit}
        disabled={loading || pin.length < 4}
        className="w-full h-[48px] rounded-full bg-primary text-primary-foreground font-semibold"
      >
        {loading ? "Processing..." : buttonText}
      </Button>
    </div>
  );
};