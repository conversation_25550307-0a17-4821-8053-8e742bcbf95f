import { useCryptoTransactions } from "@/hooks/api/userCryptoTransactions";
import { Link } from "react-router-dom"
import RecentTransactions from "./recent-transactions"
import type { Transaction } from "./recent-transactions"
import { useDrawer } from "@/components/drawer-view/use-drawer";
import CryptoTransactionDetails from "./crypto-transaction-details-drawer";
import { useState } from "react";

export default function CryptoTransactions() {
    const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);
    const { openDrawer } = useDrawer();

    const handleTransactionClick = (transaction: Transaction) => {
        console.log('Transaction ID:', transaction.transaction_id);
        setSelectedTransactionId(transaction.transaction_id);
        
        openDrawer({
            view: (
                <CryptoTransactionDetails 
                    transactionId={transaction.transaction_id} 
                />
            ),
            placement: "right",
            customSize: "480px",
        });
    };

    return (
        <div className="p-4 space-y-6">
            <div className="flex gap-2 items-center">
                <Link to="/">
                    <img
                        src="src/assets/images/backbutton.png"
                        alt="back"
                        className="cursor-pointer"
                    />
                </Link>
                <p className="text-3xl font-bold">Crypto Transactions</p>
            </div>
            <div>
                <RecentTransactions
                    useTransactionsHook={ useCryptoTransactions}
                    title=""
                    useFullSkeleton={true}
                    onTransactionClick={handleTransactionClick}
                    seeAllText=""
                />
            </div>
        </div>
    )
}