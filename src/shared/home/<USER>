"use client";

import { Eye, EyeClosed } from "lucide-react";
import { useState } from "react";

interface BalanceDisplayProps {
  title: string;
  balance: string | number;
  balanceClassName?: string;
  titleClassName?: string;
  iconClassName?: string;
  showBalanceInitially?: boolean;
}

const BalanceDisplay = ({
  title,
  balance,
  balanceClassName = "text-amber-500 text-3xl md:text-5xl font-bold mt-2",
  titleClassName = "text-gray-500 text-2xl font-normal",
  iconClassName = "h-6 w-6 text-gray-700",
  showBalanceInitially = true,
}: BalanceDisplayProps) => {
  const [showBalance, setShowBalance] = useState(showBalanceInitially);

  return (
    <div className=" flex align-bottom justify-between">
      <div>
        <h2 className={titleClassName}>{title}</h2>
        <p className={balanceClassName}>
          {showBalance ? balance : "****************"}
        </p>
      </div>
      <div
        className="cursor-pointer"
        onClick={() => setShowBalance(!showBalance)}
      >
        {showBalance ? (
          <Eye className={iconClassName} />
        ) : (
          <EyeClosed className={iconClassName} />
        )}
      </div>
    </div>
  );
};

export default BalanceDisplay;
