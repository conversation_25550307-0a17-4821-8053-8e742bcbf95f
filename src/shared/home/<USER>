import React from "react";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { BuyCryptoFlow } from "./crypto/buy";
import { SwapCryptoFlow } from "./crypto/swap";
import { SellCryptoFlow } from "./crypto/sell";
import { ReceiveCryptoFlow } from "./crypto/receive";
import { SendCryptoFlow } from "./crypto/send";

// Update all icon components to use currentColor and inherit text-primary from parent
export const CircleArrowUpRightIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width={24}
        height={24}
        color="currentColor" // <-- use currentColor
        fill="none"
        {...props}
    >
        <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="1.5"
        />
        <path
            d="M14.7731 9.22687L9 15M14.7731 9.22687C14.2678 8.72156 11.8846 9.21665 11.1649 9.22687M14.7731 9.22687C15.2784 9.73219 14.7834 12.1154 14.7731 12.8351"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

export const CircleArrowDownRightIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width={24}
        height={24}
        color="currentColor"
        fill="none"
        {...props}
    >
        <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="1.5"
        />
        <path
            d="M14.7731 14.7731L9 9M14.7731 14.7731C14.2678 15.2784 11.8846 14.7834 11.1649 14.7731M14.7731 14.7731C15.2784 14.2678 14.7834 11.8846 14.7731 11.1649"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

export const MoneyReceiveCircleIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width={24}
        height={24}
        color="currentColor"
        fill="none"
        {...props}
    >
        <path
            d="M14 2.22179C13.3538 2.09076 12.6849 2.02197 12 2.02197C6.47715 2.02197 2 6.49421 2 12.011C2 17.5277 6.47715 22 12 22C17.5228 22 22 17.5277 22 12.011C22 11.3269 21.9311 10.6587 21.8 10.0132"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
        />
        <path
            d="M12 9.01428C10.8954 9.01428 10 9.68512 10 10.5126C10 11.3401 10.8954 12.011 12 12.011C13.1046 12.011 14 12.6819 14 13.5093C14 14.3368 13.1046 15.0077 12 15.0077M12 9.01428C12.8708 9.01428 13.6116 9.43123 13.8862 10.0132M12 9.01428V8.01538M12 15.0077C11.1292 15.0077 10.3884 14.5908 10.1138 14.0088M12 15.0077V16.0066"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
        />
        <path
            d="M21.9951 2L17.8193 6.17362M16.9951 2.52119L17.1133 5.60928C17.1133 6.33713 17.5484 6.79062 18.3409 6.84782L21.465 6.99451"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

export const MoneySendCircleIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width={24}
        height={24}
        color="currentColor"
        fill="none"
        {...props}
    >
        <path
            d="M14 2.20004C13.3538 2.06886 12.6849 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 11.3151 21.9311 10.6462 21.8 10"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
        />
        <path
            d="M12 9C10.8954 9 10 9.67157 10 10.5C10 11.3284 10.8954 12 12 12C13.1046 12 14 12.6716 14 13.5C14 14.3284 13.1046 15 12 15M12 9C12.8708 9 13.6116 9.4174 13.8862 10M12 9V8M12 15C11.1292 15 10.3884 14.5826 10.1138 14M12 15V16"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
        />
        <path
            d="M16.998 7.00195L21.1739 2.82375M21.998 6.48019L21.8798 3.3887C21.8798 2.66006 21.4448 2.20607 20.6523 2.14881L17.5282 2.00195"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

export const CircleArrowDataTransferVerticalIcon = (
    props: React.SVGProps<SVGSVGElement>,
) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width={24}
        height={24}
        color="currentColor"
        fill="none"
        {...props}
    >
        <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="1.5"
        />
        <path
            d="M10.2857 7.5L10.2857 15.4338C10.2857 16.0804 10.2857 16.4037 10.1093 16.4841C9.93296 16.5646 9.72538 16.336 9.31022 15.8788L8 14.4358M13.7143 16.5L13.7143 8.56622C13.7143 7.91964 13.7143 7.59635 13.8907 7.51589C14.067 7.43543 14.2746 7.66404 14.6898 8.12124L16 9.56415"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);


interface CryptoAction {
	id: string;
	icon: React.ReactNode;
	label: string;
	flowComponent: React.ReactNode;
}


const ActionButton: React.FC<{
	icon: React.ReactNode;
	label: string;
	onClick: () => void;
}> = ({ icon, label, onClick }) => {
    return (
        <div
            onClick={onClick}
            className="flex flex-col items-center cursor-pointer bg-gray-100 dark:bg-card p-4 rounded-lg hover:bg-gray-200 dark:hover:bg-muted transition-colors border border-border"
        >
            <div className="mb-2 text-primary">{icon}</div>
            <span className="text-sm text-foreground dark:text-foreground">{label}</span>
        </div>
    );
};


export const CryptoActions: React.FC = () => {
	const { openDrawer } = useDrawer();

	
	const actions: CryptoAction[] = [
		{
			id: "send",
			icon: <CircleArrowUpRightIcon />,
			label: "Send",
			flowComponent: <SendCryptoFlow />,
		},
		{
			id: "receive",
			icon: <CircleArrowDownRightIcon />,
			label: "Receive",
			flowComponent: <ReceiveCryptoFlow />,
		},
		{
			id: "buy",
			icon: <MoneyReceiveCircleIcon />,
			label: "Buy",
			flowComponent: <BuyCryptoFlow />,
		},

		{
			id: "sell",
			icon: <MoneySendCircleIcon />,
			label: "Sell",
			flowComponent: <SellCryptoFlow />,
		},
		{
			id: "swap",
			icon: <CircleArrowDataTransferVerticalIcon />,
			label: "Swap",
			flowComponent: <SwapCryptoFlow />,
		},
	];

	
	const handleOpenAction = (action: CryptoAction) => {
		openDrawer({
			view: action.flowComponent,
			placement: "right",
			customSize: "480px",
		});
	};

	return (
		<div className="grid grid-cols-5 gap-4">
			{actions.map((action) => (
				<ActionButton
					key={action.id}
					icon={action.icon}
					label={action.label}
					onClick={() => handleOpenAction(action)}
				/>
			))}
			{/* <SendCryptoButton /> */}
		</div>
	);
};
