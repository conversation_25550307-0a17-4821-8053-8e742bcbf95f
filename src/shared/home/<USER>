import React, { useMemo } from "react";
import { <PERSON> } from "react-router-dom";
import { GripVertical } from "lucide-react";

import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { Skeleton } from "@/components/ui/skeleton";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { usePortfolioOrder } from "@/hooks/api/portfolio-order"; // Fixed typo and using shared hook

import MyPortfolioDrawer from "./my-portfolio-drawer";
import { useDrawer } from "@/components/drawer-view/use-drawer";

import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// 🔁 Sortable Portfolio Item
export const PortfolioItem: React.FC<{ crypto: ICryptoWallet }> = ({ crypto }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: crypto.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="cursor-pointer"
    >
      <div className="flex items-center justify-between p-4 rounded-full border border-border bg-background hover:shadow-sm transition-all">
        <div className="flex items-center gap-3">
          <div {...listeners} className="text-muted-foreground mr-2">
            <GripVertical className="cursor-grab" />
          </div>

          <div className="w-12 h-12 relative flex-shrink-0">
            {crypto.image ? (
              <img
                src={crypto.image}
                alt={crypto.coin_name}
                className="w-full h-full object-contain rounded-full"
              />
            ) : (
              <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                <span className="text-xs text-muted-foreground">
                  {crypto.currency?.charAt(0)}
                </span>
              </div>
            )}
          </div>

          <div className="flex flex-col">
            <h3 className="font-medium capitalize text-foreground">
              {crypto.coin_name}
            </h3>
            <p className="text-sm text-muted-foreground uppercase">
              {crypto.currency}
            </p>
          </div>
        </div>

        <div className="flex flex-col items-end">
          <span className="text-muted-foreground text-sm">Balance</span>
          <p className="text-sm font-semibold text-primary">
            {crypto.balance} {crypto.currency}
          </p>
        </div>
      </div>
    </div>
  );
};

// 📦 Main Component
const PortfolioSection: React.FC<{ userId: string }> = ({ userId }) => {
  const { data: cryptoWallets, isLoading } = useCryptoUserWallets();
  const cryptoAssets = useMemo(
    () => cryptoWallets?.slice(0, 10) || [],
    [cryptoWallets]
  );
  const { openDrawer } = useDrawer();
  
  // Use the shared hook for persistent ordering
  const { items, updateOrder } = usePortfolioOrder(cryptoAssets, userId);

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      const oldIndex = items.indexOf(active.id);
      const newIndex = items.indexOf(over.id);
      const newOrder = arrayMove(items, oldIndex, newIndex);
      updateOrder(newOrder);
    }
  };

  const cryptoMap: Record<string, ICryptoWallet> = useMemo(
    () => Object.fromEntries(cryptoAssets.map((c) => [c.id, c])),
    [cryptoAssets]
  );

  return (
    <div className="bg-card p-6 rounded-2xl border border-border">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-foreground">Portfolio</h2>
        <Link to="/portfolio">
          <button className="font-semibold text-primary cursor-pointer">See all</button>
        </Link>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center gap-4 p-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-12" />
              </div>
              <div className="ml-auto space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={items} strategy={verticalListSortingStrategy}>
            <div className="space-y-3">
              {items.map((id) => {
                const crypto = cryptoMap[id];
                if (!crypto) return null;

                return (
                  <div
                    key={id}
                    onClick={() =>
                      openDrawer({
                        view: <MyPortfolioDrawer selectedCrypto={crypto} />,
                        placement: "right",
                        customSize: "480px",
                      })
                    }
                  >
                    <PortfolioItem crypto={crypto} />
                  </div>
                );
              })}
            </div>
          </SortableContext>
        </DndContext>
      )}
    </div>
  );
};

export default PortfolioSection;