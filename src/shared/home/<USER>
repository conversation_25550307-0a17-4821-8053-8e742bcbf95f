// import { useState, useEffect, useRef } from "react";
// import { createPortal } from "react-dom";
// import { ChevronDown, Plus } from "lucide-react";
// import { motion, AnimatePresence } from "framer-motion";
// import BalanceDisplay from "./balance-display";
// import { CryptoActions } from "./crypto-actions";
// import { FiatActions } from "./fiat-actions";
// import {
// 	useCryptoUserWallets,
// 	useAddCryptoWallet,
// } from "@/hooks/api/crypto-wallets";
// import {
// 	useFiatUserWallets,
// 	useFiatAvailableCurrencyCodes,
// 	useAddFiatWallet,
// } from "@/hooks/api/fiat-wallets";
// import { useAvailableCurrencies } from "@/hooks/api/crypto-banking";
// import { formatCurrency } from "@/utils/format-currency";
// import { formatAmount } from "@/utils/format-amount";
// import AccountBalanceSkeleton from "@/components/loaders/account-balance-skeleton";
// import { toast } from "sonner";
// import useCryptoSelectionStore from "@/store/crypto-selection-store";

// function PopoverPortal({ children }: { children: React.ReactNode }) {
// 	if (typeof document === "undefined") return null;
// 	return createPortal(children, document.body);
// }

// /* ----------------------------------------------------------------- */
// interface AccountBalanceCardProps {
// 	activeView: "crypto" | "fiat";
// 	onViewChange: (view: "crypto" | "fiat") => void;
// 	className?: string;
// }

// export default function AccountBalanceCard({
// 	activeView,
// 	onViewChange,
// 	className = "",
// }: AccountBalanceCardProps) {
// 	const [containerHeight, setContainerHeight] = useState<number>(0);
// 	const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
// 	const [modalPosition, setModalPosition] = useState<{
// 		top: number;
// 		left: number;
// 		width: number;
// 	} | null>(null);

// 	const [showCryptoCurrencyOptions, setShowCryptoCurrencyOptions] =
// 		useState(false);
// 	const [showFiatCurrencyOptions, setShowFiatCurrencyOptions] =
// 		useState(false);

// 	const cryptoRef = useRef<HTMLDivElement>(null);
// 	const fiatRef = useRef<HTMLDivElement>(null);

// 	// Add store hook
// 	const { setSelectedCrypto } = useCryptoSelectionStore();

// 	const { data: cryptoWallets, isLoading: cryptoLoading } =
// 		useCryptoUserWallets();
// 	const { data: fiatWallets, isLoading: fiatLoading } = useFiatUserWallets();

// 	const {
// 		data: availableCurrencies,
// 		isLoading: availableCurrenciesLoading,
// 		isError: availableCurrenciesError,
// 	} = useFiatAvailableCurrencyCodes();

// 	const {
// 		data: availableCryptoCurrencies,
// 		isLoading: loadingCryptoCurrencies,
// 		isError: errorCryptoCurrencies,
// 	} = useAvailableCurrencies();

// 	const { mutate: addWallet } = useAddFiatWallet();
// 	const { mutate: addCryptoWallet } = useAddCryptoWallet();

// 	const [selectedCryptoWallet, setSelectedCryptoWallet] = useState<
// 		any | null
// 	>(null);

// 	const primaryFiatWallet =
// 		fiatWallets?.find((w) => w.is_default) || fiatWallets?.[0];

// 	const [selectedFiatWallet, setSelectedFiatWallet] = useState<any | null>(
// 		() => primaryFiatWallet || null,
// 	);

// 	// Auto-select default crypto wallet if not already selected
// 	useEffect(() => {
// 		if (!selectedCryptoWallet && cryptoWallets?.length) {
// 			setSelectedCryptoWallet(cryptoWallets[0]);
// 		}
// 	}, [cryptoWallets, selectedCryptoWallet]);

// 	// Auto-select default fiat wallet if not already selected
// 	useEffect(() => {
// 		if (!selectedFiatWallet && fiatWallets?.length) {
// 			const def = fiatWallets.find((w) => w.is_default) || fiatWallets[0];
// 			setSelectedFiatWallet(def);
// 		}
// 	}, [fiatWallets, selectedFiatWallet]);

// 	useEffect(() => setIsWalletModalOpen(false), [activeView]);

// 	useEffect(() => {
// 		const updateHeight = () => {
// 			const cryptoHeight = cryptoRef.current?.offsetHeight || 0;
// 			const fiatHeight = fiatRef.current?.offsetHeight || 0;
// 			setContainerHeight(Math.max(cryptoHeight, fiatHeight) + 20);
// 		};
// 		if (!cryptoLoading && !fiatLoading) setTimeout(updateHeight, 0);
// 		window.addEventListener("resize", updateHeight);
// 		return () => window.removeEventListener("resize", updateHeight);
// 	}, [cryptoLoading, fiatLoading, cryptoWallets, fiatWallets]);

// 	const openModalUnder = (el: HTMLElement) => {
// 		const rect = el.getBoundingClientRect();
// 		setModalPosition({
// 			top: rect.bottom + 8 + window.scrollY,
// 			left: rect.left + window.scrollX,
// 			width: rect.width,
// 		});
// 		setIsWalletModalOpen(true);
// 	};

// 	const closeModal = () => {
// 		setIsWalletModalOpen(false);
// 		setShowCryptoCurrencyOptions(false);
// 		setShowFiatCurrencyOptions(false);
// 	};

// 	const cryptoBalance = selectedCryptoWallet
// 		? `${formatAmount(
// 				selectedCryptoWallet.balance,
// 				selectedCryptoWallet.currency,
// 		  )} ${selectedCryptoWallet.currency}`
// 		: "0.********";

// 	const fiatBalance = selectedFiatWallet
// 		? `${selectedFiatWallet.currency} ${formatCurrency(
// 				selectedFiatWallet.available_balance ?? 0,
// 				selectedFiatWallet.currency,
// 		  )}`
// 		: "0.00";

// 	if (cryptoLoading || fiatLoading) return <AccountBalanceSkeleton />;

// 	return (
// 		<>
// 			<div
// 				className={`w-full mx-auto overflow-hidden relative ${className}`}
// 			>
// 				<div
// 					className="relative overflow-hidden bg-card rounded-2xl shadow-sm"
// 					style={{
// 						height:
// 							containerHeight > 0
// 								? `${containerHeight}px`
// 								: "auto",
// 					}}
// 				>
// 					<AnimatePresence initial={false} mode="wait">
// 						{activeView === "crypto" ? (
// 							<motion.div
// 								ref={cryptoRef}
// 								key="crypto"
// 								initial={{ x: -300, opacity: 0 }}
// 								animate={{ x: 0, opacity: 1 }}
// 								exit={{ x: -300, opacity: 0 }}
// 								transition={{
// 									duration: 0.4,
// 									ease: "easeInOut",
// 								}}
// 								className="space-y-6 absolute w-full p-6"
// 							>
// 								<button
// 									className="inline-flex items-center justify-between w-full px-6 py-3 rounded-full border border-gray-200 cursor-pointer"
// 									onClick={(e) =>
// 										openModalUnder(
// 											e.currentTarget as HTMLElement,
// 										)
// 									}
// 								>
// 									<div className="flex items-center gap-3">
// 										{/* Add crypto image to select bar */}
// 										{selectedCryptoWallet && (
// 											<div className="w-6 h-6 flex items-center justify-center">
// 												{selectedCryptoWallet.image ? (
// 													<img
// 														src={selectedCryptoWallet.image}
// 														alt={selectedCryptoWallet.currency}
// 														className="w-full h-full object-contain rounded-full"
// 													/>
// 												) : (
// 													<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 												)}
// 											</div>
// 										)}
// 										<span className="font-medium truncate">
// 											{selectedCryptoWallet
// 												? `${selectedCryptoWallet.coin_name} (${selectedCryptoWallet.currency})`
// 												: "Select Wallet"}
// 										</span>
// 									</div>
// 									<ChevronDown className="h-5 w-5" />
// 								</button>

// 								<BalanceDisplay
// 									title="Total Crypto Balance"
// 									balance={cryptoBalance}
// 									balanceClassName="text-amber-500 text-3xl lg:text-4xl font-bold mt-2"
// 									titleClassName="text-gray-500 text-2xl font-normal"
// 									iconClassName="h-6 w-6 text-gray-700"
// 									showBalanceInitially={true}
// 								/>

// 								<CryptoActions />
// 							</motion.div>
// 						) : (
// 							<motion.div
// 								ref={fiatRef}
// 								key="fiat"
// 								initial={{ x: 300, opacity: 0 }}
// 								animate={{ x: 0, opacity: 1 }}
// 								exit={{ x: 300, opacity: 0 }}
// 								transition={{
// 									duration: 0.4,
// 									ease: "easeInOut",
// 								}}
// 								className="space-y-6 absolute w-full p-6"
// 							>
// 								<button
// 									className="inline-flex items-center justify-between w-full px-6 py-3 rounded-full border border-gray-200 cursor-pointer"
// 									onClick={(e) =>
// 										openModalUnder(
// 											e.currentTarget as HTMLElement,
// 										)
// 									}
// 								>
// 									<span className="font-medium truncate">
// 										{selectedFiatWallet
// 											? `${
// 													selectedFiatWallet.name ||
// 													""
// 											  }${
// 													selectedFiatWallet.currency
// 														? ` (${selectedFiatWallet.currency})`
// 														: ""
// 											  }`
// 											: "Select Wallet"}
// 									</span>
// 									<ChevronDown className="h-5 w-5" />
// 								</button>

// 								<BalanceDisplay
// 									title="Total Fiat Balance"
// 									balance={fiatBalance}
// 									balanceClassName="text-amber-500 text-5xl font-bold mt-2"
// 									titleClassName="text-gray-500 text-2xl font-normal"
// 									iconClassName="h-6 w-6 text-gray-700"
// 									showBalanceInitially={true}
// 								/>

// 								<FiatActions />
// 							</motion.div>
// 						)}
// 					</AnimatePresence>
// 				</div>

// 				<div className="mt-4 flex justify-center">
// 					<div className="flex items-center justify-center gap-2 h-8">
// 						<motion.div
// 							className="rounded-full cursor-pointer"
// 							animate={{
// 								width: activeView === "crypto" ? 60 : 16,
// 								height: 16,
// 								backgroundColor:
// 									activeView === "crypto"
// 										? "#F59E0B"
// 										: "#E5E7EB",
// 							}}
// 							transition={{ duration: 0.3 }}
// 							onClick={() => onViewChange("crypto")}
// 						/>
// 						<motion.div
// 							className="rounded-full cursor-pointer"
// 							animate={{
// 								width: activeView === "fiat" ? 60 : 16,
// 								height: 16,
// 								backgroundColor:
// 									activeView === "fiat"
// 										? "#F59E0B"
// 										: "#E5E7EB",
// 							}}
// 							transition={{ duration: 0.3 }}
// 							onClick={() => onViewChange("fiat")}
// 						/>
// 					</div>
// 				</div>
// 			</div>

// 			{isWalletModalOpen && modalPosition && (
// 				<PopoverPortal>
// 					<AnimatePresence>
// 						<motion.div
// 							className="fixed inset-0 bg-black"
// 							style={{ opacity: 0.1, zIndex: 998 }}
// 							initial={{ opacity: 0 }}
// 							animate={{ opacity: 0.1 }}
// 							exit={{ opacity: 0 }}
// 							transition={{ duration: 0.2 }}
// 							onClick={closeModal}
// 						/>
// 						<motion.div
// 							key="wallet-modal"
// 							initial={{ opacity: 0, scale: 0.95 }}
// 							animate={{ opacity: 1, scale: 1 }}
// 							exit={{ opacity: 0, scale: 0.95 }}
// 							transition={{ duration: 0.2 }}
// 							style={{
// 								position: "absolute",
// 								top: modalPosition.top,
// 								left: modalPosition.left,
// 								width: modalPosition.width,
// 								zIndex: 999,
// 							}}
// 							className="bg-white rounded-xl shadow-xl border border-gray-200 max-h-80 overflow-y-auto"
// 						>
// 							{activeView === "crypto" ? (
// 								<div className="p-4">
// 									<button
// 										className="w-full mb-3 py-2 px-4 flex items-center gap-2 bg-primary hover:bg-primary/90 text-white rounded-lg font-medium"
// 										onClick={() =>
// 											setShowCryptoCurrencyOptions(
// 												(prev) => !prev,
// 											)
// 										}
// 									>
// 										<Plus className="h-5 w-5" />
// 										<span>Add New Wallet</span>
// 									</button>

// 									{showCryptoCurrencyOptions && (
// 										<div className="mb-3 bg-white rounded-md border border-gray-200 shadow-sm max-h-48 overflow-y-auto">
// 											{loadingCryptoCurrencies ? (
// 												<div className="p-4 text-center text-sm text-gray-500">
// 													Loading...
// 												</div>
// 											) : errorCryptoCurrencies ? (
// 												<div className="p-4 text-center text-sm text-red-500">
// 													Failed to load currencies.
// 												</div>
// 											) : (
// 												availableCryptoCurrencies?.map(
// 													(currency: string) => (
// 														<div
// 															key={currency}
// 															className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
// 															onClick={() =>
// 																addCryptoWallet(
// 																	currency,
// 																	{
// 																		onSuccess:
// 																			() => {
// 																				setShowCryptoCurrencyOptions(
// 																					false,
// 																				);
// 																				closeModal();
// 																			},
// 																	},
// 																)
// 															}
// 														>
// 															{currency}
// 														</div>
// 													),
// 												)
// 											)}
// 										</div>
// 									)}

// 									<hr className="my-2" />

// 									{cryptoWallets?.map((wallet) => (
// 										<div
// 											key={wallet.id}
// 											onClick={() => {
// 												setSelectedCryptoWallet(wallet);
// 												setSelectedCrypto(wallet); // Update global store
// 												closeModal();
// 											}}
// 											className={`px-4 py-2 rounded-md cursor-pointer hover:bg-gray-100 flex items-center gap-3 ${
// 												selectedCryptoWallet?.id ===
// 												wallet.id
// 													? "bg-gray-200 font-medium"
// 													: ""
// 											}`}
// 										>
// 											{/* Add crypto image */}
// 											<div className="w-6 h-6 flex items-center justify-center">
// 												{wallet.image ? (
// 													<img
// 														src={wallet.image}
// 														alt={wallet.currency}
// 														className="w-full h-full object-contain rounded-full"
// 													/>
// 												) : (
// 													<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 												)}
// 											</div>
// 											<span>
// 												{wallet.coin_name} ({wallet.currency})
// 											</span>
// 										</div>
// 									))}
// 								</div>
// 							) : (
// 								<div className="p-4">
// 									<button
// 										className="w-full mb-3 py-2 px-4 flex items-center gap-2 bg-primary hover:bg-primary/90 text-white rounded-full font-medium"
// 										onClick={() =>
// 											setShowFiatCurrencyOptions(
// 												(prev) => !prev,
// 											)
// 										}
// 									>
// 										<Plus className="h-5 w-5" />
// 										<span>Add New Wallet</span>
// 									</button>

// 									{showFiatCurrencyOptions && (
// 										<div className="mb-3 bg-card rounded-md border border-gray-200 shadow-sm max-h-48 overflow-y-auto">
// 											{availableCurrenciesLoading ? (
// 												<div className="p-4 text-center text-sm text-gray-500">
// 													Loading...
// 												</div>
// 											) : availableCurrenciesError ? (
// 												<div className="p-4 text-center text-sm text-red-500">
// 													Failed to load currencies.
// 												</div>
// 											) : (
// 												Array.isArray(availableCurrencies?.currencies) &&
// 												availableCurrencies.currencies.map(
// 													(code: string) => (
// 														<div
// 															key={code}
// 															className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
// 															onClick={() =>
// 																addWallet(
// 																	code,
// 																	{
// 																		onSuccess:
// 																			() => {
// 																				toast.success(
// 																					`Wallet for ${code} added`,
// 																				);
// 																				setShowFiatCurrencyOptions(
// 																					false,
// 																				);
// 																				closeModal();
// 																			},
// 																		onError:
// 																			() => {
// 																				toast.error(
// 																					"Failed to add wallet",
// 																				);
// 																			},
// 																	},
// 																)
// 															}
// 														>
// 															{code}
// 														</div>
// 													),
// 												)
// 											)}
// 										</div>
// 									)}

// 									<hr className="my-2" />

// 									{fiatWallets?.map((wallet) => (
// 										<div
// 											key={wallet.id}
// 											onClick={() => {
// 												setSelectedFiatWallet(wallet);
// 												closeModal();
// 											}}
// 											className={`px-4 py-2 rounded-md cursor-pointer hover:bg-gray-100 ${
// 												selectedFiatWallet?.id ===
// 												wallet.id
// 													? "bg-gray-200 font-medium"
// 													: ""
// 											}`}
// 										>
// 											{wallet.name} ({wallet.currency})
// 										</div>
// 									))}
// 								</div>
// 							)}
// 						</motion.div>
// 					</AnimatePresence>
// 				</PopoverPortal>
// 			)}
// 		</>
// 	);
// }

import { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { ChevronDown, Plus } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import BalanceDisplay from "./balance-display";
import { CryptoActions } from "./crypto-actions";
import { FiatActions } from "./fiat-actions";
import {
	useCryptoUserWallets,
	useAddCryptoWallet,
} from "@/hooks/api/crypto-wallets";
import {
	useFiatUserWallets,
	useFiatAvailableCurrencyCodes,
	useAddFiatWallet,
} from "@/hooks/api/fiat-wallets";
import { useAvailableCurrencies } from "@/hooks/api/crypto-banking";
import { formatCurrency } from "@/utils/format-currency";
import { formatAmount } from "@/utils/format-amount";
import AccountBalanceSkeleton from "@/components/loaders/account-balance-skeleton";
import { toast } from "sonner";
import useCryptoSelectionStore from "@/store/crypto-selection-store";
import useFiatSelectionStore from "@/store/fiat-selection-store";

function PopoverPortal({ children }: { children: React.ReactNode }) {
	if (typeof document === "undefined") return null;
	return createPortal(children, document.body);
}

/* ----------------------------------------------------------------- */
interface AccountBalanceCardProps {
	activeView: "crypto" | "fiat";
	onViewChange: (view: "crypto" | "fiat") => void;
	className?: string;
}

export default function AccountBalanceCard({
	activeView,
	onViewChange,
	className = "",
}: AccountBalanceCardProps) {
	const [containerHeight, setContainerHeight] = useState<number>(0);
	const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
	const [modalPosition, setModalPosition] = useState<{
		top: number;
		left: number;
		width: number;
	} | null>(null);

	const [showCryptoCurrencyOptions, setShowCryptoCurrencyOptions] =
		useState(false);
	const [showFiatCurrencyOptions, setShowFiatCurrencyOptions] =
		useState(false);

	const cryptoRef = useRef<HTMLDivElement>(null);
	const fiatRef = useRef<HTMLDivElement>(null);

	// Add both store hooks
	const { setSelectedCrypto } = useCryptoSelectionStore();
	const { selectedFiat, setSelectedFiat } = useFiatSelectionStore();

	const { data: cryptoWallets, isLoading: cryptoLoading } =
		useCryptoUserWallets();
	const { data: fiatWallets, isLoading: fiatLoading } = useFiatUserWallets();

	const {
		data: availableCurrencies,
		isLoading: availableCurrenciesLoading,
		isError: availableCurrenciesError,
	} = useFiatAvailableCurrencyCodes();

	const {
		data: availableCryptoCurrencies,
		isLoading: loadingCryptoCurrencies,
		isError: errorCryptoCurrencies,
	} = useAvailableCurrencies();

	const { mutate: addWallet } = useAddFiatWallet();
	const { mutate: addCryptoWallet } = useAddCryptoWallet();

	const [selectedCryptoWallet, setSelectedCryptoWallet] = useState<
		any | null
	>(null);

	const primaryFiatWallet =
		fiatWallets?.find((w) => w.is_default) || fiatWallets?.[0];

	// Auto-select default crypto wallet if not already selected
	useEffect(() => {
		if (!selectedCryptoWallet && cryptoWallets?.length) {
			setSelectedCryptoWallet(cryptoWallets[0]);
		}
	}, [cryptoWallets, selectedCryptoWallet]);

	// Auto-select default fiat wallet if not already selected - now using store
	useEffect(() => {
		if (!selectedFiat && fiatWallets?.length) {
			const def = fiatWallets.find((w) => w.is_default) || fiatWallets[0];
			setSelectedFiat(def);
		}
	}, [fiatWallets, selectedFiat, setSelectedFiat]);

	useEffect(() => setIsWalletModalOpen(false), [activeView]);

	useEffect(() => {
		const updateHeight = () => {
			const cryptoHeight = cryptoRef.current?.offsetHeight || 0;
			const fiatHeight = fiatRef.current?.offsetHeight || 0;
			setContainerHeight(Math.max(cryptoHeight, fiatHeight) + 20);
		};
		if (!cryptoLoading && !fiatLoading) setTimeout(updateHeight, 0);
		window.addEventListener("resize", updateHeight);
		return () => window.removeEventListener("resize", updateHeight);
	}, [cryptoLoading, fiatLoading, cryptoWallets, fiatWallets]);

	const openModalUnder = (el: HTMLElement) => {
		const rect = el.getBoundingClientRect();
		setModalPosition({
			top: rect.bottom + 8 + window.scrollY,
			left: rect.left + window.scrollX,
			width: rect.width,
		});
		setIsWalletModalOpen(true);
	};

	const closeModal = () => {
		setIsWalletModalOpen(false);
		setShowCryptoCurrencyOptions(false);
		setShowFiatCurrencyOptions(false);
	};

	const cryptoBalance = selectedCryptoWallet
		? `${formatAmount(
				selectedCryptoWallet.balance,
				selectedCryptoWallet.currency,
		  )} ${selectedCryptoWallet.currency}`
		: "0.********";

	// Update to use selectedFiat from store
	const fiatBalance = selectedFiat
		? `${selectedFiat.currency} ${formatCurrency(
				selectedFiat.available_balance ?? 0,
				selectedFiat.currency,
		  )}`
		: "0.00";

	if (cryptoLoading || fiatLoading) return <AccountBalanceSkeleton />;

	return (
		<>
			<div
				className={`w-full mx-auto overflow-hidden relative ${className}`}
			>
				<div
					className="relative overflow-hidden bg-card border rounded-2xl shadow-sm"
					style={{
						height:
							containerHeight > 0
								? `${containerHeight}px`
								: "auto",
					}}
				>
					<AnimatePresence initial={false} mode="wait">
						{activeView === "crypto" ? (
							<motion.div
								ref={cryptoRef}
								key="crypto"
								initial={{ x: -300, opacity: 0 }}
								animate={{ x: 0, opacity: 1 }}
								exit={{ x: -300, opacity: 0 }}
								transition={{
									duration: 0.4,
									ease: "easeInOut",
								}}
								className="space-y-6 absolute w-full p-6"
							>
								<button
									className="inline-flex items-center justify-between w-full px-6 py-3 rounded-full border border-gray-200 cursor-pointer"
									onClick={(e) =>
										openModalUnder(
											e.currentTarget as HTMLElement,
										)
									}
								>
									<div className="flex items-center gap-3">
										{/* Add crypto image to select bar */}
										{selectedCryptoWallet && (
											<div className="w-6 h-6 flex items-center justify-center">
												{selectedCryptoWallet.image ? (
													<img
														src={selectedCryptoWallet.image}
														alt={selectedCryptoWallet.currency}
														className="w-full h-full object-contain rounded-full"
													/>
												) : (
													<div className="w-6 h-6 bg-gray-200 rounded-full" />
												)}
											</div>
										)}
										<span className="font-medium truncate">
											{selectedCryptoWallet
												? `${selectedCryptoWallet.coin_name} (${selectedCryptoWallet.currency})`
												: "Select Wallet"}
										</span>
									</div>
									<ChevronDown className="h-5 w-5" />
								</button>

								<BalanceDisplay
									title="Total Crypto Balance"
									balance={cryptoBalance}
									balanceClassName="text-amber-500 text-3xl lg:text-4xl font-bold mt-2"
									titleClassName="text-gray-500 text-2xl font-normal"
									iconClassName="h-6 w-6 text-gray-700"
									showBalanceInitially={true}
								/>

								<CryptoActions />
							</motion.div>
						) : (
							<motion.div
								ref={fiatRef}
								key="fiat"
								initial={{ x: 300, opacity: 0 }}
								animate={{ x: 0, opacity: 1 }}
								exit={{ x: 300, opacity: 0 }}
								transition={{
									duration: 0.4,
									ease: "easeInOut",
								}}
								className="space-y-6 absolute w-full p-6"
							>
								<button
									className="inline-flex items-center justify-between w-full px-6 py-3 rounded-full border border-gray-200 cursor-pointer"
									onClick={(e) =>
										openModalUnder(
											e.currentTarget as HTMLElement,
										)
									}
								>
									<span className="font-medium truncate">
										{selectedFiat
											? `${
													selectedFiat.name ||
													""
											  }${
													selectedFiat.currency
														? ` (${selectedFiat.currency})`
														: ""
											  }`
											: "Select Wallet"}
									</span>
									<ChevronDown className="h-5 w-5" />
								</button>

								<BalanceDisplay
									title="Total Fiat Balance"
									balance={fiatBalance}
									balanceClassName="text-amber-500 text-5xl font-bold mt-2"
									titleClassName="text-gray-500 text-2xl font-normal"
									iconClassName="h-6 w-6 text-gray-700"
									showBalanceInitially={true}
								/>

								<FiatActions />
							</motion.div>
						)}
					</AnimatePresence>
				</div>

				<div className="mt-4 flex justify-center">
					<div className="flex items-center justify-center gap-2 h-8">
						<motion.div
							className="rounded-full cursor-pointer"
							animate={{
								width: activeView === "crypto" ? 60 : 16,
								height: 16,
								backgroundColor:
									activeView === "crypto"
										? "#F59E0B"
										: "#E5E7EB",
							}}
							transition={{ duration: 0.3 }}
							onClick={() => onViewChange("crypto")}
						/>
						<motion.div
							className="rounded-full cursor-pointer"
							animate={{
								width: activeView === "fiat" ? 60 : 16,
								height: 16,
								backgroundColor:
									activeView === "fiat"
										? "#F59E0B"
										: "#E5E7EB",
							}}
							transition={{ duration: 0.3 }}
							onClick={() => onViewChange("fiat")}
						/>
					</div>
				</div>
			</div>

			{isWalletModalOpen && modalPosition && (
				<PopoverPortal>
					<AnimatePresence>
						<motion.div
							className="fixed inset-0 bg-black"
							style={{ opacity: 0.1, zIndex: 998 }}
							initial={{ opacity: 0 }}
							animate={{ opacity: 0.1 }}
							exit={{ opacity: 0 }}
							transition={{ duration: 0.2 }}
							onClick={closeModal}
						/>
						<motion.div
							key="wallet-modal"
							initial={{ opacity: 0, scale: 0.95 }}
							animate={{ opacity: 1, scale: 1 }}
							exit={{ opacity: 0, scale: 0.95 }}
							transition={{ duration: 0.2 }}
							style={{
								position: "absolute",
								top: modalPosition.top,
								left: modalPosition.left,
								width: modalPosition.width,
								zIndex: 999,
							}}
							className="bg-white rounded-xl shadow-xl border border-gray-200 max-h-80 overflow-y-auto"
						>
							{activeView === "crypto" ? (
								<div className="p-4">
									<button
										className="w-full mb-3 py-2 px-4 flex items-center gap-2 bg-primary hover:bg-primary/90 text-white rounded-lg font-medium"
										onClick={() =>
											setShowCryptoCurrencyOptions(
												(prev) => !prev,
											)
										}
									>
										<Plus className="h-5 w-5" />
										<span>Add New Wallet</span>
									</button>

									{showCryptoCurrencyOptions && (
										<div className="mb-3 bg-white rounded-md border border-gray-200 shadow-sm max-h-48 overflow-y-auto">
											{loadingCryptoCurrencies ? (
												<div className="p-4 text-center text-sm text-gray-500">
													Loading...
												</div>
											) : errorCryptoCurrencies ? (
												<div className="p-4 text-center text-sm text-red-500">
													Failed to load currencies.
												</div>
											) : (
												availableCryptoCurrencies?.map(
													(currency: string) => (
														<div
															key={currency}
															className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
															onClick={() =>
																addCryptoWallet(
																	currency,
																	{
																		onSuccess:
																			() => {
																				setShowCryptoCurrencyOptions(
																					false,
																				);
																				closeModal();
																			},
																	},
																)
															}
														>
															{currency}
														</div>
													),
												)
											)}
										</div>
									)}

									<hr className="my-2" />

									{cryptoWallets?.map((wallet) => (
										<div
											key={wallet.id}
											onClick={() => {
												setSelectedCryptoWallet(wallet);
												setSelectedCrypto(wallet); // Update global store
												closeModal();
											}}
											className={`px-4 py-2 rounded-md cursor-pointer hover:bg-gray-100 flex items-center gap-3 ${
												selectedCryptoWallet?.id ===
												wallet.id
													? "bg-gray-200 font-medium"
													: ""
											}`}
										>
											{/* Add crypto image */}
											<div className="w-6 h-6 flex items-center justify-center">
												{wallet.image ? (
													<img
														src={wallet.image}
														alt={wallet.currency}
														className="w-full h-full object-contain rounded-full"
													/>
												) : (
													<div className="w-6 h-6 bg-gray-200 rounded-full" />
												)}
											</div>
											<span>
												{wallet.coin_name} ({wallet.currency})
											</span>
										</div>
									))}
								</div>
							) : (
								<div className="p-4">
									<button
										className="w-full mb-3 py-2 px-4 flex items-center gap-2 bg-primary hover:bg-primary/90 text-white rounded-full font-medium"
										onClick={() =>
											setShowFiatCurrencyOptions(
												(prev) => !prev,
											)
										}
									>
										<Plus className="h-5 w-5" />
										<span>Add New Wallet</span>
									</button>

									{showFiatCurrencyOptions && (
										<div className="mb-3 bg-card rounded-md border border-gray-200 shadow-sm max-h-48 overflow-y-auto">
											{availableCurrenciesLoading ? (
												<div className="p-4 text-center text-sm text-gray-500">
													Loading...
												</div>
											) : availableCurrenciesError ? (
												<div className="p-4 text-center text-sm text-red-500">
													Failed to load currencies.
												</div>
											) : (
												Array.isArray(availableCurrencies?.currencies) &&
												availableCurrencies.currencies.map(
													(code: string) => (
														<div
															key={code}
															className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
															onClick={() =>
																addWallet(
																	code,
																	{
																		onSuccess:
																			() => {
																				toast.success(
																					`Wallet for ${code} added`,
																				);
																				setShowFiatCurrencyOptions(
																					false,
																				);
																				closeModal();
																			},
																		onError:
																			() => {
																				toast.error(
																					"Failed to add wallet",
																				);
																			},
																	},
																)
															}
														>
															{code}
														</div>
													),
												)
											)}
										</div>
									)}

									<hr className="my-2" />

									{fiatWallets?.map((wallet) => (
										<div
											key={wallet.id}
											onClick={() => {
												setSelectedFiat(wallet); // Update global store
												closeModal();
											}}
											className={`px-4 py-2 rounded-md cursor-pointer hover:bg-gray-100 ${
												selectedFiat?.id === wallet.id
													? "bg-gray-200 font-medium"
													: ""
											}`}
										>
											{wallet.name} ({wallet.currency})
										</div>
									))}
								</div>
							)}
						</motion.div>
					</AnimatePresence>
				</PopoverPortal>
			)}
		</>
	);
}