import React from "react";
import { Link } from "react-router-dom";
import { useModal } from "@/components/modal-view/use-modal";
import CreatePin from "@/components/create-pin";
import { SendCryptoForm } from "@/components/send-crypto-form";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { CreditCardIcon, Landmark, LayoutGrid, Lightbulb, BrainCircuitIcon } from "lucide-react";

// Interfaces
interface QuickAction {
	id: string;
	label: string;
	icon: React.ReactNode;
	flowComponent: React.ReactNode | string;
	visible: boolean;
	link: string;
}

const actions: QuickAction[] = [
	{
		id: "loans",
		icon: <Landmark />,
		label: "Loans",
		flowComponent: "",
		visible: false,
		link: "",
	},
	{
		id: "lifestyle",
		icon: <LayoutGrid />,
		label: "Lifestyle",
		flowComponent: "",
		visible: true,
		link: "/lifestyle",
	},
	{
		id: "learn",
		icon: <Lightbulb />,
		label: "Learn",
		flowComponent: "",
		visible: true,
		link: "",
	},
	{
		id: "large-payout",
		icon: <BrainCircuitIcon />,
		label: "AI",
		flowComponent: "",
		visible: true,
		link: "",
	},
	{
		id: "gifts",
		icon: <CreditCardIcon />,
		label: "Gifts",
		flowComponent: "",
		visible: false,
		link: "",
	},
];

const QuickActionCard = () => {
	return (
		<div className="bg-card p-6 rounded-2xl">
			<h2 className="text-xl font-semibold mb-6 text-foreground dark:text-foreground">Quick actions</h2>
			<div className="grid grid-cols-3 gap-4">
				{actions
					.filter((action) => action.visible)
					.map((action) => (
						<Link key={action.id} to={action.link || "#"}>
							<div className="bg-card p-6 border rounded-lg flex flex-col items-center gap-2  transition">
								<div className="text-2xl text-primary">{action.icon}</div>
								<span>{action.label}</span>
							</div>
						</Link>
					))}
			</div>
		</div>
	);
};

// Reusable button (optional export if needed elsewhere)
interface QuickActionButtonProps {
	icon: React.ReactNode;
	label: string;
	onClick?: () => void;
	className?: string;
}

export const QuickActionButton = ({
	icon,
	label,
	onClick,
	className,
}: QuickActionButtonProps) => {
	return (
		<button
			onClick={onClick}
			className={`flex flex-col items-center justify-center p-4 bg-background hover:bg-muted border border-border rounded-lg shadow-sm transition-all duration-200 space-y-2 dark:bg-background dark:hover:bg-muted dark:border-border ${className}`}
		>
			<div className="text-primary">{icon}</div>
			<span className="text-sm font-medium text-center text-foreground dark:text-foreground">
				{label}
			</span>
		</button>
	);
};

export default QuickActionCard;
