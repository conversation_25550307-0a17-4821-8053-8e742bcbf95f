import React from "react";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import {
	CircleArrowDataTransferVerticalIcon,
	CircleArrowDownRightIcon,
	CircleArrowUpRightIcon,
} from "./crypto-actions";
import { ConvertFiatFlow } from "./fiat/convert";
import { WithdrawFiatFlow } from "./fiat/withdraw";
import { DepositFiatFlow } from "./fiat/deposit";


interface FiatAction {
	id: string;
	icon: React.ReactNode;
	label: string;
	flowComponent: React.ReactNode;
}


const ActionButton: React.FC<{
	icon: React.ReactNode;
	label: string;
	onClick: () => void;
}> = ({ icon, label, onClick }) => {
	return (
		<div
			onClick={onClick}

			className="flex flex-col items-center bg-gray-100 dark:bg-card py-6 px-4 rounded-xl cursor-pointer hover:bg-gray-200 dark:hover:bg-muted border border-border transition-colors"
		>
			<div className="mb-2 text-primary">{icon}</div>
			<span className="text-base text-foreground dark:text-foreground">{label}</span>
		</div>
	);
};


export const FiatActions: React.FC = () => {
	const { openDrawer } = useDrawer();

	
	const actions: FiatAction[] = [
		{
			id: "deposit",
			icon: <CircleArrowDownRightIcon />,
			label: "Deposit",
			flowComponent: <DepositFiatFlow />,
		},
		{
			id: "withdraw",
			icon: <CircleArrowUpRightIcon />,
			label: "Withdraw",
			flowComponent: <WithdrawFiatFlow />,
		},
		{
			id: "convert",
			icon: <CircleArrowDataTransferVerticalIcon />,
			label: "Convert",
			flowComponent: <ConvertFiatFlow />,
		},
	];

	
	const handleOpenAction = (action: FiatAction) => {
		openDrawer({
			view: action.flowComponent,
			placement: "right",
			customSize: "480px",
		});
	};

	return (
		<div className="grid grid-cols-3 gap-4">
			{actions.map((action) => (
				<ActionButton
					key={action.id}
					icon={action.icon}
					label={action.label}
					onClick={() => handleOpenAction(action)}
				/>
			))}
		</div>
	);
};
