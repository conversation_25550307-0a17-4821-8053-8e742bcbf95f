import { GenericList } from "@/components/generic-list";
import RecentTransactionsSkeleton from "@/components/loaders/recent-transactions-skeleton";
import FullTransactionSkeleton from "@/components/loaders/full-transaction-skeleton";
import TransactionItem from "./transaction-item";
import { Link } from "react-router-dom";

export interface Transaction {
    id: number;
    type: string;
    time: string;
    date: string;
    account: string;
    amount: string;
    status: "Successful" | "Failed" | "Processing" | "Pending";
    user_id?: string;
    transaction_id?: string; 
}

interface RecentTransactionsProps {
    useTransactionsHook: () => {
        transactions: Transaction[];
        loading: boolean;
        error: string | null;
    };
    title?: string;
    useFullSkeleton?: boolean;
    seeAllLink?: string;
    seeAllText?: string;
    showSeeAll?: boolean;
    onTransactionClick?: (transaction: Transaction) => void;
}

export default function RecentTransactions({
    useTransactionsHook,
    title = "Recent Transactions",
    useFullSkeleton = false,
    seeAllLink = "/fiat-transactions",
    seeAllText = "See all",
    showSeeAll = true,
    onTransactionClick,
}: RecentTransactionsProps) {
    const { transactions, loading, error } = useTransactionsHook();
   
    return (
        <div className="bg-card border border-border p-6 rounded-2xl shadow-sm">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-foreground">{title}</h2>
                {showSeeAll && (
                    <Link to={seeAllLink}>
                        <button className="font-semibold text-primary cursor-pointer">
                            {seeAllText}
                        </button>
                    </Link>
                )}
            </div>
           
            {loading ? (
                useFullSkeleton ? <FullTransactionSkeleton /> : <RecentTransactionsSkeleton />
            ) : error ? (
                <p className="text-red-500">{error}</p>
            ) : (
                <div className="max-h-120 overflow-y-auto pr-2">
                    <GenericList
                        items={transactions}
                        renderItem={(transaction) => (
                            <TransactionItem 
                                transaction={transaction} 
                                onClick={onTransactionClick} 
                            />
                        )}
                        spacing="normal"
                        emptyMessage="No recent transactions"
                        className="rounded-lg space-y-6"
                    />
                </div>
            )}
        </div>
    );
}