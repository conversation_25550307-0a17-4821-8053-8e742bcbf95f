import type React from "react";
import { useState, useEffect, useRef } from "react";
import { ChevronDown, Search, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { ICryptoWallet } from "@/types/crypto-wallets";

export interface Wallet {
	id: string;
	name: string;
	symbol: string;
	icon: React.ReactNode;
	balance: number;
}

interface WalletSelectorProps {
	wallets: ICryptoWallet[];
	selectedWallet: ICryptoWallet | null;
	onSelect: (wallet: ICryptoWallet) => void;
	onAddNew: () => void;
	className?: string;
}

export const WalletSelector: React.FC<WalletSelectorProps> = ({
	wallets,
	selectedWallet,
	onSelect,
	onAddNew,
	className,
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const dropdownRef = useRef<HTMLDivElement>(null);

	const filteredWallets = wallets.filter(
		(wallet) =>
			wallet.coin_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			(wallet.image && wallet.image.toLowerCase().includes(searchTerm.toLowerCase())),
	);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const getWalletIcon = (wallet: ICryptoWallet) => {
		if (wallet.image) {
			return (
				<div className="size-8 rounded-full overflow-hidden flex items-center justify-center">
					<img
						src={wallet.image}
						alt={wallet.currency}
						className="w-full h-full object-cover"
					/>
				</div>
			);
		}

		return (
			<div className="size-8 rounded-full bg-gray-500 flex items-center justify-center text-white font-bold overflow-hidden">
				{wallet.currency.charAt(0)}
			</div>
		);
	};

	return (
		<div className={cn("relative w-full", className)} ref={dropdownRef}>
			{/* Select Trigger */}
			<motion.div
				className="w-full px-5 py-4 flex items-center justify-between rounded-full border border-gray-300 bg-white dark:bg-input dark:border-input cursor-pointer"
				onClick={() => setIsOpen(!isOpen)}
				whileHover={{ borderColor: "#d1d5db" }} // Consider dark mode hover for border as well if needed
				whileTap={{ scale: 0.99 }}
				initial={{ boxShadow: "0 0 0 rgba(0,0,0,0)" }}
				animate={{
					boxShadow: isOpen
						? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)" // Consider dark shadow
						: "0 0 0 rgba(0,0,0,0)",
				}}
				transition={{ duration: 0.2 }}
			>
				<span
					className={cn(
						selectedWallet ? "text-gray-900 dark:text-white" : "text-gray-500 dark:text-gray-400"
					)}
				>
					{selectedWallet ? selectedWallet.coin_name : "Choose Coin"}
				</span>
				<motion.div
					animate={{ rotate: isOpen ? 180 : 0 }}
					transition={{
						duration: 0.3,
						type: "spring",
						stiffness: 300,
						damping: 20,
					}}
				>
					<ChevronDown className="h-5 w-5 text-gray-400 dark:text-gray-500" />
				</motion.div>
			</motion.div>

			{/* Dropdown Content */}
			<AnimatePresence>
				{isOpen && (
					<motion.div
						className="absolute z-50 mt-1 w-full bg-white dark:bg-card border border-gray-200 dark:border-border rounded-3xl shadow-lg dark:shadow-xl overflow-hidden"
						initial={{ opacity: 0, height: 0, y: -10 }}
						animate={{ opacity: 1, height: "auto", y: 0 }}
						exit={{ opacity: 0, height: 0, y: -10 }}
						transition={{
							duration: 0.2,
							type: "spring",
							stiffness: 500,
							damping: 30,
						}}
					>
						<div className="p-6">
							<div className="mb-5 flex justify-between items-center">
								<motion.h3
									className="text-3xl font-bold text-gray-800 dark:text-white"
									initial={{ opacity: 0, x: -10 }}
									animate={{ opacity: 1, x: 0 }}
									transition={{ delay: 0.1, duration: 0.2 }}
								>
									Wallets
								</motion.h3>
								<motion.div
									initial={{ opacity: 0, x: 10 }}
									animate={{ opacity: 1, x: 0 }}
									transition={{ delay: 0.1, duration: 0.2 }}
								>
									<Button
										onClick={(e) => {
											e.stopPropagation();
											onAddNew();
										}}
										className="py-2 px-6 flex items-center gap-2 bg-primary hover:bg-primary/90 text-white rounded-full border-none font-medium"
									>
										<span>Add New</span>
										<Plus className="h-5 w-5" />
									</Button>
								</motion.div>
							</div>

							<motion.div
								className="relative mb-5"
								initial={{ opacity: 0, y: -10 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: 0.15, duration: 0.2 }}
							>
								<div className="relative">
									<Search className="h-5 w-5 text-gray-400 dark:text-gray-500 absolute left-5 top-1/2 transform -translate-y-1/2" />
									<Input
										type="text"
										placeholder="Search"
										value={searchTerm}
										onChange={(e) =>
											setSearchTerm(e.target.value)
										}
										className="pl-14 pr-5 py-6 w-full rounded-full border border-gray-200 dark:border-input dark:bg-input text-lg dark:text-white dark:placeholder-gray-400"
										onClick={(e) => e.stopPropagation()}
									/>
								</div>
							</motion.div>

							<motion.div
								className="max-h-[400px] overflow-y-auto pr-2 -mr-2"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 0.2, duration: 0.2 }}
							>
								{filteredWallets.length > 0 ? (
									<>
										{filteredWallets.map(
											(wallet, index) => (
												<motion.div
													key={wallet.id}
													className={cn(
														"flex items-center justify-between px-4 py-1 rounded-full cursor-pointer mb-2 overflow-hidden",
														selectedWallet?.id ===
															wallet.id
															? "bg-gray-600 text-white dark:bg-slate-600 dark:text-white" // Slightly adjusted selected style for dark if needed
															: "hover:bg-gray-100 dark:hover:bg-slate-700",
													)}
													onClick={(e) => {
														e.stopPropagation();
														onSelect(wallet);
														setIsOpen(false);
													}}
													initial={{
														opacity: 0,
														y: 20,
													}}
													animate={{
														opacity: 1,
														y: 0,
													}}
													exit={{ opacity: 0, y: 20 }}
													transition={{
														delay:
															0.1 + index * 0.05,
														duration: 0.2,
														type: "spring",
														stiffness: 300,
														damping: 25,
													}}
													whileTap={{ scale: 0.98 }}
												>
													<div className="flex items-center gap-4">
														<motion.div
															whileHover={{
																scale: 1.1,
															}}
															whileTap={{
																scale: 0.95,
															}}
															transition={{
																duration: 0.2,
															}}
														>
															{getWalletIcon(
																wallet,
															)}
														</motion.div>
														<div>
															<div
																className={cn(
																	"text-lg font-semibold",
																	selectedWallet?.id ===
																		wallet.id
																		? "text-white"
																		: "text-gray-800 dark:text-white",
																)}
															>
																{
																	wallet.coin_name
																}
															</div>
															<div
																className={cn(
																	"text-sm",
																	selectedWallet?.id ===
																		wallet.id
																		? "text-gray-200"
																		: "text-gray-500 dark:text-gray-400",
																)}
															>
																{
																	wallet.currency
																}
															</div>
														</div>
													</div>
													<motion.div
														className={cn(
															"text-lg font-bold",
															selectedWallet?.id ===
																wallet.id
																? "text-white"
																: "text-gray-800 dark:text-white",
														)}
														initial={{ scale: 0.9 }}
														animate={{ scale: 1 }}
														transition={{
															delay:
																0.2 +
																index * 0.05,
															duration: 0.2,
														}}
													>
														{wallet.balance.toFixed(
															2,
														)}
													</motion.div>
												</motion.div>
											),
										)}
									</>
								) : (
									<motion.div
										className="p-4 text-center text-gray-500 dark:text-gray-400 text-lg"
										initial={{ opacity: 0 }}
										animate={{ opacity: 1 }}
										transition={{
											delay: 0.2,
											duration: 0.2,
										}}
									>
										No wallets found
									</motion.div>
								)}
							</motion.div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
};
