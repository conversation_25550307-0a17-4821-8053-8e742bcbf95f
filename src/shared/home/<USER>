// import { Coins } from "lucide-react";
// import BalanceDisplay from "./balance-display";
// import { CryptoActions } from "./crypto-actions";
// import { formatAmount } from "@/utils/format-amount";
// import { useCoinsMarketData, useCoinsMarketChart } from "@/hooks/api/crypto-portfolio";
// import RecentTransactions from "./recent-transactions";
// import { useCryptoTransactions } from "@/hooks/api/userCryptoTransactions";
// import {
//     LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid
// } from "recharts";

// interface MyPortfolioDrawerProps {
//     selectedCrypto: any;
// }

// export default function MyPortfolioDrawer({ selectedCrypto }: MyPortfolioDrawerProps) {
//     const { data: marketData, isLoading: marketLoading } = useCoinsMarketData({
//         currency: "usd",
//         page: 1,
//         size: 10
//     });

//     // Prepare fallback coin_id for hook call
//     const coinId = selectedCrypto?.currency || "bitcoin";

//     // Always call the hook, even if selectedCrypto is not available
//     const { data: marketChartData, isLoading: chartLoading } = useCoinsMarketChart({
//         coin_id: coinId,
//         currency: "usd",
//         days: 7,
//         interval: "hourly",
//     });

//     // useEffect(() => {
//     //     console.log("Chart Data:", marketChartData);
//     // }, [marketChartData]);

//     const formattedChartData = Array.isArray(marketChartData?.data?.coin_data)
//         ? marketChartData.data.coin_data.map(([timestamp, price]) => ({
//             time: new Date(timestamp * 1000).toLocaleDateString("en-US", {
//                 month: "short",
//                 day: "numeric",
//             }),
//             price,
//         }))
//         : [];

//     // Early return if selectedCrypto is not available
//     if (!selectedCrypto) {
//         return (
//             <div className="p-6 flex items-center justify-center">
//                 <div className="text-center">
//                     <p className="text-gray-500">Loading crypto data...</p>
//                 </div>
//             </div>
//         );
//     }

//     const cryptoBalance = selectedCrypto?.balance && selectedCrypto.currency
//         ? `${formatAmount(selectedCrypto.balance, selectedCrypto.currency)} ${selectedCrypto.currency}`
//         : "0.00000000";

//     const getMarketData = () => {
//         if (!marketData?.data?.coins || !selectedCrypto?.currency) {
//             return null;
//         }

//         return marketData.data.coins.find(coin =>
//             coin?.currency?.toLowerCase() === selectedCrypto?.currency?.toLowerCase()
//         ) || null;
//     };

//     const cryptoMarketData = getMarketData();

//     const formatPrice = (price) => {
//         if (!price) return "N/A";
//         return new Intl.NumberFormat('en-US', {
//             style: 'currency',
//             currency: 'USD',
//             minimumFractionDigits: 2,
//             maximumFractionDigits: 4
//         }).format(price);
//     };

//     const formatPercentageChange = (change) => {
//         if (change === undefined || change === null) return "N/A";
//         const isPositive = change >= 0;
//         return (
//             <span className={isPositive ? "text-green-600" : "text-red-600"}>
//                 {isPositive ? "+" : ""}{change.toFixed(2)}%
//             </span>
//         );
//     };

//     const getCryptoImage = () => {
//         if (selectedCrypto?.image) return selectedCrypto.image;
//         if (cryptoMarketData?.image) return cryptoMarketData.image;
//         if (selectedCrypto?.currency) return `src/assets/images/crypto/${selectedCrypto.currency.toLowerCase()}.png`;
//         return null;
//     };

//     const cryptoImage = getCryptoImage();

//     return (
//         <div className="p-6 space-y-6">
//             {/* Header */}
//             <div className="flex items-center gap-4">
//                 <div>
//                     {cryptoImage ? (
//                         <img
//                             src={cryptoImage}
//                             alt={selectedCrypto?.coin_name || selectedCrypto?.currency}
//                             className="w-16 h-16 rounded-full object-cover"
//                             onError={(e) => {
//                                 e.currentTarget.style.display = 'none';
//                                 e.currentTarget.nextElementSibling?.classList.remove('hidden');
//                             }}
//                         />
//                     ) : null}
//                     <div className={`w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center ${cryptoImage ? 'hidden' : ''}`}>
//                         <Coins className="w-8 h-8 text-amber-600" />
//                     </div>
//                 </div>

//                 <div>
//                     <h2 className="text-2xl font-bold text-gray-900">
//                         {selectedCrypto?.coin_name || cryptoMarketData?.name || "Unknown Crypto"}
//                     </h2>
//                     <p className="text-lg text-gray-500">
//                         {selectedCrypto?.currency || cryptoMarketData?.currency || ""}
//                     </p>
//                 </div>
//             </div>

//             {/* Balance Display */}
//             <BalanceDisplay
//                 title="Balance"
//                 balance={cryptoBalance}
//                 balanceClassName="text-amber-500 text-xl font-bold mt-2"
//                 titleClassName="text-gray-500 text-lg font-normal"
//                 iconClassName="h-5 w-5 text-gray-700"
//                 showBalanceInitially={true}
//             />

//             {/* Chart */}
//             <div className="h-64 w-full">
//                 {chartLoading ? (
//                     <div className="text-gray-500">Loading chart...</div>
//                 ) : (
//                     <ResponsiveContainer width="100%" height="100%">
//                         <LineChart data={formattedChartData}>
//                             <CartesianGrid strokeDasharray="3 3" />
//                             <XAxis dataKey="time" />
//                             <YAxis domain={['auto', 'auto']} />
//                             <Tooltip />
//                             <Line
//                                 type="monotone"
//                                 dataKey="price"
//                                 stroke="#f59e0b"
//                                 strokeWidth={2}
//                                 dot={false}
//                             />
//                         </LineChart>
//                     </ResponsiveContainer>
//                 )}
//             </div>

//             {/* Price Information */}
//             <div className="flex items-center justify-between">
//                 <div>
//                     <p className="text-gray-600 text-sm">Current Price</p>
//                     <p className="font-bold text-gray-900">
//                         {marketLoading ? (
//                             <span className="animate-pulse bg-gray-200 h-6 w-24 rounded"></span>
//                         ) : (
//                             formatPrice(cryptoMarketData?.current_price)
//                         )}
//                     </p>

//                     <div className="flex place-items-center gap-2">
//                         <p className="text-sm">
//                             {marketLoading ? (
//                                 <span className="animate-pulse bg-gray-200 h-5 w-16 rounded"></span>
//                             ) : (
//                                 formatPercentageChange(cryptoMarketData?.price_change_percentage_24h)
//                             )}
//                         </p>
//                         <p className="text-gray-600 text-sm">currently</p>
//                     </div>
//                 </div>
//                 <button className="bg-gray-400 hover:bg-gray-500 transition-colors p-3 rounded-full cursor-pointer text-white text-sm">
//                     Set price alert
//                 </button>
//             </div>

//             {/* Crypto Actions */}
//             <div className="space-y-4">
//                 <CryptoActions />
//             </div>

//             <div className="text-center">
// <RecentTransactions 
//     useTransactionsHook={useCryptoTransactions} 
//     title="Crypto Transactions"
//     useFullSkeleton={true}
//     seeAllLink="/crypto-transactions"
//     seeAllText="See all"
// />
//             </div>
//         </div>
//     );
// }


import { Coins } from "lucide-react";
import BalanceDisplay from "./balance-display";
import { CryptoActions } from "./crypto-actions";
import { formatAmount } from "@/utils/format-amount";
import { useCoinsMarketData, useCoinsMarketChart } from "@/hooks/api/crypto-portfolio";
import RecentTransactions from "./recent-transactions";
import { useCryptoTransactions } from "@/hooks/api/userCryptoTransactions";
import {
    LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid
} from "recharts";

interface MyPortfolioDrawerProps {
    selectedCrypto: any;
}

export default function MyPortfolioDrawer({ selectedCrypto }: MyPortfolioDrawerProps) {
    const { data: marketData, isLoading: marketLoading } = useCoinsMarketData({
        currency: "usd",
        page: 1,
        size: 10
    });

    // Prepare fallback coin_id for hook call
    const coinId = selectedCrypto?.currency || "bitcoin";

    // Always call the hook, even if selectedCrypto is not available
    const { data: marketChartData, isLoading: chartLoading } = useCoinsMarketChart({
        coin_id: coinId,
        currency: "usd",
        days: 7,
        interval: "hourly",
    });

    const formattedChartData = Array.isArray(marketChartData?.data?.coin_data)
        ? marketChartData.data.coin_data.map(([timestamp, price]) => ({
            time: new Date(timestamp * 1000).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
            }),
            price,
        }))
        : [];

    // Early return if selectedCrypto is not available
    if (!selectedCrypto) {
        return (
            <div className="p-6 flex items-center justify-center">
                <div className="text-center">
                    <p className="text-gray-500">Loading crypto data...</p>
                </div>
            </div>
        );
    }

    const cryptoBalance = selectedCrypto?.balance && selectedCrypto.currency
        ? `${formatAmount(selectedCrypto.balance, selectedCrypto.currency)} ${selectedCrypto.currency}`
        : "0.00000000";

    const getMarketData = () => {
        if (!marketData?.data?.coins || !selectedCrypto?.currency) {
            return null;
        }

        return marketData.data.coins.find(coin =>
            coin?.currency?.toLowerCase() === selectedCrypto?.currency?.toLowerCase()
        ) || null;
    };

    const cryptoMarketData = getMarketData();

    const formatPrice = (price) => {
        if (!price) return "N/A";
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 4
        }).format(price);
    };

    const formatPercentageChange = (change) => {
        if (change === undefined || change === null) return "N/A";
        const isPositive = change >= 0;
        return (
            <span className={isPositive ? "text-green-600" : "text-red-600"}>
                {isPositive ? "+" : ""}{change.toFixed(2)}%
            </span>
        );
    };

    const getCryptoImage = () => {
        if (selectedCrypto?.image) return selectedCrypto.image;
        if (cryptoMarketData?.image) return cryptoMarketData.image;
        if (selectedCrypto?.currency) return `src/assets/images/crypto/${selectedCrypto.currency.toLowerCase()}.png`;
        return null;
    };

    const cryptoImage = getCryptoImage();

    // Create a custom hook function that filters transactions by selected currency
    const useFilteredCryptoTransactions = () => {
        return useCryptoTransactions(selectedCrypto?.currency);
    };

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <div>
                    {cryptoImage ? (
                        <img
                            src={cryptoImage}
                            alt={selectedCrypto?.coin_name || selectedCrypto?.currency}
                            className="w-16 h-16 rounded-full object-cover"
                            onError={(e) => {
                                e.currentTarget.style.display = 'none';
                                e.currentTarget.nextElementSibling?.classList.remove('hidden');
                            }}
                        />
                    ) : null}
                    <div className={`w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center ${cryptoImage ? 'hidden' : ''}`}>
                        <Coins className="w-8 h-8 text-amber-600" />
                    </div>
                </div>

                <div>
                    <h2 className="text-2xl font-bold text-gray-900">
                        {selectedCrypto?.coin_name || cryptoMarketData?.name || "Unknown Crypto"}
                    </h2>
                    <p className="text-lg text-gray-500">
                        {selectedCrypto?.currency || cryptoMarketData?.currency || ""}
                    </p>
                </div>
            </div>

            {/* Balance Display */}
            <BalanceDisplay
                title="Balance"
                balance={cryptoBalance}
                balanceClassName="text-amber-500 text-xl font-bold mt-2"
                titleClassName="text-gray-500 text-lg font-normal"
                iconClassName="h-5 w-5 text-gray-700"
                showBalanceInitially={true}
            />

            {/* Chart */}
            <div className="h-64 w-full">
                {chartLoading ? (
                    <div className="text-gray-500">Loading chart...</div>
                ) : (
                    <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={formattedChartData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" />
                            <YAxis domain={['auto', 'auto']} />
                            <Tooltip />
                            <Line
                                type="monotone"
                                dataKey="price"
                                stroke="#f59e0b"
                                strokeWidth={2}
                                dot={false}
                            />
                        </LineChart>
                    </ResponsiveContainer>
                )}
            </div>

            {/* Price Information */}
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-gray-600 text-sm">Current Price</p>
                    <p className="font-bold text-gray-900">
                        {marketLoading ? (
                            <span className="animate-pulse bg-gray-200 h-6 w-24 rounded"></span>
                        ) : (
                            formatPrice(cryptoMarketData?.current_price)
                        )}
                    </p>

                    <div className="flex place-items-center gap-2">
                        <p className="text-sm">
                            {marketLoading ? (
                                <span className="animate-pulse bg-gray-200 h-5 w-16 rounded"></span>
                            ) : (
                                formatPercentageChange(cryptoMarketData?.price_change_percentage_24h)
                            )}
                        </p>
                        <p className="text-gray-600 text-sm">currently</p>
                    </div>
                </div>
                <button className="bg-gray-400 hover:bg-gray-500 transition-colors p-3 rounded-full cursor-pointer text-white text-sm">
                    Set price alert
                </button>
            </div>

            {/* Crypto Actions */}
            <div className="space-y-4">
                <CryptoActions />
            </div>

            <div className="text-center">
                <RecentTransactions 
                    useTransactionsHook={useFilteredCryptoTransactions}
                    title={`${selectedCrypto?.currency?.toUpperCase() || 'Crypto'} Transactions`}
                    useFullSkeleton={true}
                    seeAllLink="/crypto-transactions"
                    seeAllText="See all"
                />
            </div>
        </div>
    );
}