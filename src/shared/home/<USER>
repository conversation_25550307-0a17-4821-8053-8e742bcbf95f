import { useFiatTransactions } from "@/hooks/api/userFiatTransactions"
import { Link } from "react-router-dom"
import RecentTransactions from "./recent-transactions"
import type { Transaction } from "./recent-transactions"
import { useDrawer } from "@/components/drawer-view/use-drawer";
import FiatTransactionDetails from "./fiat-transaction-details-drawer";
import { useState } from "react";

export default function FiatTransactions() {
    const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);
    const { openDrawer } = useDrawer();

    const handleTransactionClick = (transaction: Transaction) => {
        // console.log('Transaction ID:', transaction.transaction_id);
        setSelectedTransactionId(transaction.transaction_id);
        
        openDrawer({
            view: (
                <FiatTransactionDetails 
                    transactionId={transaction.transaction_id} 
                />
            ),
            placement: "right",
            customSize: "480px",
        });
    };

    return (
        <div className="p-4 space-y-6">
            <div className="flex gap-2 items-center">
                <Link to="/">
                    <img
                        src="src/assets/images/backbutton.png"
                        alt="back"
                        className="cursor-pointer"
                    />
                </Link>
                <p className="text-3xl font-bold">Fiat Transactions</p>
            </div>
            <div>
                <RecentTransactions
                    useTransactionsHook={useFiatTransactions}
                    title=""
                    useFullSkeleton={true}
                    onTransactionClick={handleTransactionClick}
                    seeAllText=""
                />
            </div>
        </div>
    )
}