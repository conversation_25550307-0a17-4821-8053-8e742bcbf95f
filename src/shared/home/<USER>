import { Transaction } from "./recent-transactions";

interface TransactionItemProps {
    transaction: Transaction; // Use the display-specific interface
    onClick?: (transaction: Transaction) => void;
}

export default function TransactionItem({ transaction, onClick }: TransactionItemProps) {
    const handleClick = () => {
        if (onClick) {
            onClick(transaction);
        }
    };

    return (
        <div 
            className={`transaction-item ${onClick ? 'cursor-pointer hover:bg-gray-50' : ''}`}
            onClick={handleClick}
        >
            <div className="flex justify-between items-center p-4">
                <div>
                    <p className="font-medium">{transaction.type}</p>
                    <p className="text-sm text-gray-500">{transaction.account}</p>
                    <p className="text-xs text-gray-400">{transaction.date} {transaction.time}</p>
                </div>
                <div className="text-right">
                    <p className="font-medium">{transaction.amount}</p>
                    <span className={`text-xs px-2 py-1 rounded ${
                        transaction.status === 'Successful' ? 'bg-green-100 text-green-800' :
                        transaction.status === 'Failed' ? 'bg-red-100 text-red-800' :
                        transaction.status === 'Processing' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                    }`}>
                        {transaction.status}
                    </span>
                </div>
            </div>
        </div>
    );
}