import { useState, useEffect } from 'react';
import { APP_CONFIG } from '@/config/index'; 

interface TransactionDetailsProps {
    transactionId: string;
}

interface TimelineDetail {
    description: string;
    timestamp: string | null;
}

interface TransactionDetail {
    account_name: string;
    currency: string;
    amount: string;
    fee: string;
    status: string;
    type: string;
    method: string;
    details: {
        type: string;
        details: TimelineDetail[];
    };
    user_note: string;
    analytics: any;
    sender: any;
    date: string;
}

interface ApiResponse {
    message: string;
    data: TransactionDetail;
}

export default function CryptoTransactionDetails({ transactionId }: TransactionDetailsProps) {
    const [transactionDetails, setTransactionDetails] = useState<TransactionDetail | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchTransactionDetails = async () => {
            if (!transactionId) return;

            try {
                setLoading(true);
                setError(null);

                const apiUrl = `${APP_CONFIG.API_URLS.CURRENCY}/crypto-banking/get-transaction?transaction_id=${transactionId}`;
                const token = localStorage.getItem("auth_token");
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                    },
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch transaction details: ${response.status}`);
                }

                const apiResponse: ApiResponse = await response.json();
                // Extract the actual transaction data from the API response
                setTransactionDetails(apiResponse.data);
            } catch (err) {
                console.error('Error fetching transaction details:', err);
                setError(err instanceof Error ? err.message : 'An error occurred');
            } finally {
                setLoading(false);
            }
        };

        fetchTransactionDetails();
    }, [transactionId]);

    if (loading) {
        return (
            <div className="p-6">
                <div className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded mb-4"></div>
                    <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="text-red-500 text-center">
                    <p className="font-semibold">Error loading transaction details</p>
                    <p className="text-sm mt-2">{error}</p>
                </div>
            </div>
        );
    }

    if (!transactionDetails) {
        return (
            <div className="p-6">
                <div className="text-gray-500 text-center">
                    <p>No transaction details found</p>
                </div>
            </div>
        );
    }

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'completed':
            case 'success':
            case 'successful':
                return 'bg-green-100 text-green-800';
            case 'pending':
            case 'processing':
            case 'created':
                return 'bg-yellow-100 text-yellow-800';
            case 'failed':
            case 'failure':
            case 'error':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="p-6 space-y-6">
            <div className="border-b pb-4">
                <h2 className="text-2xl font-bold">Transaction Details</h2>
            </div>
            
            <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                    {/* <div>
                        <label className="text-sm font-medium text-gray-500">Transaction ID</label>
                        <p className="text-sm font-mono bg-gray-100 p-2 rounded">{transactionId}</p>
                    </div> */}
                    
                    <div>
                        <label className="text-sm font-medium text-gray-500">Account Name</label>
                        <p className="text-base">{transactionDetails.account_name}</p>
                    </div>
                    
                    <div>
                        <label className="text-sm font-medium text-gray-500">Amount</label>
                        <p className="text-lg font-semibold">{transactionDetails.amount} {transactionDetails.currency}</p>
                    </div>
                    
                    <div>
                        <label className="text-sm font-medium text-gray-500">Fee</label>
                        <p className="text-base">{transactionDetails.fee} {transactionDetails.currency}</p>
                    </div>
                    
                    <div>
                        <label className="text-sm font-medium text-gray-500">Status</label>
                        <span className={`inline-block px-2 py-1 rounded text-sm font-medium ${getStatusColor(transactionDetails.status)}`}>
                            {transactionDetails.status}
                        </span>
                    </div>
                    
                    <div>
                        <label className="text-sm font-medium text-gray-500">Type</label>
                        <p className="text-base">{transactionDetails.type}</p>
                    </div>
                    
                    <div>
                        <label className="text-sm font-medium text-gray-500">Method</label>
                        <p className="text-base">{transactionDetails.method}</p>
                    </div>
                    
                    <div>
                        <label className="text-sm font-medium text-gray-500">Date</label>
                        <p className="text-base">{new Date(transactionDetails.date).toLocaleString()}</p>
                    </div>

                    {transactionDetails.user_note && (
                        <div>
                            <label className="text-sm font-medium text-gray-500">User Note</label>
                            <p className="text-base">{transactionDetails.user_note}</p>
                        </div>
                    )}
                </div>

                {/* Timeline Section */}
                {transactionDetails.details?.details && transactionDetails.details.details.length > 0 && (
                    <div className="mt-6">
                        <h3 className="text-lg font-semibold mb-4">Transaction Timeline</h3>
                        <div className="space-y-3">
                            {transactionDetails.details.details.map((item, index) => (
                                <div key={index} className="flex items-start space-x-3">
                                    <div className="flex-shrink-0 w-2 h-2 bg-primary rounded-full mt-2"></div>
                                    <div className="flex-grow">
                                        <p className="text-sm text-gray-900">{item.description}</p>
                                        {item.timestamp && (
                                            <p className="text-xs text-gray-500 mt-1">
                                                {new Date(item.timestamp).toLocaleString()}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}