import React, { useMemo } from "react";
import { <PERSON> } from "react-router-dom";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { Skeleton } from "@/components/ui/skeleton";
import { PortfolioItem } from "./portfolio";
import MyPortfolioDrawer from "./my-portfolio-drawer";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { usePortfolioOrder } from "@/hooks/api/portfolio-order"; // Using shared hook

import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

interface MyPortfolioProps {
  userId: string;
}

export default function MyPortfolio({ userId }: MyPortfolioProps) {
    const { data: cryptoWallets, isLoading: cryptoLoading } = useCryptoUserWallets();
    const cryptoAssets = useMemo(
        () => cryptoWallets?.slice(0, 10) || [],
        [cryptoWallets]
    );
    const { openDrawer } = useDrawer();
    
    // Use the same shared hook as PortfolioSection
    const { items, updateOrder } = usePortfolioOrder(cryptoAssets, userId);
    
    const sensors = useSensors(useSensor(PointerSensor));

    const handleDragEnd = (event: any) => {
        const { active, over } = event;
        if (active.id !== over.id) {
            const oldIndex = items.indexOf(active.id);
            const newIndex = items.indexOf(over.id);
            const newOrder = arrayMove(items, oldIndex, newIndex);
            updateOrder(newOrder);
        }
    };

    const cryptoMap: Record<string, ICryptoWallet> = useMemo(
        () => Object.fromEntries(cryptoAssets.map((c) => [c.id, c])),
        [cryptoAssets]
    );
    
    const handleCryptoClick = (crypto: ICryptoWallet) => {
        // Add validation before opening drawer
        if (!crypto) {
            console.error('No crypto data provided');
            return;
        }
        
        openDrawer({
            view: (
                <MyPortfolioDrawer selectedCrypto={crypto} />
            ),
            placement: "right",
            customSize: "480px",
        });
    };
    
    return (
        <div className="p-4 space-y-6">
            <div className="flex gap-2 items-center">
                <Link to="/">
                    <img
                        src="src/assets/images/backbutton.png"
                        alt="back"
                        className="cursor-pointer"
                    />
                </Link>
                <p className="text-3xl font-bold">Portfolio</p>
            </div>
            <div className="bg-white dark:bg-gray-900 p-6 rounded-2xl">
                {cryptoLoading ? (
                    <div className="space-y-4">
                        {[...Array(5)].map((_, index) => (
                            <div key={index} className="flex items-center gap-4 p-4">
                                <Skeleton className="h-12 w-12 rounded-full" />
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-3 w-12" />
                                </div>
                                <div className="ml-auto space-y-2">
                                    <Skeleton className="h-4 w-32" />
                                    <Skeleton className="h-3 w-24" />
                                </div>
                            </div>
                        ))}
                    </div>
                ) : cryptoAssets.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        No cryptocurrencies in your portfolio
                    </div>
                ) : (
                    <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                        <SortableContext items={items} strategy={verticalListSortingStrategy}>
                            <div className="space-y-3">
                                {items.map((id) => {
                                    const crypto = cryptoMap[id];
                                    if (!crypto) return null;

                                    return (
                                        <div
                                            key={id}
                                            onClick={() => handleCryptoClick(crypto)}
                                        >
                                            <PortfolioItem crypto={crypto} />
                                        </div>
                                    );
                                })}
                            </div>
                        </SortableContext>
                    </DndContext>
                )}
            </div>
        </div>
    );
}