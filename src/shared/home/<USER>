import { useEffect, useRef } from "react";
import { Tab } from "@/pages/dashbaord/home";

interface HomeTabsProps {
	tabs: Tab[];
	activeTab: Tab;
	onTabChange: (tab: Tab) => void;
}

export function AnimatedHomeTabs({
	tabs,
	activeTab,
	onTabChange,
}: HomeTabsProps) {
	const indicatorRef = useRef<HTMLDivElement>(null);
	const buttonsRef = useRef<(HTMLButtonElement | null)[]>([]);
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const activeIndex = tabs.findIndex((tab) => tab.id === activeTab.id);
		const activeButton = buttonsRef.current[activeIndex];

		if (activeButton && indicatorRef.current && scrollContainerRef.current) {
			const { offsetLeft, offsetWidth } = activeButton;

			// Animate the indicator
			indicatorRef.current.style.width = `${offsetWidth}px`;
			indicatorRef.current.style.transform = `translateX(${offsetLeft}px)`;

			// Scroll into view if overflowed
			const container = scrollContainerRef.current;
			const containerRect = container.getBoundingClientRect();
			const buttonRect = activeButton.getBoundingClientRect();

			if (buttonRect.left < containerRect.left || buttonRect.right > containerRect.right) {
				activeButton.scrollIntoView({ behavior: "smooth", inline: "center" });
			}
		}
	}, [activeTab, tabs]);

	return (
		<div className="relative mb-8">
			{/* Scrollable tab container */}
			<div
				ref={scrollContainerRef}
				className="flex gap-6 overflow-x-auto scrollbar-hide relative px-2"
			>
				{/* Sliding indicator */}
				{/* <div
					ref={indicatorRef}
					className="absolute bottom-0 h-1 bg-primary rounded-full transition-all duration-300 ease-in-out"
					style={{
						width: "0px",
						transform: "translateX(0)",
					}}
				/> */}

				{/* Tab buttons */}
				{tabs.map((tab, index) => (
					<button
						key={tab.id}
						ref={(el) => {
							buttonsRef.current[index] = el;
						}}
						className={`px-8 py-3 rounded-full whitespace-nowrap transition-all duration-200 font-medium shadow-sm ${
	activeTab.id === tab.id
		? "bg-primary text-primary-foreground shadow-md"
		: "bg-card text-foreground hover:bg-muted hover:text-foreground border border-border"
}`}

						onClick={() => onTabChange(tab)}
					>
						{tab.title}
					</button>
				))}
			</div>
		</div>
	);
}
