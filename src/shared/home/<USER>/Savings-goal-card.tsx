import { SavingsDetails } from "@/types/fiat-savings";
import { format } from "date-fns";

interface SavingsGoalCardProps {
	goal: SavingsDetails;
	key?:string;
	onClick: () => void;
	colorClass: string;
	badgeBgClass: string;
}

export const SavingsGoalCard: React.FC<SavingsGoalCardProps> = ({
	goal,
	onClick,
	colorClass,
	badgeBgClass,
}) => {
	return (
		<div
			onClick={onClick}
			className={`p-4 rounded-xl shadow-md cursor-pointer flex flex-col gap-2 ${colorClass}`}
		>
			<div className="flex justify-end">
				<span className={`${badgeBgClass} px-2 py-1 text-xs rounded-full font-semibold`}>
					{goal.currency} {goal.amountSaved}
				</span>
			</div>

			<div>
				<h3 className="text-lg font-semibold">{goal.title}</h3>
				<p className="text-sm ">
					{format(new Date(goal.startDate), "dd/MM/yyyy")}-
					{format(new Date(goal.endDate), "dd/MM/yyyy")}
				</p>
			</div>
		</div>
	);
};
