import { Button } from "@/components/custom/button";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { CreateSavingsGoalForm } from "../savings/create-savings-goal";
import { useEffect, useState, useMemo } from "react";
import { SavingsGoalDetails } from "../savings/savings-goal-details";
import { SavingsGoalCard } from "../savings/Savings-goal-card";
import { SavingsGoal, SavingsDetails } from "@/types/fiat-savings";
import { SavingsGoalCardSkeleton } from "../savings/savings-goal-card-skeleton";
import { Link } from "react-router-dom";
import { useAllGoalsStore } from "@/store/useGoalStore";

const SavingsView = () => {
	const { openDrawer, closeDrawer } = useDrawer();
	const [drawerKey, setDrawerKey] = useState(0);
	const [selectedGoal, setSelectedGoal] = useState<SavingsDetails | null>(
		null,
	);
	const [activeTab, setActiveTab] = useState<"fiat" | "crypto">("fiat");

	const {
		fiatGoals: storeFiatGoals,
		cryptoGoals: storeCryptoGoals,
		isLoading: storeIsLoading,
		error: storeError, 
		fetchAllGoals,
	} = useAllGoalsStore();

	useEffect(() => {
		fetchAllGoals();
	}, [fetchAllGoals]);

	const isLoading = storeIsLoading;

	const displayedGoals: SavingsDetails[] = useMemo(() => {
		const currentGoalsRaw =
			activeTab === "fiat" ? storeFiatGoals : storeCryptoGoals;
		const actualGoals: SavingsGoal[] = Array.isArray(currentGoalsRaw)
			? currentGoalsRaw
			: [];

		return actualGoals.slice(0, 3).map(
			(goalItem: SavingsGoal): SavingsDetails => ({
				...goalItem,
				fundingSource:
					activeTab === "fiat" ? "Fiat Account" : "Crypto Wallet",
				accountName:
					goalItem.currency+ " Account" ||
					(activeTab === "fiat" ? "Default Fiat Account" : "Default Crypto Wallet"),
			}),
		);
	}, [storeFiatGoals, storeCryptoGoals, activeTab]);

	const handleCardClick = (goal: SavingsDetails) => {
		setSelectedGoal(goal);
	};

	const handleSavingsDrawer = () => {
		openDrawer({
			view: (
				<CreateSavingsGoalForm
					key={drawerKey}
					onClose={closeDrawer}
					onGoalCreated={(newGoalData) => {
						fetchAllGoals(); 
						setSelectedGoal(newGoalData as SavingsDetails);
						closeDrawer();
					}}
				/>
			),
			placement: "right",
			customSize: "480px",
		});
		setDrawerKey((prev) => prev + 1);
	};
	if (selectedGoal) {
		return (
			<SavingsGoalDetails
				goal={selectedGoal}
				onBack={() => setSelectedGoal(null)}
			/>
		);
	}

	const SavingsSkeleton = () => {
		return (
			<div className="flex gap-6">
				{[...Array(3)].map((_, i) => (
					<SavingsGoalCardSkeleton key={i} />
				))}
			</div>
		);
	};

	function Savings() {
		if (isLoading) {
			return (
				<>
					{/* Tab switcher */}
					<div className="flex space-x-4 border-b border-border pb-4 mb-6">
						<button
							onClick={() => setActiveTab("fiat")}
							className={`pb-2 px-6 rounded-t-lg transition-all duration-300 ${
								activeTab === "fiat"
									? "text-primary bg-primary/10 border-b-4 border-primary font-semibold"
									: "text-muted-foreground hover:text-primary hover:bg-primary/5"
							}`}
						>
							Fiat Savings
						</button>
						<button
							onClick={() => setActiveTab("crypto")}
							className={`pb-2 px-6 rounded-t-lg transition-all duration-300 ${
								activeTab === "crypto"
									? "text-primary bg-primary/10 border-b-4 border-primary font-semibold"
									: "text-muted-foreground hover:text-primary hover:bg-primary/5"
							}`}
						>
							Crypto Savings
						</button>
					</div>
					<SavingsSkeleton />
				</>
			);
		}

		const colorStyles = [
			{
				colorClass:
					"bg-[#d6ffe2] border-emerald-200 hover:shadow-lg transition-shadow duration-200 dark:bg-emerald-900 dark:border-emerald-700",
				badgeBgClass: "bg-emerald-600 text-white",
			},
			{
				colorClass:
					"bg-[#ffebeb] border-rose-200 hover:shadow-lg transition-shadow duration-200 dark:bg-rose-900 dark:border-rose-700",
				badgeBgClass: "bg-red-600 text-white",
			},
			{
				colorClass:
					"bg-[#fff1de] border-amber-200 hover:shadow-lg transition-shadow duration-200 dark:bg-amber-900 dark:border-amber-700",
				badgeBgClass: "bg-orange-500 text-white",
			},
		];

		return (
			<>
				{/* Tab switcher */}
				<div className="flex space-x-4 border-b border-border pb-4 mb-6">
					<button
						onClick={() => setActiveTab("fiat")}
						className={`pb-2 px-6 rounded-t-lg transition-all duration-300 ${
							activeTab === "fiat"
								? "text-primary bg-primary/10 border-b-4 border-primary font-semibold"
								: "text-muted-foreground hover:text-primary hover:bg-primary/5"
						}`}
					>
						Fiat Savings
					</button>
					<button
						onClick={() => setActiveTab("crypto")}
						className={`pb-2 px-6 rounded-t-lg transition-all duration-300 ${
							activeTab === "crypto"
								? "text-primary bg-primary/10 border-b-4 border-primary font-semibold"
								: "text-muted-foreground hover:text-primary hover:bg-primary/5"
						}`}
					>
						Crypto Savings
					</button>
				</div>

				{!displayedGoals || displayedGoals.length === 0 ? (
					<div className="flex flex-col items-center py-3 text-foreground space-y-4">
						<h1 className="text-xl font-semibold">
							My {activeTab === "fiat" ? "Fiat" : "Crypto"}{" "}
							Savings Goals
						</h1>
						<p className="text-muted-foreground">
							Create a savings goal and build wealth easily
						</p>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
						{displayedGoals.map((goal, index) => (
							<SavingsGoalCard
								key={
									goal.id
										? goal.id.toString()
										: index.toString()
								}
								goal={goal}
								onClick={() => handleCardClick(goal)}
								colorClass={
									colorStyles[index % colorStyles.length]
										.colorClass
								}
								badgeBgClass={
									colorStyles[index % colorStyles.length]
										.badgeBgClass
								}
							/>
						))}
					</div>
				)}

				{/* Create New Goal button */}
				<div className="mt-6 flex justify-center">
					<Button
						onClick={handleSavingsDrawer}
						className="bg-primary cursor-pointer text-white rounded-full px-8 py-3 shadow-md"
					>
						Create New {activeTab === "fiat" ? "Fiat" : "Crypto"}{" "}
						Goal
					</Button>
				</div>
			</>
		);
	}

	return (
		<div>
			<Savings />
			<div className="space-y-4 mt-8 ">
				<Link to={"/auto-save"}>
					<div className="bg-card cursor-pointer border border-border rounded-xl p-4 shadow flex flex-col gap-2">
						<span className="font-semibold text-foreground">
							Auto Save
						</span>
						<span className="text-muted-foreground text-sm">
							Save automatically without the stress of having to
							transfer into your savings.
						</span>
					</div>
				</Link>

				<Link to="/safe-lock">	<div className="bg-card border my-4 border-border rounded-xl p-4 shadow flex flex-col gap-2">
					<span className="font-semibold text-foreground">
						Safe Lock Savings
					</span>
					<span className="text-muted-foreground text-sm">
						Lock your savings and earn up to 10% interest at the end
						of your set lock period.
					</span>
				</div></Link>
			
				<div className="bg-card border border-border rounded-xl p-4 shadow flex flex-col gap-2">
					<span className="font-semibold text-foreground">
						Recent Activities
					</span>
					<span className="text-muted-foreground text-sm">
						Easily track your savings transactions and keep track of
						your money.
					</span>
				</div>
			</div>
		</div>
	);
};

export default SavingsView;
