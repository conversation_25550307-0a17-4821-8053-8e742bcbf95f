import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
// import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { IOption } from "@/types/general";
import { IFiatWallet } from "@/types/fiat-wallets";
// import { ICryptoWallet } from "@/types/wallet";
import EnhancedSelect from "@/components/enhanced-select";
import { Plus } from "lucide-react";
import { formatAmount } from "@/utils/format-amount";
import useCreateCardStore from "@/store/create-card-store";

interface SelectCurrencyProps {
	handleStepChange: () => void;

}

const SelectCurrency = ({ handleStepChange }: SelectCurrencyProps) => {
	const { setSelectedCoin } = useCreateCardStore();
	const [selectedFiat, setSelectedFiat] = useState<IFiatWallet | null>(null);
	// const [selectedCrypto,setSelectedCrypto] = useState<ICryptoWallet | null>(null);

	const { data: fiatWallets = [], isLoading: isLoadingFiatWallets } =
		useFiatUserWallets();
		// const {data:cryptoWallets = [],isLoading:isLoadingCryptoWallets} = useCryptoUserWallets()

	const fiatOptions: IOption<IFiatWallet>[] = fiatWallets.map((wallet) => ({
		id: `fiat-${wallet.currency}`,
		value: wallet.currency,
		label: wallet.currency,
		icon: wallet.image,
		raw: wallet,
	}));

	// const cryptoOptions:IOption<ICryptoWallet>[] = cryptoWallets.map((wallet)=>({
	// 	id:`crypto-${wallet.currency}`,
	// 	value:wallet.currency,
	// 	label:wallet.currency,
	// 	icon:wallet.image,
	// 	raw:wallet,
	// }));
	
	const selectedFiatOption =
		fiatOptions.find((option) => option.value === selectedFiat?.currency) ||
		null;

	const handleFiatChange = (option: IOption<IFiatWallet> | null) => {
		if (option && option.raw) {
			setSelectedFiat(option.raw);
			setSelectedCoin(option.raw);
			handleStepChange();
		} else {
			setSelectedFiat(null);
		}
	};


	const handleAddNew = () => {};
	
	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	return (
		<div className="flex flex-col h-full p-6">
			<div className="mb-6">
				<p className="text-muted-foreground text-center ">
					Select the currency for your virtual card. This currency
					will be used for all transactions.
				</p>
			</div>

			<div className="relative">
				<EnhancedSelect<IFiatWallet>
					header={walletHeader}
					options={fiatOptions}
					value={selectedFiatOption}
					onChange={handleFiatChange}
					placeholder="Select account"
					isLoading={isLoadingFiatWallets}
					className="w-full mb-3"
					displayClassName="p-4"
					renderSelected={(option) => (
						<div className="flex items-center gap-2">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<span>{option.value}</span>
						</div>
					)}
					renderOption={(option) => (
						<div className="flex items-center gap-2 w-full">
							<div className="w-6 h-6 flex items-center justify-center">
								{typeof option.icon === "string" ? (
									<img
										src={option.icon}
										alt={option.value}
										className="w-full h-full object-contain rounded-full"
									/>
								) : option.icon ? (
									option.icon
								) : (
									<div className="w-6 h-6 bg-gray-200 rounded-full" />
								)}
							</div>
							<div className="flex flex-col">
								<span>{option.label}</span>
								<span className="text-xs text-gray-500 group-hover:text-white">
									Balance:{" "}
									{formatAmount(
										option.raw?.available_balance,
										option.value,
									)}
								</span>
							</div>
						</div>
					)}
				/>
			</div>
		</div>
	);
};

export default SelectCurrency;
