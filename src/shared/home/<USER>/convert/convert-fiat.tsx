// import { useState, useEffect } from "react";
// import { z } from "zod";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Loader2, AlertCircle, Plus } from "lucide-react";
// import { IFiatWallet } from "@/types/fiat-wallets";
// import { IOption } from "@/types/general";
// import { formatAmount } from "@/utils/format-amount";
// import { parseFormattedNumber } from "@/utils/parse-formatted-number";
// import EnhancedSelect from "@/components/enhanced-select";
// import { Button } from "@/components/custom/button";
// import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
// import { useGetSwapPrice } from "@/hooks/api/swaps";
// import useSwapFiatTransactionStore from "@/store/convert-fiat-store";
// import { ArrowDataTransferVerticalIcon } from "../../crypto/swap";

// const swapSchema = z.object({
// 	amount: z.number().min(0.01, "Amount must be greater than 0"),
// 	to_amount: z.number().optional(),
// });

// type SwapFormValues = z.infer<typeof swapSchema>;

// const CONVERSION_TYPE = "INSTANT SWAP FIAT TO FIAT";

// interface SwapFiatProps {
// 	onProceed: () => void;
// }

// export function ConvertFiat({ onProceed }: SwapFiatProps) {
// 	const { setTransaction } = useSwapFiatTransactionStore();

// 	const { data: fiatWallets = [], isLoading: fiatLoading } =
// 		useFiatUserWallets();

// 	const form = useForm<SwapFormValues>({
// 		resolver: zodResolver(swapSchema),
// 		defaultValues: {
// 			amount: undefined,
// 			to_amount: undefined,
// 		},
// 		mode: "onChange",
// 	});

// 	const { watch, setValue, handleSubmit, formState, trigger } = form;
// 	const amount = watch("amount");
// 	const toAmount = watch("to_amount");

// 	form.register("amount", {
// 		validate: (value) => {
// 			if (value) {
// 				if (value > (fromCurrency?.available_balance || 0))
// 					return `Insufficient balance`;
// 			}
// 			return true;
// 		},
// 	});

// 	const [fromCurrency, setFromCurrency] = useState<IFiatWallet | null>(null);
// 	const [toCurrency, setToCurrency] = useState<IFiatWallet | null>(null);

// 	const [fromDisplay, setFromDisplay] = useState("");
// 	const [toDisplay, setToDisplay] = useState("");
// 	const [activeField, setActiveField] = useState<"from" | "to">();

// 	const { data: swapPrice, isLoading: rateLoading } = useGetSwapPrice(
// 		fromCurrency?.currency || "",
// 		toCurrency?.currency || "",
// 		CONVERSION_TYPE,
// 	);

// 	const currencyOptions: IOption<IFiatWallet>[] = fiatWallets.map(
// 		(wallet) => ({
// 			id: `fiat-${wallet.currency}`,
// 			value: wallet.currency,
// 			label: wallet.currency,
// 			icon: wallet.image,
// 			raw: wallet,
// 		}),
// 	);

// 	const selectedFromCurrencyOption =
// 		currencyOptions.find(
// 			(option) => option.value === fromCurrency?.currency,
// 		) || null;

// 	const selectedToCurrencyOption =
// 		currencyOptions.find(
// 			(option) => option.value === toCurrency?.currency,
// 		) || null;

// 	useEffect(() => {
// 		if (!swapPrice?.rate || !activeField) return;

// 		if (activeField === "from" && amount !== undefined) {
// 			const calculatedTo = amount * swapPrice.rate;
// 			setValue("to_amount", calculatedTo, { shouldValidate: true });
// 			setToDisplay(
// 				formatAmount(calculatedTo, toCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		} else if (activeField === "to" && toAmount !== undefined) {
// 			const calculatedFrom = toAmount / swapPrice.rate;
// 			setValue("amount", calculatedFrom, { shouldValidate: true });
// 			setFromDisplay(
// 				formatAmount(calculatedFrom, fromCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		}
// 	}, [
// 		amount,
// 		toAmount,
// 		swapPrice?.rate,
// 		activeField,
// 		setValue,
// 		toCurrency,
// 		fromCurrency,
// 	]);

// 	useEffect(() => {
// 		if (amount)
// 			setFromDisplay(
// 				formatAmount(amount, fromCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		if (toAmount)
// 			setToDisplay(
// 				formatAmount(toAmount, toCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 	}, []);

// 	useEffect(() => {
// 		if (swapPrice?.rate && activeField === "from" && amount) {
// 			const calculatedTo = amount * swapPrice.rate;
// 			setValue("to_amount", calculatedTo);
// 			setToDisplay(
// 				formatAmount(calculatedTo, toCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		}
// 	}, [swapPrice?.rate, activeField, amount, setValue, toCurrency]);

// 	const handleFromAmountChange = (text: string) => {
// 		if (!fromCurrency) return;

// 		setActiveField("from");
// 		const parsed = parseFormattedNumber(text);
// 		setFromDisplay(text);
// 		setValue("amount", parsed);
// 		trigger("amount");
// 	};

// 	const handleToAmountChange = (text: string) => {
// 		if (!toCurrency || !fromCurrency) return;

// 		setActiveField("to");
// 		const parsed = parseFormattedNumber(text);
// 		setToDisplay(text);
// 		setValue("to_amount", parsed);
// 	};

// 	const handleFromBlur = () => {
// 		if (!fromCurrency) return;

// 		setFromDisplay(
// 			formatAmount(amount, fromCurrency?.currency, { rawFormat: true }),
// 		);
// 	};

// 	const handleToBlur = () => {
// 		if (!toCurrency) return;

// 		setToDisplay(
// 			formatAmount(toAmount, toCurrency?.currency, { rawFormat: true }),
// 		);
// 	};

// 	const handleFromCurrencyChange = (option: IOption<IFiatWallet> | null) => {
// 		if (!option) return;

// 		if (option.raw) {
// 			setFromCurrency(option.raw);
// 		} else {
// 			const selectedWallet = fiatWallets.find(
// 				(wallet) => wallet.currency === option.value,
// 			);
// 			setFromCurrency(selectedWallet || null);
// 		}

// 		setValue("amount", 0);
// 		setValue("to_amount", 0);
// 		setFromDisplay("");
// 		setToDisplay("");
// 		setActiveField("from");
// 	};

// 	const handleToCurrencyChange = (option: IOption<IFiatWallet> | null) => {
// 		if (!option) return;

// 		if (option.raw) {
// 			setToCurrency(option.raw);
// 		} else {
// 			const selectedWallet = fiatWallets.find(
// 				(wallet) => wallet.currency === option.value,
// 			);
// 			setToCurrency(selectedWallet || null);
// 		}

// 		if (amount && fromCurrency) {
// 			setActiveField("from");
// 			trigger("amount");
// 		}
// 	};

// 	const handleReviewOrder = () => {
// 		if (fromCurrency && toCurrency && swapPrice) {
// 			const payload = {
// 				from: fromCurrency,
// 				to: toCurrency,
// 				amount: Number(amount),
// 				method: CONVERSION_TYPE,
// 				price_data: swapPrice,
// 			};

// 			setTransaction(payload);

// 			onProceed();
// 		}
// 	};

// 	const handleAddNew = () => {};

// 	const walletHeader = (
// 		<div className="py-2 flex justify-between items-center">
// 			<h3 className="text-2xl font-bold font-body">Wallets</h3>
// 			<Button
// 				onClick={handleAddNew}
// 				className="text-sm flex items-center rounded-full"
// 			>
// 				Add New
// 				<Plus className="h-3 w-3 ml-2" />
// 			</Button>
// 		</div>
// 	);

// 	const toCurrencyOptions = currencyOptions.filter(
// 		(option) => option.value !== fromCurrency?.currency,
// 	);

// 	const isLoading = fiatLoading || rateLoading;
// 	const isButtonDisabled =
// 		!fromCurrency ||
// 		!toCurrency ||
// 		!swapPrice ||
// 		!amount ||
// 		!formState.isValid;

// 	return (
// 		<div className="flex flex-col h-full">
// 			<div className="flex-1 p-6">
// 				{/* From Currency Selection */}
// 				<div className="flex flex-col items-center mb-8">
// 					<EnhancedSelect<IFiatWallet>
// 						header={walletHeader}
// 						options={currencyOptions}
// 						value={selectedFromCurrencyOption}
// 						onChange={handleFromCurrencyChange}
// 						placeholder="Choose Fiat Account"
// 						isLoading={fiatLoading}
// 						className="w-full mb-6"
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>From {option.value}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<div className="flex flex-col">
// 									<span>{option.label}</span>
// 									<span className="text-xs text-gray-500 group-hover:text-white">
// 										Balance:{" "}
// 										{formatAmount(
// 											option.raw?.available_balance,
// 											option.value,
// 										)}
// 									</span>
// 								</div>
// 							</div>
// 						)}
// 					/>

// 					{/* From Amount Input */}
// 					<div className="flex flex-col items-center w-full">
// 						<div className="flex items-end justify-center">
// 							<input
// 								type="text"
// 								inputMode="decimal"
// 								value={fromDisplay}
// 								onChange={(e) =>
// 									handleFromAmountChange(e.target.value)
// 								}
// 								onBlur={handleFromBlur}
// 								placeholder="0.00"
// 								disabled={!fromCurrency}
// 								className={`text-6xl text-center bg-transparent outline-none ${
// 									formState.errors?.amount
// 										? "text-red-500"
// 										: ""
// 								} ${
// 									!fromCurrency
// 										? "opacity-50 cursor-not-allowed"
// 										: ""
// 								}`}
// 							/>
// 							{/* <span className="text-2xl ml-2 mb-1">
// 								{fromCurrency?.currency || ""}
// 							</span> */}
// 						</div>

// 						<div className="text-center mt-2 w-full">
// 							{fiatLoading ? (
// 								<div className="flex items-center justify-center gap-2">
// 									<Loader2 className="h-4 w-4 animate-spin" />
// 									<span>Loading balance...</span>
// 								</div>
// 							) : fromCurrency ? (
// 								<p className="text-sm text-gray-600">
// 									Available {fromCurrency.currency} balance:{" "}
// 									{formatAmount(
// 										fromCurrency.available_balance,
// 										fromCurrency.currency,
// 									)}
// 								</p>
// 							) : (
// 								<div className="flex items-center justify-center gap-1 text-amber-500">
// 									<AlertCircle className="h-4 w-4" />
// 									<p className="text-sm">
// 										Please select a currency first
// 									</p>
// 								</div>
// 							)}

// 							{formState.errors?.amount?.message ? (
// 								<p className="text-sm text-red-500 font-medium mt-1">
// 									{formState.errors.amount.message}
// 								</p>
// 							) : (
// 								swapPrice?.rate &&
// 								fromCurrency &&
// 								toCurrency && (
// 									<p className="text-sm text-gray-600 mt-1">
// 										1 {fromCurrency.currency} ≈{" "}
// 										{formatAmount(
// 											swapPrice.rate,
// 											toCurrency.currency,
// 											{ overrideMinDecimalPlaces: 4 },
// 										)}{" "}
// 										{toCurrency.currency}
// 									</p>
// 								)
// 							)}
// 						</div>
// 					</div>
// 				</div>

// 				{/* Conversion Arrow */}
// 				<div className="flex justify-center my-6">
// 					<div
// 						className={`bg-amber-100 rounded-full p-2 ${
// 							!fromCurrency || !toCurrency ? "opacity-50" : ""
// 						}`}
// 					>
// 						<ArrowDataTransferVerticalIcon
// 							className={`h-6 w-6 ${
// 								fromCurrency && toCurrency
// 									? "text-primary"
// 									: "text-amber-300"
// 							}`}
// 						/>
// 					</div>
// 				</div>

// 				{/* To Currency Selection */}
// 				<div className="flex flex-col items-center">
// 					{/* To Amount Input */}
// 					<div className="flex flex-col items-center w-full mb-6">
// 						<div className="flex items-end justify-center">
// 							<input
// 								type="text"
// 								inputMode="decimal"
// 								value={toDisplay}
// 								onChange={(e) =>
// 									handleToAmountChange(e.target.value)
// 								}
// 								onBlur={handleToBlur}
// 								placeholder="0.00"
// 								disabled={!toCurrency || !fromCurrency}
// 								className={`text-6xl text-center bg-transparent outline-none ${
// 									!toCurrency || !fromCurrency
// 										? "opacity-50 cursor-not-allowed"
// 										: ""
// 								}`}
// 							/>
// 							{/* <span className="text-2xl ml-2 mb-1">
// 								{toCurrency?.currency || ""}
// 							</span> */}
// 						</div>

// 						{!toCurrency ? (
// 							<div className="flex items-center justify-center gap-1 text-amber-500 mt-1">
// 								<AlertCircle className="h-4 w-4" />
// 								<p className="text-sm">
// 									Please select a destination currency
// 								</p>
// 							</div>
// 						) : (
// 							swapPrice?.rate &&
// 							fromCurrency &&
// 							toCurrency && (
// 								<p className="text-sm text-gray-600 mt-1">
// 									1 {toCurrency.currency} ≈{" "}
// 									{formatAmount(
// 										1 / swapPrice.rate,
// 										fromCurrency.currency,
// 										{ overrideMinDecimalPlaces: 4 },
// 									)}{" "}
// 									{fromCurrency.currency}
// 								</p>
// 							)
// 						)}
// 					</div>

// 					<EnhancedSelect<IFiatWallet>
// 						options={toCurrencyOptions}
// 						value={selectedToCurrencyOption}
// 						onChange={handleToCurrencyChange}
// 						placeholder="Select destination currency"
// 						isLoading={fiatLoading}
// 						className="w-full mb-6"
// 						isSearchable={false}
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>To {option.value}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>{option.label}</span>
// 							</div>
// 						)}
// 					/>
// 				</div>
// 			</div>

// 			{/* Review Button */}
// 			<div className="p-6 border-t">
// 				<Button
// 					className="w-full h-14 rounded-full text-lg font-medium"
// 					disabled={isButtonDisabled || isLoading}
// 					onClick={handleSubmit(handleReviewOrder)}
// 				>
// 					{isLoading ? (
// 						<div className="flex items-center gap-2">
// 							<Loader2 className="h-5 w-5 animate-spin" />
// 							<span>Loading...</span>
// 						</div>
// 					) : !fromCurrency ? (
// 						"Select a source currency"
// 					) : !toCurrency ? (
// 						"Select a destination currency"
// 					) : (
// 						`Review ${toCurrency.currency} Order`
// 					)}
// 				</Button>
// 			</div>
// 		</div>
// 	);
// }


import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, AlertCircle, Plus } from "lucide-react";
import { IFiatWallet } from "@/types/fiat-wallets";
import { IOption } from "@/types/general";
import { formatAmount } from "@/utils/format-amount";
import { parseFormattedNumber } from "@/utils/parse-formatted-number";
import EnhancedSelect from "@/components/enhanced-select";
import { Button } from "@/components/custom/button";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useGetSwapPrice } from "@/hooks/api/swaps";
import useSwapFiatTransactionStore from "@/store/convert-fiat-store";
import useFiatSelectionStore from "@/store/fiat-selection-store";
import { ArrowDataTransferVerticalIcon } from "../../crypto/swap";

const swapSchema = z.object({
	amount: z.number().min(0.01, "Amount must be greater than 0"),
	to_amount: z.number().optional(),
});

type SwapFormValues = z.infer<typeof swapSchema>;

const CONVERSION_TYPE = "INSTANT SWAP FIAT TO FIAT";

interface SwapFiatProps {
	onProceed: () => void;
}

export function ConvertFiat({ onProceed }: SwapFiatProps) {
	const { setTransaction } = useSwapFiatTransactionStore();
	const { selectedFiat } = useFiatSelectionStore(); // Get selected fiat from store

	const { data: fiatWallets = [], isLoading: fiatLoading } =
		useFiatUserWallets();

	const form = useForm<SwapFormValues>({
		resolver: zodResolver(swapSchema),
		defaultValues: {
			amount: undefined,
			to_amount: undefined,
		},
		mode: "onChange",
	});

	const { watch, setValue, handleSubmit, formState, trigger } = form;
	const amount = watch("amount");
	const toAmount = watch("to_amount");

	form.register("amount", {
		validate: (value) => {
			if (value) {
				if (value > (fromCurrency?.available_balance || 0))
					return `Insufficient balance`;
			}
			return true;
		},
	});

	const [fromCurrency, setFromCurrency] = useState<IFiatWallet | null>(null);
	const [toCurrency, setToCurrency] = useState<IFiatWallet | null>(null);

	const [fromDisplay, setFromDisplay] = useState("");
	const [toDisplay, setToDisplay] = useState("");
	const [activeField, setActiveField] = useState<"from" | "to">();

	// Auto-select the fiat from the store when component mounts or selectedFiat changes
	useEffect(() => {
		if (selectedFiat) {
			setFromCurrency(selectedFiat);
			// Reset form values when from currency changes
			setValue("amount", 0);
			setValue("to_amount", 0);
			setFromDisplay("");
			setToDisplay("");
			setActiveField("from");
		}
	}, [selectedFiat, setValue]);

	const { data: swapPrice, isLoading: rateLoading } = useGetSwapPrice(
		fromCurrency?.currency || "",
		toCurrency?.currency || "",
		CONVERSION_TYPE,
	);

	const currencyOptions: IOption<IFiatWallet>[] = fiatWallets.map(
		(wallet) => ({
			id: `fiat-${wallet.currency}`,
			value: wallet.currency,
			label: wallet.currency,
			icon: wallet.image,
			raw: wallet,
		}),
	);

	const selectedFromCurrencyOption =
		currencyOptions.find(
			(option) => option.value === fromCurrency?.currency,
		) || null;

	const selectedToCurrencyOption =
		currencyOptions.find(
			(option) => option.value === toCurrency?.currency,
		) || null;

	useEffect(() => {
		if (!swapPrice?.rate || !activeField) return;

		if (activeField === "from" && amount !== undefined) {
			const calculatedTo = amount * swapPrice.rate;
			setValue("to_amount", calculatedTo, { shouldValidate: true });
			setToDisplay(
				formatAmount(calculatedTo, toCurrency?.currency, {
					rawFormat: true,
				}),
			);
		} else if (activeField === "to" && toAmount !== undefined) {
			const calculatedFrom = toAmount / swapPrice.rate;
			setValue("amount", calculatedFrom, { shouldValidate: true });
			setFromDisplay(
				formatAmount(calculatedFrom, fromCurrency?.currency, {
					rawFormat: true,
				}),
			);
		}
	}, [
		amount,
		toAmount,
		swapPrice?.rate,
		activeField,
		setValue,
		toCurrency,
		fromCurrency,
	]);

	useEffect(() => {
		if (amount)
			setFromDisplay(
				formatAmount(amount, fromCurrency?.currency, {
					rawFormat: true,
				}),
			);
		if (toAmount)
			setToDisplay(
				formatAmount(toAmount, toCurrency?.currency, {
					rawFormat: true,
				}),
			);
	}, []);

	useEffect(() => {
		if (swapPrice?.rate && activeField === "from" && amount) {
			const calculatedTo = amount * swapPrice.rate;
			setValue("to_amount", calculatedTo);
			setToDisplay(
				formatAmount(calculatedTo, toCurrency?.currency, {
					rawFormat: true,
				}),
			);
		}
	}, [swapPrice?.rate, activeField, amount, setValue, toCurrency]);

	const handleFromAmountChange = (text: string) => {
		if (!fromCurrency) return;

		setActiveField("from");
		const parsed = parseFormattedNumber(text);
		setFromDisplay(text);
		setValue("amount", parsed);
		trigger("amount");
	};

	const handleToAmountChange = (text: string) => {
		if (!toCurrency || !fromCurrency) return;

		setActiveField("to");
		const parsed = parseFormattedNumber(text);
		setToDisplay(text);
		setValue("to_amount", parsed);
	};

	const handleFromBlur = () => {
		if (!fromCurrency) return;

		setFromDisplay(
			formatAmount(amount, fromCurrency?.currency, { rawFormat: true }),
		);
	};

	const handleToBlur = () => {
		if (!toCurrency) return;

		setToDisplay(
			formatAmount(toAmount, toCurrency?.currency, { rawFormat: true }),
		);
	};

	const handleFromCurrencyChange = (option: IOption<IFiatWallet> | null) => {
		if (!option) return;

		if (option.raw) {
			setFromCurrency(option.raw);
		} else {
			const selectedWallet = fiatWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setFromCurrency(selectedWallet || null);
		}

		setValue("amount", 0);
		setValue("to_amount", 0);
		setFromDisplay("");
		setToDisplay("");
		setActiveField("from");
	};

	const handleToCurrencyChange = (option: IOption<IFiatWallet> | null) => {
		if (!option) return;

		if (option.raw) {
			setToCurrency(option.raw);
		} else {
			const selectedWallet = fiatWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setToCurrency(selectedWallet || null);
		}

		if (amount && fromCurrency) {
			setActiveField("from");
			trigger("amount");
		}
	};

	const handleReviewOrder = () => {
		if (fromCurrency && toCurrency && swapPrice) {
			const payload = {
				from: fromCurrency,
				to: toCurrency,
				amount: Number(amount),
				method: CONVERSION_TYPE,
				price_data: swapPrice,
			};

			setTransaction(payload);

			onProceed();
		}
	};

	const handleAddNew = () => {};

	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	const toCurrencyOptions = currencyOptions.filter(
		(option) => option.value !== fromCurrency?.currency,
	);

	const isLoading = fiatLoading || rateLoading;
	const isButtonDisabled =
		!fromCurrency ||
		!toCurrency ||
		!swapPrice ||
		!amount ||
		!formState.isValid;

	return (
		<div className="flex flex-col h-full">
			<div className="flex-1 p-6">
				{/* From Currency Selection */}
				<div className="flex flex-col items-center mb-8">
					<EnhancedSelect<IFiatWallet>
						header={walletHeader}
						options={currencyOptions}
						value={selectedFromCurrencyOption}
						onChange={handleFromCurrencyChange}
						placeholder="Choose Fiat Account"
						isLoading={fiatLoading}
						className="w-full mb-6"
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>From {option.value}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<div className="flex flex-col">
									<span>{option.label}</span>
									<span className="text-xs text-gray-500 group-hover:text-white">
										Balance:{" "}
										{formatAmount(
											option.raw?.available_balance,
											option.value,
										)}
									</span>
								</div>
							</div>
						)}
					/>

					{/* From Amount Input */}
					<div className="flex flex-col items-center w-full">
						<div className="flex items-end justify-center">
							<input
								type="text"
								inputMode="decimal"
								value={fromDisplay}
								onChange={(e) =>
									handleFromAmountChange(e.target.value)
								}
								onBlur={handleFromBlur}
								placeholder="0.00"
								disabled={!fromCurrency}
								className={`text-6xl text-center bg-transparent outline-none ${
									formState.errors?.amount
										? "text-red-500"
										: ""
								} ${
									!fromCurrency
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
							{/* <span className="text-2xl ml-2 mb-1">
								{fromCurrency?.currency || ""}
							</span> */}
						</div>

						<div className="text-center mt-2 w-full">
							{fiatLoading ? (
								<div className="flex items-center justify-center gap-2">
									<Loader2 className="h-4 w-4 animate-spin" />
									<span>Loading balance...</span>
								</div>
							) : fromCurrency ? (
								<p className="text-sm text-gray-600">
									Available {fromCurrency.currency} balance:{" "}
									{formatAmount(
										fromCurrency.available_balance,
										fromCurrency.currency,
									)}
								</p>
							) : (
								<div className="flex items-center justify-center gap-1 text-amber-500">
									<AlertCircle className="h-4 w-4" />
									<p className="text-sm">
										Please select a currency first
									</p>
								</div>
							)}

							{formState.errors?.amount?.message ? (
								<p className="text-sm text-red-500 font-medium mt-1">
									{formState.errors.amount.message}
								</p>
							) : (
								swapPrice?.rate &&
								fromCurrency &&
								toCurrency && (
									<p className="text-sm text-gray-600 mt-1">
										1 {fromCurrency.currency} ≈{" "}
										{formatAmount(
											swapPrice.rate,
											toCurrency.currency,
											{ overrideMinDecimalPlaces: 4 },
										)}{" "}
										{toCurrency.currency}
									</p>
								)
							)}
						</div>
					</div>
				</div>

				{/* Conversion Arrow */}
				<div className="flex justify-center my-6">
					<div
						className={`bg-amber-100 rounded-full p-2 ${
							!fromCurrency || !toCurrency ? "opacity-50" : ""
						}`}
					>
						<ArrowDataTransferVerticalIcon
							className={`h-6 w-6 ${
								fromCurrency && toCurrency
									? "text-primary"
									: "text-amber-300"
							}`}
						/>
					</div>
				</div>

				{/* To Currency Selection */}
				<div className="flex flex-col items-center">
					{/* To Amount Input */}
					<div className="flex flex-col items-center w-full mb-6">
						<div className="flex items-end justify-center">
							<input
								type="text"
								inputMode="decimal"
								value={toDisplay}
								onChange={(e) =>
									handleToAmountChange(e.target.value)
								}
								onBlur={handleToBlur}
								placeholder="0.00"
								disabled={!toCurrency || !fromCurrency}
								className={`text-6xl text-center bg-transparent outline-none ${
									!toCurrency || !fromCurrency
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
							{/* <span className="text-2xl ml-2 mb-1">
								{toCurrency?.currency || ""}
							</span> */}
						</div>

						{!toCurrency ? (
							<div className="flex items-center justify-center gap-1 text-amber-500 mt-1">
								<AlertCircle className="h-4 w-4" />
								<p className="text-sm">
									Please select a destination currency
								</p>
							</div>
						) : (
							swapPrice?.rate &&
							fromCurrency &&
							toCurrency && (
								<p className="text-sm text-gray-600 mt-1">
									1 {toCurrency.currency} ≈{" "}
									{formatAmount(
										1 / swapPrice.rate,
										fromCurrency.currency,
										{ overrideMinDecimalPlaces: 4 },
									)}{" "}
									{fromCurrency.currency}
								</p>
							)
						)}
					</div>

					<EnhancedSelect<IFiatWallet>
						options={toCurrencyOptions}
						value={selectedToCurrencyOption}
						onChange={handleToCurrencyChange}
						placeholder="Select destination currency"
						isLoading={fiatLoading}
						className="w-full mb-6"
						isSearchable={false}
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>To {option.value}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>{option.label}</span>
							</div>
						)}
					/>
				</div>
			</div>

			{/* Review Button */}
			<div className="p-6 border-t">
				<Button
					className="w-full h-14 rounded-full text-lg font-medium"
					disabled={isButtonDisabled || isLoading}
					onClick={handleSubmit(handleReviewOrder)}
				>
					{isLoading ? (
						<div className="flex items-center gap-2">
							<Loader2 className="h-5 w-5 animate-spin" />
							<span>Loading...</span>
						</div>
					) : !fromCurrency ? (
						"Select a source currency"
					) : !toCurrency ? (
						"Select a destination currency"
					) : (
						`Review ${toCurrency.currency} Order`
					)}
				</Button>
			</div>
		</div>
	);
}