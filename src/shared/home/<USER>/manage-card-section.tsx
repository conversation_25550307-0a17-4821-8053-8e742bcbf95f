import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useDrawer } from "@/components/drawer-view/use-drawer";
import { Card } from "./card-item";
import { useMediaQuery } from "@/hooks/use-media-query";

import { ChangeCardPin } from "./manage-card/change-card-pin";
import { WithdrawCard } from "./manage-card/withdraw-card";
import { BlockCard } from "./manage-card/block-card";
import { TerminateCard } from "./manage-card/terminate-card";
import { FundCard } from "./manage-card/fund-card";
import { ManageCardSettings } from "./manage-card/manage-card-settings";
import { ActivateFaceId } from "./manage-card/activate-face-id";
import { ICard } from "@/types/cards";

interface ManageCardSectionProps {
	onBack: () => void;
	card:ICard

}

const menuItems = [
	{ label: "Change Card Pin", action: "change-pin" },
	{ label: "Withdraw from Card", action: "withdraw" },
	{ label: "Block/Unblock Card", action: "block" },
	{ label: "terminate card", action: "terminate" },
	{ label: "Manage Card", action: "manage" },
	{ label: "Fund Card", action: "funding" },
	{ label: "Activate Face ID", action: "faceid" },
];

export const ManageCardSection = ({ onBack,card }: ManageCardSectionProps) => {
	const { openDrawer, closeDrawer } = useDrawer();
	const isMobile = useMediaQuery("(max-width: 768px)");

	const handleMenuClick = (action: string) => {
		let ComponentToRender;
		switch (action) {
			case "change-pin":
				ComponentToRender = ChangeCardPin;
				break;
			case "withdraw":
				ComponentToRender = WithdrawCard;
				break;
			case "block":
				ComponentToRender = BlockCard;
				break;
			case "terminate":
				ComponentToRender = TerminateCard;
				break;
			case "manage":
				ComponentToRender = ManageCardSettings;
				break;
			case "funding":
				ComponentToRender = FundCard;
				break;
		
			case "faceid":
				ComponentToRender = ActivateFaceId;
				break;
			default:
				ComponentToRender = () => <div className="p-8">No content</div>;
		}

		openDrawer({
			view: <ComponentToRender onClose={closeDrawer} card={card}/>,
			placement: isMobile ? "bottom" : "right",
			customSize: isMobile ? undefined : "480px",
		});
	};

	return (
		<div className="container mx-auto p-6">
			<div className="flex items-center gap-3 mb-8">
				<button
					onClick={onBack}
					className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
				>
					<ChevronLeft className="w-5 h-5 text-primary" />
				</button>
				<h3 className="text-2xl font-bold text-foreground">
					Manage Card
				</h3>
			</div>
			<div className="flex flex-col md:flex-row gap-8 items-start">
				<div className="w-full max-w-sm xl:max-w-none xl:w-88 mx-auto xl:mx-0 relative">
					<Card className="w-full h-48" />
					<div className="text-lg font-semibold text-foreground mb-2 mt-4">
						Nigerian Naira Card
					</div>
				</div>
				{/* Menu Actions */}
				<div className="w-full md:w-1/2 flex flex-col gap-3">
					{menuItems.map((item) => (
						<button
							key={item.action}
							onClick={() => handleMenuClick(item.action)}
							className="flex justify-between items-center w-full bg-white border border-border rounded-full px-6 py-4 text-left text-foreground hover:bg-gray-900/10 cursor-pointer transition group dark:bg-gray-900 dark:hover:bg-gray-700 dark:text-white dark:border-gray-700"
						>
							<span className="text-base font-medium">
								{item.label}
							</span>
							<ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-primary" />
						</button>
					))}
				</div>
			</div>
		</div>
	);
};
