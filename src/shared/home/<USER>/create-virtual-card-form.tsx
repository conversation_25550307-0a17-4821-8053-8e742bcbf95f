import FloatingLabelInput from "@/components/custom/input/floating-label-input";
import { cn } from "@/lib/utils";
import useCreateCardStore from "@/store/create-card-store";
import { Loader } from "lucide-react";
import { z } from "zod";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCreateCard } from "@/hooks/api/cards";
import { Button } from "@/components/custom/button";
import EnhancedSelect from "@/components/enhanced-select";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";

interface CreateVirtualCardFormProps {
	handleStepChange: () => void;

}

const createCardSchema = z.object({
	streetName: z.string().min(1, "Street name is required"),
	cityName: z.string().min(1, "City name is required"),
	stateName: z.string().min(1, "State name is required"),
	postalCode: z.string().min(1, "Postal code is required"),
	phoneNumber: z
		.string()
		.min(1, "Phone number is required")
		.regex(/^\d{10,15}$/, "Please enter a valid phone number"),
	currency: z.string().min(1, "Currency selection is required"),
});

type CreateCardSchema = z.infer<typeof createCardSchema>;

const CreateVirtualCardForm = ({
	handleStepChange,
}: CreateVirtualCardFormProps) => {
	const { cardData, setCardDetails } = useCreateCardStore();
	const { mutate: createVirtualCard, isPending: isSubmitting } =
		useCreateCard();
		const userID = localStorage.getItem("user_id");
	const { data: fiatWallets = [] } = useFiatUserWallets();

	const {
		register,
		handleSubmit,
		control,
		formState: { errors },
	} = useForm<CreateCardSchema>({
		resolver: zodResolver(createCardSchema),
		defaultValues: {
			streetName: "",
			cityName: "",
			stateName: "",
			postalCode: "",
			phoneNumber: "",
			currency: cardData?.currency || "",
		},
	});

	const currencyOptions = fiatWallets.map((wallet) => ({
		id: wallet.currency,
		label: wallet.currency,
		value: wallet.currency,
		icon: wallet.image || undefined,
	}));

	const onSubmitForm = (data: CreateCardSchema) => {
		setCardDetails({
			currency: data.currency,
			name: cardData?.name || "",
			limit: cardData?.limit || "",
		});

		const payload = {
			user_id: userID,
			phone_number: data.phoneNumber,
			street: data.streetName,
			city: data.cityName,
			state: data.stateName,
			postal_code: data.postalCode,
		};

		createVirtualCard(payload, {
			onSuccess: () => {
				notify("Card created successfully!", ResponseStatus.SUCCESS);
				handleStepChange();
			},
			onError: (error) => {
				notify(
					error?.message || "Failed to create card",
					ResponseStatus.ERROR,
				);
			},
		});
	};

	const cardCreationFee = "0.5";
	const currency = cardData?.currency || "USD";

	return (
		<div className="flex flex-col h-full p-6">
			<p className="text-center mb-6 text-slate-600 dark:text-slate-400">
				Please enter your billing details and select a currency.
			</p>

			<form
				onSubmit={handleSubmit(onSubmitForm)}
				className="space-y-4 flex-1"
			>
				{/* Currency Selection */}
				<div>
					<Controller
						name="currency"
						control={control}
						render={({ field }) => (
							<div className="relative">
								<EnhancedSelect
									{...field}
									value={
										currencyOptions.find(
											(option) =>
												option.value === field.value,
										) || null
									}
									onChange={(selectedOption) =>
										field.onChange(
											selectedOption?.value || "",
										)
									}
									options={currencyOptions}
									placeholder="Select Currency"
									className={cn(
										" border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
										errors.currency &&
											"border-red-500 focus:ring-red-200 focus:border-red-500",
									)}
									containerClassName="w-full"
									displayClassName="h-14 px-4 py-3 text-base bg-white dark:bg-gray-900"
								/>
							</div>
						)}
					/>
					{errors.currency && (
						<p className="text-red-500 text-xs mt-1">
							{errors.currency.message}
						</p>
					)}
				</div>
				<div>
					<FloatingLabelInput
						label="Enter your street name"
						{...register("streetName")}
						className={cn(
							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
							errors.streetName &&
								"border-red-500 focus:ring-red-200 focus:border-red-500",
						)}
					/>
					{errors.streetName && (
						<p className="text-red-500 text-xs mt-1">
							{errors.streetName.message}
						</p>
					)}
				</div>

				<div>
					<FloatingLabelInput
						label="Enter your city name"
						{...register("cityName")}
						className={cn(
							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
							errors.cityName &&
								"border-red-500 focus:ring-red-200 focus:border-red-500",
						)}
					/>
					{errors.cityName && (
						<p className="text-red-500 text-xs mt-1">
							{errors.cityName.message}
						</p>
					)}
				</div>

				<div>
					<FloatingLabelInput
						label="Enter your state name"
						{...register("stateName")}
						className={cn(
							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
							errors.stateName &&
								"border-red-500 focus:ring-red-200 focus:border-red-500",
						)}
					/>
					{errors.stateName && (
						<p className="text-red-500 text-xs mt-1">
							{errors.stateName.message}
						</p>
					)}
				</div>

				<div>
					<FloatingLabelInput
						label="Enter your postal code"
						{...register("postalCode")}
						className={cn(
							"border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
							errors.postalCode &&
								"border-red-500 focus:ring-red-200 focus:border-red-500",
						)}
					/>
					{errors.postalCode && (
						<p className="text-red-500 text-xs mt-1">
							{errors.postalCode.message}
						</p>
					)}
				</div>

				<div>
					<Controller
						name="phoneNumber"
						control={control}
						render={({ field: { onChange, value, ...field } }) => (
							<FloatingLabelInput
								label="Enter Phone Number"
								type="tel"
								{...field}
								value={value}
								onChange={(e) => {
									// Strip non-digits and limit to 15 characters
									const numericValue = e.target.value
										.replace(/[^\d]/g, "")
										.slice(0, 15);
									onChange(numericValue);
								}}
								className={cn(
									"border border-gray-300 text-foreground focus:outline-none focus:ring-2 focus:ring-orange-200 focus:border-orange-400",
									errors.phoneNumber &&
										"border-red-500 focus:ring-red-200 focus:border-red-500",
								)}
							/>
						)}
					/>
					{errors.phoneNumber && (
						<p className="text-red-500 text-xs mt-1">
							{errors.phoneNumber.message}
						</p>
					)}
				</div>

				<div className="text-center mt-6 mb-4">
					<p className="text-foreground">
						You need {currency} {cardCreationFee} to create this
						card.
						<br />
						Please fund your account.
					</p>
				</div>

				<div className="mt-auto pt-4">
					<Button
						type="submit"
						disabled={isSubmitting}
						className="w-full py-4 mb-2 rounded-full hover:bg-primary/90 cursor-pointer text-white flex items-center justify-center gap-2"
					>
						{isSubmitting ? (
							<>
								<Loader className="w-5 h-5 animate-spin" />
								Processing...
							</>
						) : (
							"Create Card"
						)}
					</Button>
				</div>
			</form>
		</div>
	);
};

export default CreateVirtualCardForm;
