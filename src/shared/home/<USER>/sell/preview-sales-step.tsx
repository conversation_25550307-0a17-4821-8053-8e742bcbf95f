import { format } from "date-fns";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { formatAmount } from "@/utils/format-amount";
import useSellCryptoTransactionStore from "@/store/sell-crypto-store";

interface PreviewSellCryptoProps {
	onSellCrypto: () => void;
}

export function PreviewSellCrypto({ onSellCrypto }: PreviewSellCryptoProps) {
	const { transaction } = useSellCryptoTransactionStore();

	
	if (!transaction) {
		return (
			<div className="flex flex-col items-center justify-center h-full p-6">
				<p className="text-gray-500">No transaction data available</p>
			</div>
		);
	}

	return (
		<div className="flex flex-col p-6 space-y-6">
			{/* Amount Display */}
			<motion.div
				className="text-center my-4"
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ delay: 0.1 }}
			>
				<h2 className="text-xl font-bold">
					You are about to sell {transaction.from.currency}{" "}
					{transaction.amount.toString()} for{" "}
					{transaction.to.currency}
				</h2>
			</motion.div>

			{/* Transaction Details */}
			<motion.div
				className="space-y-4"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
			>
				<div className="flex justify-between">
					<span className="text-gray-500">Coin</span>
					<span className="font-medium">
						{transaction.from.currency}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Rate</span>
					<span className="font-medium">
						{`1 ${transaction.from.currency} ≈ ${formatAmount(
							transaction.price_data.rate,
							transaction.to.currency,
							{ overrideMinDecimalPlaces: 4 },
						)} ${transaction.to.currency}`}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Amount</span>
					<span className="font-medium">
						{`${formatAmount(
							Number(transaction.amount),
							transaction.from.currency,
							{ overrideMinDecimalPlaces: 4 },
						)} ${transaction.from.currency}`}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">You Receive</span>
					<span className="font-medium">
						{`${formatAmount(
							Number(transaction.amount) *
								transaction.price_data.rate,
							transaction.to.currency,
							{ overrideMinDecimalPlaces: 4 },
						)} ${transaction.to.currency}`}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Fee</span>
					<span className="font-medium">
						{`${formatAmount(0.********, transaction.to.currency, {
							overrideMinDecimalPlaces: 8,
						})} ${transaction.to.currency}`}
					</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Payment</span>
					<span className="font-medium">Fiat Account</span>
				</div>
				<div className="flex justify-between">
					<span className="text-gray-500">Date</span>
					<span className="font-medium">
						{format(new Date(), "dd-MM-yyyy | HH:mm:ss")}
					</span>
				</div>
			</motion.div>

			{/* Amount to be received */}
			<motion.div
				className="bg-gray-100 p-4 rounded-full text-center"
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.4 }}
			>
				<span className="text-gray-600">
					You will receive{" "}
					{formatAmount(
						Number(transaction.amount) *
							transaction.price_data.rate,
						transaction.to.currency,
						{ overrideMinDecimalPlaces: 4 },
					)}{" "}
					{transaction.to.currency}
				</span>
			</motion.div>

			{/* Sell Button */}
			<motion.div
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.5 }}
			>
				<Button
					className="bg-primary hover:bg-primary/80 text-white rounded-full py-6 mt-6 w-full"
					onClick={onSellCrypto}
				>
					Sell Crypto
				</Button>
			</motion.div>
		</div>
	);
}
