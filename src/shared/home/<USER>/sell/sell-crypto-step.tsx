// import { useState, useEffect } from "react";
// import { z } from "zod";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Loader2, AlertCircle, Plus } from "lucide-react";
// import { IFiatWallet } from "@/types/fiat-wallets";
// import { ICryptoWallet } from "@/types/crypto-wallets";
// import { IOption } from "@/types/general";
// import { formatAmount } from "@/utils/format-amount";
// import { parseFormattedNumber } from "@/utils/parse-formatted-number";
// import EnhancedSelect from "@/components/enhanced-select";
// import { Button } from "@/components/custom/button";
// import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
// import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
// import { useGetSwapPrice } from "@/hooks/api/swaps";
// import useSellCryptoTransactionStore from "@/store/sell-crypto-store";
// import { ArrowDataTransferVerticalIcon } from "../swap";

// const sellSchema = z.object({
// 	amount: z.number().min(0, "Amount must be greater than 0"),

// 	to_amount: z.number().optional(),
// });

// type SellFormValues = z.infer<typeof sellSchema>;

// const CONVERSION_TYPE = "INSTANT SWAP CRYPTO TO FIAT";

// interface SellCryptoProps {
// 	onProceed: () => void;
// }

// export function SellCrypto({ onProceed }: SellCryptoProps) {
// 	const { setTransaction } = useSellCryptoTransactionStore();

// 	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
// 		useCryptoUserWallets();
// 	const { data: fiatWallets = [], isLoading: fiatLoading } =
// 		useFiatUserWallets();

// 	const form = useForm<SellFormValues>({
// 		resolver: zodResolver(sellSchema),
// 		defaultValues: {
// 			amount: undefined,
// 			to_amount: undefined,
// 		},
// 		mode: "onChange",
// 	});

// 	const { watch, setValue, handleSubmit, formState, trigger } = form;
// 	const amount = watch("amount");
// 	const toAmount = watch("to_amount");

// 	form.register("amount", {
// 		validate: (value) => {
// 			if (value) {
// 				if (value > (fromCoin?.balance || 0))
// 					return `Insufficient balance`;
// 			}
// 			return true;
// 		},
// 	});

// 	const [fromCoin, setFromCoin] = useState<ICryptoWallet | null>(null);
// 	const [toCurrency, setToCurrency] = useState<IFiatWallet | null>(null);

// 	const [fromDisplay, setFromDisplay] = useState("");
// 	const [toDisplay, setToDisplay] = useState("");
// 	const [activeField, setActiveField] = useState<"from" | "to">();

// 	const { data: swapPrice, isLoading: rateLoading } = useGetSwapPrice(
// 		fromCoin?.currency || "",
// 		toCurrency?.currency || "",
// 		CONVERSION_TYPE,
// 	);

// 	const cryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
// 		(wallet) => ({
// 			id: `crypto-${wallet.currency}`,
// 			value: wallet.currency,
// 			label: wallet.currency,
// 			icon: wallet.image,
// 			raw: wallet,
// 		}),
// 	);

// 	const fiatOptions: IOption<IFiatWallet>[] = fiatWallets.map((wallet) => ({
// 		id: `fiat-${wallet.currency}`,
// 		value: wallet.currency,
// 		label: wallet.currency,
// 		icon: wallet.image,
// 		raw: wallet,
// 	}));

// 	const selectedCryptoOption =
// 		cryptoOptions.find((option) => option.value === fromCoin?.currency) ||
// 		null;

// 	const selectedFiatOption =
// 		fiatOptions.find((option) => option.value === toCurrency?.currency) ||
// 		null;

// 	useEffect(() => {
// 		if (!swapPrice?.rate || !activeField) return;

// 		if (activeField === "from" && amount !== undefined) {
// 			const calculatedTo = amount * swapPrice.rate;
// 			setValue("to_amount", calculatedTo, { shouldValidate: true });
// 			setToDisplay(
// 				formatAmount(calculatedTo, toCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		} else if (activeField === "to" && toAmount !== undefined) {
// 			const calculatedFrom = toAmount / swapPrice.rate;
// 			setValue("amount", calculatedFrom, { shouldValidate: true });
// 			setFromDisplay(
// 				formatAmount(calculatedFrom, fromCoin?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		}
// 	}, [
// 		amount,
// 		toAmount,
// 		swapPrice?.rate,
// 		activeField,
// 		setValue,
// 		toCurrency,
// 		fromCoin,
// 	]);

// 	useEffect(() => {
// 		if (amount)
// 			setFromDisplay(
// 				formatAmount(amount, fromCoin?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		if (toAmount)
// 			setToDisplay(
// 				formatAmount(toAmount, toCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 	}, [amount, fromCoin?.currency, toAmount, toCurrency?.currency]);

// 	useEffect(() => {
// 		if (swapPrice?.rate && activeField === "from" && amount) {
// 			const calculatedTo = amount * swapPrice.rate;
// 			setValue("to_amount", calculatedTo);
// 			setToDisplay(
// 				formatAmount(calculatedTo, toCurrency?.currency, {
// 					rawFormat: true,
// 				}),
// 			);
// 		}
// 	}, [swapPrice?.rate, activeField, amount, setValue, toCurrency]);

// 	const handleFromAmountChange = (text: string) => {
// 		if (!fromCoin) return;

// 		setActiveField("from");
// 		const parsed = parseFormattedNumber(text);
// 		setFromDisplay(text);
// 		setValue("amount", parsed);
// 		trigger("amount");
// 	};

// 	const handleToAmountChange = (text: string) => {
// 		if (!toCurrency || !fromCoin) return;

// 		setActiveField("to");
// 		const parsed = parseFormattedNumber(text);
// 		setToDisplay(text);
// 		setValue("to_amount", parsed);
// 	};

// 	const handleFromBlur = () => {
// 		if (!fromCoin) return;

// 		setFromDisplay(
// 			formatAmount(amount, fromCoin?.currency, { rawFormat: true }),
// 		);
// 	};

// 	const handleToBlur = () => {
// 		if (!toCurrency) return;

// 		setToDisplay(
// 			formatAmount(toAmount, toCurrency?.currency, { rawFormat: true }),
// 		);
// 	};

// 	const handleCryptoChange = (option: IOption<ICryptoWallet> | null) => {
// 		if (!option) return;

// 		if (option.raw) {
// 			setFromCoin(option.raw);
// 		} else {
// 			const selectedWallet = cryptoWallets.find(
// 				(wallet) => wallet.currency === option.value,
// 			);
// 			setFromCoin(selectedWallet || null);
// 		}

// 		setValue("amount", 0);
// 		setValue("to_amount", 0);
// 		setFromDisplay("");
// 		setToDisplay("");
// 		setActiveField("from");
// 	};

// 	const handleFiatChange = (option: IOption<IFiatWallet> | null) => {
// 		if (!option) return;

// 		if (option.raw) {
// 			setToCurrency(option.raw);
// 		} else {
// 			const selectedWallet = fiatWallets.find(
// 				(wallet) => wallet.currency === option.value,
// 			);
// 			setToCurrency(selectedWallet || null);
// 		}

// 		if (amount && fromCoin) {
// 			setActiveField("from");
// 			trigger("amount");
// 		}
// 	};

// 	const handleReviewOrder = () => {
// 		if (fromCoin && toCurrency && swapPrice) {
// 			const payload = {
// 				from: fromCoin,
// 				to: toCurrency,
// 				amount: Number(amount),
// 				method: CONVERSION_TYPE,
// 				price_data: swapPrice,
// 			};

// 			setTransaction(payload);

// 			onProceed();
// 		}
// 	};

// 	const handleAddNew = () => {};

// 	const walletHeader = (
// 		<div className="py-2 flex justify-between items-center">
// 			<h3 className="text-2xl font-bold font-body">Wallets</h3>
// 			<Button
// 				onClick={handleAddNew}
// 				className="text-sm flex items-center rounded-full"
// 			>
// 				Add New
// 				<Plus className="h-3 w-3 ml-2" />
// 			</Button>
// 		</div>
// 	);

// 	const isLoading = cryptoLoading || fiatLoading || rateLoading;
// 	const isButtonDisabled =
// 		!fromCoin || !toCurrency || !swapPrice || !amount || !formState.isValid;

// 	return (
// 		<div className="flex flex-col h-full">
// 			<div className="flex-1 p-6">
// 				{/* Crypto Selection */}
// 				<div className="flex flex-col items-center mb-8">
// 					<EnhancedSelect<ICryptoWallet>
// 						header={walletHeader}
// 						options={cryptoOptions}
// 						value={selectedCryptoOption}
// 						onChange={handleCryptoChange}
// 						placeholder="Select Cryptocurrency"
// 						isLoading={cryptoLoading}
// 						className="w-full mb-6"
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>From {option.value}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<div className="flex flex-col">
// 									<span>{option.label}</span>
// 									<span className="text-xs text-gray-500 group-hover:text-white">
// 										Balance:{" "}
// 										{formatAmount(
// 											option.raw?.balance,
// 											option.value,
// 										)}
// 									</span>
// 								</div>
// 							</div>
// 						)}
// 					/>

// 					{/* From Amount Input */}
// 					<div className="flex flex-col items-center w-full">
// 						<div className="flex items-end justify-center">
// 							<input
// 								type="text"
// 								inputMode="decimal"
// 								value={fromDisplay}
// 								onChange={(e) =>
// 									handleFromAmountChange(e.target.value)
// 								}
// 								onBlur={handleFromBlur}
// 								placeholder="0.00"
// 								disabled={!fromCoin}
// 								className={`text-6xl text-center bg-transparent outline-none ${
// 									formState.errors?.amount
// 										? "text-red-500"
// 										: ""
// 								} ${
// 									!fromCoin
// 										? "opacity-50 cursor-not-allowed"
// 										: ""
// 								}`}
// 							/>
// 							{/* <span className="text-2xl ml-2 mb-1">
// 								{fromCoin?.currency || ""}
// 							</span> */}
// 						</div>

// 						<div className="text-center mt-2 w-full">
// 							{cryptoLoading ? (
// 								<div className="flex items-center justify-center gap-2">
// 									<Loader2 className="h-4 w-4 animate-spin" />
// 									<span>Loading balance...</span>
// 								</div>
// 							) : fromCoin ? (
// 								<p className="text-sm text-gray-600">
// 									Available {fromCoin.currency} balance:{" "}
// 									{formatAmount(
// 										fromCoin.balance,
// 										fromCoin.currency,
// 									)}
// 								</p>
// 							) : (
// 								<div className="flex items-center justify-center gap-1 text-amber-500">
// 									<AlertCircle className="h-4 w-4" />
// 									<p className="text-sm">
// 										Please select a cryptocurrency first
// 									</p>
// 								</div>
// 							)}

// 							{formState.errors?.amount?.message ? (
// 								<p className="text-sm text-red-500 font-medium mt-1">
// 									{formState.errors.amount.message}
// 								</p>
// 							) : (
// 								swapPrice?.rate &&
// 								fromCoin &&
// 								toCurrency && (
// 									<p className="text-sm text-gray-600 mt-1">
// 										1 {fromCoin.currency} ≈{" "}
// 										{formatAmount(
// 											swapPrice.rate,
// 											toCurrency.currency,
// 											{ overrideMinDecimalPlaces: 4 },
// 										)}{" "}
// 										{toCurrency.currency}
// 									</p>
// 								)
// 							)}
// 						</div>
// 					</div>
// 				</div>

// 				{/* Conversion Arrow */}
// 				<div className="flex justify-center my-6">
// 					<div
// 						className={`bg-amber-100 rounded-full p-2 ${
// 							!fromCoin || !toCurrency ? "opacity-50" : ""
// 						}`}
// 					>
// 						<ArrowDataTransferVerticalIcon
// 							className={`h-6 w-6 ${
// 								fromCoin && toCurrency
// 									? "text-amber-500"
// 									: "text-amber-300"
// 							}`}
// 						/>
// 					</div>
// 				</div>

// 				{/* Fiat Selection */}
// 				<div className="flex flex-col items-center">
// 					{/* To Amount Input */}
// 					<div className="flex flex-col items-center w-full mb-6">
// 						<div className="flex items-end justify-center">
// 							<input
// 								type="text"
// 								inputMode="decimal"
// 								value={toDisplay}
// 								onChange={(e) =>
// 									handleToAmountChange(e.target.value)
// 								}
// 								onBlur={handleToBlur}
// 								placeholder="0.00"
// 								disabled={!toCurrency || !fromCoin}
// 								className={`text-6xl text-center bg-transparent outline-none ${
// 									!toCurrency || !fromCoin
// 										? "opacity-50 cursor-not-allowed"
// 										: ""
// 								}`}
// 							/>
// 							{/* <span className="text-2xl ml-2 mb-1">
// 								{toCurrency?.currency || ""}
// 							</span> */}
// 						</div>

// 						{!toCurrency && !fiatLoading ? (
// 							<div className="flex items-center justify-center gap-1 text-amber-500 mt-1">
// 								<AlertCircle className="h-4 w-4" />
// 								<p className="text-sm">
// 									Please select a fiat currency
// 								</p>
// 							</div>
// 						) : (
// 							swapPrice?.rate &&
// 							fromCoin &&
// 							toCurrency && (
// 								<p className="text-sm text-gray-600 mt-1">
// 									1 {toCurrency.currency} ≈{" "}
// 									{formatAmount(
// 										1 / swapPrice.rate,
// 										fromCoin.currency,
// 										{ overrideMinDecimalPlaces: 4 },
// 									)}{" "}
// 									{fromCoin.currency}
// 								</p>
// 							)
// 						)}
// 					</div>

// 					<EnhancedSelect<IFiatWallet>
// 						options={fiatOptions}
// 						value={selectedFiatOption}
// 						onChange={handleFiatChange}
// 						placeholder="Select Fiat currency"
// 						isLoading={fiatLoading}
// 						className="w-full mb-6"
// 						isSearchable={false}
// 						renderSelected={(option) => (
// 							<div className="flex items-center gap-2">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>To {option.value}</span>
// 							</div>
// 						)}
// 						renderOption={(option) => (
// 							<div className="flex items-center gap-2 w-full">
// 								<div className="w-6 h-6 flex items-center justify-center">
// 									{typeof option.icon === "string" ? (
// 										<img
// 											src={option.icon}
// 											alt={option.value}
// 											className="w-full h-full object-contain rounded-full"
// 										/>
// 									) : option.icon ? (
// 										option.icon
// 									) : (
// 										<div className="w-6 h-6 bg-gray-200 rounded-full" />
// 									)}
// 								</div>
// 								<span>{option.label}</span>
// 							</div>
// 						)}
// 					/>
// 				</div>
// 			</div>

// 			{/* Review Order Button */}
// 			<div className="p-6 border-t">
// 				<Button
// 					className="w-full h-14 rounded-full text-lg font-medium"
// 					disabled={isButtonDisabled || isLoading}
// 					onClick={handleSubmit(handleReviewOrder)}
// 				>
// 					{isLoading ? (
// 						<div className="flex items-center gap-2">
// 							<Loader2 className="h-5 w-5 animate-spin" />
// 							<span>Loading...</span>
// 						</div>
// 					) : !fromCoin ? (
// 						"Select a cryptocurrency to continue"
// 					) : !toCurrency ? (
// 						"Select a fiat currency to continue"
// 					) : (
// 						`Review ${toCurrency.currency} Order`
// 					)}
// 				</Button>
// 			</div>
// 		</div>
// 	);
// }


import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, AlertCircle, Plus } from "lucide-react";
import { IFiatWallet } from "@/types/fiat-wallets";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { IOption } from "@/types/general";
import { formatAmount } from "@/utils/format-amount";
import { parseFormattedNumber } from "@/utils/parse-formatted-number";
import EnhancedSelect from "@/components/enhanced-select";
import { Button } from "@/components/custom/button";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { useGetSwapPrice } from "@/hooks/api/swaps";
import useSellCryptoTransactionStore from "@/store/sell-crypto-store";
import useCryptoSelectionStore from "@/store/crypto-selection-store";
import { ArrowDataTransferVerticalIcon } from "../swap";

const sellSchema = z.object({
	amount: z.number().min(0, "Amount must be greater than 0"),
	to_amount: z.number().optional(),
});

type SellFormValues = z.infer<typeof sellSchema>;

const CONVERSION_TYPE = "INSTANT SWAP CRYPTO TO FIAT";

interface SellCryptoProps {
	onProceed: () => void;
}

export function SellCrypto({ onProceed }: SellCryptoProps) {
	const { setTransaction } = useSellCryptoTransactionStore();
	const { selectedCrypto } = useCryptoSelectionStore();

	const { data: cryptoWallets = [], isLoading: cryptoLoading } =
		useCryptoUserWallets();
	const { data: fiatWallets = [], isLoading: fiatLoading } =
		useFiatUserWallets();

	const form = useForm<SellFormValues>({
		resolver: zodResolver(sellSchema),
		defaultValues: {
			amount: undefined,
			to_amount: undefined,
		},
		mode: "onChange",
	});

	const { watch, setValue, handleSubmit, formState, trigger } = form;
	const amount = watch("amount");
	const toAmount = watch("to_amount");

	form.register("amount", {
		validate: (value) => {
			if (value) {
				if (value > (fromCoin?.balance || 0))
					return `Insufficient balance`;
			}
			return true;
		},
	});

	const [fromCoin, setFromCoin] = useState<ICryptoWallet | null>(null);
	const [toCurrency, setToCurrency] = useState<IFiatWallet | null>(null);
	const [manuallySelectedCrypto, setManuallySelectedCrypto] = useState<string | null>(null); // Track manual selection
	const [lastStoreSelection, setLastStoreSelection] = useState<string | null>(null); // Track last store selection

	const [fromDisplay, setFromDisplay] = useState("");
	const [toDisplay, setToDisplay] = useState("");
	const [activeField, setActiveField] = useState<"from" | "to">();

	const { data: swapPrice, isLoading: rateLoading } = useGetSwapPrice(
		fromCoin?.currency || "",
		toCurrency?.currency || "",
		CONVERSION_TYPE,
	);

	const cryptoOptions: IOption<ICryptoWallet>[] = cryptoWallets.map(
		(wallet) => ({
			id: `crypto-${wallet.currency}`,
			value: wallet.currency,
			label: wallet.currency,
			icon: wallet.image,
			raw: wallet,
		}),
	);

	const fiatOptions: IOption<IFiatWallet>[] = fiatWallets.map((wallet) => ({
		id: `fiat-${wallet.currency}`,
		value: wallet.currency,
		label: wallet.currency,
		icon: wallet.image,
		raw: wallet,
	}));

	const selectedCryptoOption =
		cryptoOptions.find((option) => option.value === fromCoin?.currency) ||
		null;

	const selectedFiatOption =
		fiatOptions.find((option) => option.value === toCurrency?.currency) ||
		null;

	// Auto-select crypto from store when component mounts or when data changes
	useEffect(() => {
		if (cryptoWallets.length === 0) return;

		const currentStoreSelection = selectedCrypto?.currency || null;
		
		// Check if store selection has changed
		const storeSelectionChanged = currentStoreSelection !== lastStoreSelection;
		
		if (storeSelectionChanged) {
			setLastStoreSelection(currentStoreSelection);
			
			// If store has a new selection, use it (this allows store updates to work)
			if (currentStoreSelection) {
				const matchingWallet = cryptoWallets.find(
					wallet => wallet.currency === currentStoreSelection
				);
				
				if (matchingWallet) {
					setFromCoin(matchingWallet);
					// Clear manual selection since store provided a new selection
					setManuallySelectedCrypto(null);
					return;
				}
			}
		}

		// If we have a manual selection and store hasn't changed, respect manual selection
		if (manuallySelectedCrypto) {
			const manualWallet = cryptoWallets.find(
				wallet => wallet.currency === manuallySelectedCrypto
			);
			
			if (manualWallet) {
				setFromCoin(manualWallet);
				return;
			} else {
				// Manual selection is no longer available, clear it
				setManuallySelectedCrypto(null);
			}
		}

		// Fallback: if no manual selection and no store selection, use first available
		if (!fromCoin && cryptoWallets.length > 0) {
			setFromCoin(cryptoWallets[0]);
		}
	}, [selectedCrypto, cryptoWallets, manuallySelectedCrypto, lastStoreSelection, fromCoin]);

	useEffect(() => {
		if (!swapPrice?.rate || !activeField) return;

		if (activeField === "from" && amount !== undefined) {
			const calculatedTo = amount * swapPrice.rate;
			setValue("to_amount", calculatedTo, { shouldValidate: true });
			setToDisplay(
				formatAmount(calculatedTo, toCurrency?.currency, {
					rawFormat: true,
				}),
			);
		} else if (activeField === "to" && toAmount !== undefined) {
			const calculatedFrom = toAmount / swapPrice.rate;
			setValue("amount", calculatedFrom, { shouldValidate: true });
			setFromDisplay(
				formatAmount(calculatedFrom, fromCoin?.currency, {
					rawFormat: true,
				}),
			);
		}
	}, [
		amount,
		toAmount,
		swapPrice?.rate,
		activeField,
		setValue,
		toCurrency,
		fromCoin,
	]);

	useEffect(() => {
		if (amount)
			setFromDisplay(
				formatAmount(amount, fromCoin?.currency, {
					rawFormat: true,
				}),
			);
		if (toAmount)
			setToDisplay(
				formatAmount(toAmount, toCurrency?.currency, {
					rawFormat: true,
				}),
			);
	}, [amount, fromCoin?.currency, toAmount, toCurrency?.currency]);

	useEffect(() => {
		if (swapPrice?.rate && activeField === "from" && amount) {
			const calculatedTo = amount * swapPrice.rate;
			setValue("to_amount", calculatedTo);
			setToDisplay(
				formatAmount(calculatedTo, toCurrency?.currency, {
					rawFormat: true,
				}),
			);
		}
	}, [swapPrice?.rate, activeField, amount, setValue, toCurrency]);

	const handleFromAmountChange = (text: string) => {
		if (!fromCoin) return;

		setActiveField("from");
		const parsed = parseFormattedNumber(text);
		setFromDisplay(text);
		setValue("amount", parsed);
		trigger("amount");
	};

	const handleToAmountChange = (text: string) => {
		if (!toCurrency || !fromCoin) return;

		setActiveField("to");
		const parsed = parseFormattedNumber(text);
		setToDisplay(text);
		setValue("to_amount", parsed);
	};

	const handleFromBlur = () => {
		if (!fromCoin) return;

		setFromDisplay(
			formatAmount(amount, fromCoin?.currency, { rawFormat: true }),
		);
	};

	const handleToBlur = () => {
		if (!toCurrency) return;

		setToDisplay(
			formatAmount(toAmount, toCurrency?.currency, { rawFormat: true }),
		);
	};

	const handleCryptoChange = (option: IOption<ICryptoWallet> | null) => {
		if (!option) {
			setManuallySelectedCrypto(null);
			setFromCoin(null);
			return;
		}

		// Mark that user has manually selected a crypto
		setManuallySelectedCrypto(option.value);

		if (option.raw) {
			setFromCoin(option.raw);
		} else {
			const selectedWallet = cryptoWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setFromCoin(selectedWallet || null);
		}

		setValue("amount", 0);
		setValue("to_amount", 0);
		setFromDisplay("");
		setToDisplay("");
		setActiveField("from");
	};

	const handleFiatChange = (option: IOption<IFiatWallet> | null) => {
		if (!option) return;

		if (option.raw) {
			setToCurrency(option.raw);
		} else {
			const selectedWallet = fiatWallets.find(
				(wallet) => wallet.currency === option.value,
			);
			setToCurrency(selectedWallet || null);
		}

		if (amount && fromCoin) {
			setActiveField("from");
			trigger("amount");
		}
	};

	const handleReviewOrder = () => {
		if (fromCoin && toCurrency && swapPrice) {
			const payload = {
				from: fromCoin,
				to: toCurrency,
				amount: Number(amount),
				method: CONVERSION_TYPE,
				price_data: swapPrice,
			};

			setTransaction(payload);

			onProceed();
		}
	};

	const handleAddNew = () => {};

	const walletHeader = (
		<div className="py-2 flex justify-between items-center">
			<h3 className="text-2xl font-bold font-body">Wallets</h3>
			<Button
				onClick={handleAddNew}
				className="text-sm flex items-center rounded-full"
			>
				Add New
				<Plus className="h-3 w-3 ml-2" />
			</Button>
		</div>
	);

	const isLoading = cryptoLoading || fiatLoading || rateLoading;
	const isButtonDisabled =
		!fromCoin || !toCurrency || !swapPrice || !amount || !formState.isValid;

	return (
		<div className="flex flex-col h-full">
			<div className="flex-1 p-6">
				{/* Crypto Selection */}
				<div className="flex flex-col items-center mb-8">
					<EnhancedSelect<ICryptoWallet>
						header={walletHeader}
						options={cryptoOptions}
						value={selectedCryptoOption}
						onChange={handleCryptoChange}
						placeholder="Select Cryptocurrency"
						isLoading={cryptoLoading}
						className="w-full mb-6"
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>From {option.value}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<div className="flex flex-col">
									<span>{option.label}</span>
									<span className="text-xs text-gray-500 group-hover:text-white">
										Balance:{" "}
										{formatAmount(
											option.raw?.balance,
											option.value,
										)}
									</span>
								</div>
							</div>
						)}
					/>

					{/* From Amount Input */}
					<div className="flex flex-col items-center w-full">
						<div className="flex items-end justify-center">
							<input
								type="text"
								inputMode="decimal"
								value={fromDisplay}
								onChange={(e) =>
									handleFromAmountChange(e.target.value)
								}
								onBlur={handleFromBlur}
								placeholder="0.00"
								disabled={!fromCoin}
								className={`text-6xl text-center bg-transparent outline-none ${
									formState.errors?.amount
										? "text-red-500"
										: ""
								} ${
									!fromCoin
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
						</div>

						<div className="text-center mt-2 w-full">
							{cryptoLoading ? (
								<div className="flex items-center justify-center gap-2">
									<Loader2 className="h-4 w-4 animate-spin" />
									<span>Loading balance...</span>
								</div>
							) : fromCoin ? (
								<p className="text-sm text-gray-600">
									Available {fromCoin.currency} balance:{" "}
									{formatAmount(
										fromCoin.balance,
										fromCoin.currency,
									)}
								</p>
							) : (
								<div className="flex items-center justify-center gap-1 text-amber-500">
									<AlertCircle className="h-4 w-4" />
									<p className="text-sm">
										Please select a cryptocurrency first
									</p>
								</div>
							)}

							{formState.errors?.amount?.message ? (
								<p className="text-sm text-red-500 font-medium mt-1">
									{formState.errors.amount.message}
								</p>
							) : (
								swapPrice?.rate &&
								fromCoin &&
								toCurrency && (
									<p className="text-sm text-gray-600 mt-1">
										1 {fromCoin.currency} ≈{" "}
										{formatAmount(
											swapPrice.rate,
											toCurrency.currency,
											{ overrideMinDecimalPlaces: 4 },
										)}{" "}
										{toCurrency.currency}
									</p>
								)
							)}
						</div>
					</div>
				</div>

				{/* Conversion Arrow */}
				<div className="flex justify-center my-6">
					<div
						className={`bg-amber-100 rounded-full p-2 ${
							!fromCoin || !toCurrency ? "opacity-50" : ""
						}`}
					>
						<ArrowDataTransferVerticalIcon
							className={`h-6 w-6 ${
								fromCoin && toCurrency
									? "text-amber-500"
									: "text-amber-300"
							}`}
						/>
					</div>
				</div>

				{/* Fiat Selection */}
				<div className="flex flex-col items-center">
					{/* To Amount Input */}
					<div className="flex flex-col items-center w-full mb-6">
						<div className="flex items-end justify-center">
							<input
								type="text"
								inputMode="decimal"
								value={toDisplay}
								onChange={(e) =>
									handleToAmountChange(e.target.value)
								}
								onBlur={handleToBlur}
								placeholder="0.00"
								disabled={!toCurrency || !fromCoin}
								className={`text-6xl text-center bg-transparent outline-none ${
									!toCurrency || !fromCoin
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
						</div>

						{!toCurrency && !fiatLoading ? (
							<div className="flex items-center justify-center gap-1 text-amber-500 mt-1">
								<AlertCircle className="h-4 w-4" />
								<p className="text-sm">
									Please select a fiat currency
								</p>
							</div>
						) : (
							swapPrice?.rate &&
							fromCoin &&
							toCurrency && (
								<p className="text-sm text-gray-600 mt-1">
									1 {toCurrency.currency} ≈{" "}
									{formatAmount(
										1 / swapPrice.rate,
										fromCoin.currency,
										{ overrideMinDecimalPlaces: 4 },
									)}{" "}
									{fromCoin.currency}
								</p>
							)
						)}
					</div>

					<EnhancedSelect<IFiatWallet>
						options={fiatOptions}
						value={selectedFiatOption}
						onChange={handleFiatChange}
						placeholder="Select Fiat currency"
						isLoading={fiatLoading}
						className="w-full mb-6"
						isSearchable={false}
						renderSelected={(option) => (
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>To {option.value}</span>
							</div>
						)}
						renderOption={(option) => (
							<div className="flex items-center gap-2 w-full">
								<div className="w-6 h-6 flex items-center justify-center">
									{typeof option.icon === "string" ? (
										<img
											src={option.icon}
											alt={option.value}
											className="w-full h-full object-contain rounded-full"
										/>
									) : option.icon ? (
										option.icon
									) : (
										<div className="w-6 h-6 bg-gray-200 rounded-full" />
									)}
								</div>
								<span>{option.label}</span>
							</div>
						)}
					/>
				</div>
			</div>

			{/* Review Order Button */}
			<div className="p-6 border-t">
				<Button
					className="w-full h-14 rounded-full text-lg font-medium"
					disabled={isButtonDisabled || isLoading}
					onClick={handleSubmit(handleReviewOrder)}
				>
					{isLoading ? (
						<div className="flex items-center gap-2">
							<Loader2 className="h-5 w-5 animate-spin" />
							<span>Loading...</span>
						</div>
					) : !fromCoin ? (
						"Select a cryptocurrency to continue"
					) : !toCurrency ? (
						"Select a fiat currency to continue"
					) : (
						`Review ${toCurrency.currency} Order`
					)}
				</Button>
			</div>
		</div>
	);
}