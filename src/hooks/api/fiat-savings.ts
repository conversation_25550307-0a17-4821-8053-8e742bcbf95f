import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import { FiatSavingsService } from "@/services/fiat-savings";
import {
	FundFiatSavingsPayload,
    CreateFiatSavingGoalPayload,
	WithdrawFiatSavingsPayload,
} from "@/types/fiat-savings";

export const FIAT_SAVINGS = "fiat-savings";


export const useGetFiatSavingsGoals = () => {
	return useQuery({
		queryKey: [FIAT_SAVINGS, "goals"],
		queryFn: () => FiatSavingsService.getSavingGoals(),
		staleTime:100 * 60 *5,
	});
};


export const useCreateFiatSavingGoal = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: CreateFiatSavingGoalPayload) =>
			FiatSavingsService.createSavingGoal(payload),
		onSuccess: () => {
			notify("Saving goal created successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({
				queryKey: [FIAT_SAVINGS, "goals"],
			});
		},
		onError: (error) => {
			const errorMessage =
				error?.message || "Failed to create saving goal";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};


export const useFundFiatSavingsManual = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: FundFiatSavingsPayload) =>
			FiatSavingsService.fundSavingsManual(payload),
		onSuccess: () => {
			notify("Savings funded successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [FIAT_SAVINGS] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to fund savings";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};


export const useFundFiatSavingsAuto = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: FundFiatSavingsPayload) =>
			FiatSavingsService.fundSavingsAuto(payload),
		onSuccess: () => {
			notify("Auto-funding setup successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [FIAT_SAVINGS] });
		},
		onError: (error) => {
			const errorMessage =
				error?.message || "Failed to setup auto-funding";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};


export const useWithdrawFiatSavings = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: WithdrawFiatSavingsPayload) =>
			FiatSavingsService.withdrawSavings(payload),
		onSuccess: () => {
			notify("Withdrawal successful!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [FIAT_SAVINGS] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to withdraw savings";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};
