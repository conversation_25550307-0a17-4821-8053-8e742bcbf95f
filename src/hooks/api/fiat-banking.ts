import { useMutation, useQuery } from "@tanstack/react-query";
// import { notify, ResponseStatus } from "@/utils/notifications";
// import { FiatBankingService } from "@/services/fiat-banking";

import { FiatBankingService } from "@/services/fiat-banking";
import {
	Bank,
	CreateDepositPayload,
	CreateWithdrawalPayload,
	IFiatDepositMethod,
	IFiatWithdrawalMethod,
	ResolveAccountPayload,
} from "@/types/fiat-banking";
import { IOption } from "@/types/general";

export const FIAT_BANKING = "fiat-banking";

export const useResolveAccount = (payload: ResolveAccountPayload) => {
	const requestPayload = {
		...payload,
		transaction_method: payload.transaction_method ?? "CLYP NGN WITHDRAWAL",
	};
	return useQuery({
		queryKey: [FIAT_BANKING, "resolve-account", requestPayload],
		queryFn: () => FiatBankingService.resolveAccount(requestPayload),
		enabled: !!payload.accountNumber && !!payload.bankCode,
	});
};

export const useBankList = (method: string) => {
	return useQuery({
		queryKey: [FIAT_BANKING, "banks"],
		queryFn: () => FiatBankingService.getBankList(method),
		select: (data) =>
			data.data.map((item) => ({
				label: item["Bank name"],
				value: item["Bank code"],
				raw: item as Bank,
			})) as IOption<Bank>[],
	});
};

// export const useCreateDepositMethod = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: CreateDepositMethodPayload) =>
// 			FiatBankingService.createDepositMethod(payload),
// 		onSuccess: () => {
// 			notify(
// 				"Deposit method created successfully!",
// 				ResponseStatus.SUCCESS,
// 			);
// 			queryClient.invalidateQueries({
// 				queryKey: [FIAT_BANKING, "methods"],
// 			});
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to create deposit method";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

export const useCreateDeposit = () => {
	// const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: CreateDepositPayload) =>
			FiatBankingService.createDeposit(payload),
		// onSuccess: () => {
		// 	notify("Deposit created successfully!", ResponseStatus.SUCCESS);
		// 	queryClient.invalidateQueries({
		// 		queryKey: [FIAT_BANKING, "transactions"],
		// 	});
		// },
		// onError: (error) => {
		// 	const errorMessage = error?.message || "Failed to create deposit";
		// 	notify(errorMessage, ResponseStatus.ERROR);
		// },
	});
};

export const useCreateWithdrawal = () => {
	// const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: Partial<CreateWithdrawalPayload>) =>
			FiatBankingService.createWithdrawal(payload),
		// onSuccess: () => {
		// 	notify("Withdrawal created successfully!", ResponseStatus.SUCCESS);
		// 	queryClient.invalidateQueries({
		// 		queryKey: [FIAT_BANKING, "transactions"],
		// 	});
		// },
	});
};

export const useGetFiatWalletDepositMethod = (account_id: string) => {
	return useQuery({
		queryKey: [FIAT_BANKING, "deposit-method", account_id],
        enabled: !!account_id,
		queryFn: () => FiatBankingService.getDepositMethod(account_id),
        select: (data) => data?.data as IFiatDepositMethod[],
	});
};

// export const useFiatTransaction = (payload: GetTransactionPayload) => {
// 	return useQuery({
// 		queryKey: [FIAT_BANKING, "transaction", payload.transactionId],
// 		queryFn: () => FiatBankingService.getTransactionById(payload),
// 	});
// };

export const useGetFiatWalletWithdrawalMethod = (currency: string) => {
	return useQuery({
		queryKey: ["fiat", "withdrawal-methods", currency],
		enabled: !!currency,
		// enabled: !!wallet,
		queryFn: () => FiatBankingService.getWithdrawFiatMethods(currency),
		select: (data) => data?.data as IFiatWithdrawalMethod[],
	});
};
