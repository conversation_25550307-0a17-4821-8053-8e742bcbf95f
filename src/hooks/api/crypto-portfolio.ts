import { CryptoPortfolioService } from "@/services/crypto-portfolio";
import { CoinDataPayload, MarketChartPayload, MarketDataPayload } from "@/types/crypto-portfolio";
import { useQuery } from "@tanstack/react-query";

export const CRYPTO_PORTFOLIO = "crypto-portfolio";


export const useCoinsMarketData = (payload: MarketDataPayload) => {
	return useQuery({
		queryKey: [CRYPTO_PORTFOLIO, "market-data", payload.currency],
		queryFn: () => CryptoPortfolioService.fetchCoinsMarketData(payload),
	});
};


export const useCoinsMarketChart = (payload: MarketChartPayload) => {
	return useQuery({
		queryKey: [CRYPTO_PORTFOLIO, "market-chart", payload],
		queryFn: () => CryptoPortfolioService.fetchCoinsMarketChart(payload),
	});
};


export const useCoinData = (payload: CoinDataPayload) => {
	return useQuery({
		queryKey: [CRYPTO_PORTFOLIO, "coin-data", payload.coin_id],
		queryFn: () => CryptoPortfolioService.fetchCoinData(payload),
	});
};
