import { useMutation, useQuery } from "@tanstack/react-query";
import { BillsService } from "@/services/bills";
import { ValidateCustomerBillPayload, PurchaseBillPayload, PurchaseAirtimePayload } from "@/types/bills";
// import { BillsFromCategoryPayload, PurchaseBillPayload, ValidateCustomerBillPayload } from "@/types/bills";

export const BILLS = "bills";

// Get All Biller Categories
// export const useAllBillerCategories = () => {
// 	return useQuery({
// 		queryKey: [BILLS, "categories"],
// 		queryFn: () => BillsService.getAllBillerCategory(),
// 	});
// };

// // Get Bills from Category
// export const useBillsFromCategory = (payload: BillsFromCategoryPayload) => {
// 	return useQuery({
// 		queryKey: [BILLS, "category", payload],
// 		queryFn: () => BillsService.getBillsFromCategory(payload),
// 	});
// };

// Get Bills from Category
// export const useBillsFromCategory = (payload: BillsFromCategoryPayload) => {
// 	return useQuery({
// 		queryKey: [BILLS, "category", payload],
// 		queryFn: () => BillsService.getBillsFromCategory(payload),
// 		enabled: !!payload,
// 	});
// };

// Validate Customer Bill
export const useValidateCustomerBill = () => {
	return useMutation({
		mutationFn: (payload: ValidateCustomerBillPayload) =>
			BillsService.validateCustomerBill(payload),
		
	});
};

// Purchase Bill
export const usePurchaseBill = () => {
	return useMutation({
		mutationFn: (payload: PurchaseBillPayload) =>
			BillsService.purchaseBill(payload),
		
	});
};

export const usePurchaseAirtime = () => {
    return useMutation({
        mutationFn: (payload: PurchaseAirtimePayload) =>
            BillsService.purchaseAirtime(payload),
    });
};
