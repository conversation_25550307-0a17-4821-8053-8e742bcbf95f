import { useQuery } from "@tanstack/react-query";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CryptoWalletService } from "@/services/crypto-wallets";
import { toast } from "sonner";

export const CRYPTO_WALLETS = "crypto-wallets";


export const useAddWallet = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (currency: string) => CryptoWalletService.addWallet(currency),
	onSuccess: () => {
	queryClient.invalidateQueries({ queryKey: [CRYPTO_WALLETS] });
	},
	});
};


// export const useCreateWallet = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: CreateCryptoWalletPayload) =>
// 			CryptoWalletService.createWallet(payload),
// 	});
// };


export const useCryptoUserWallets = () => {
	return useQuery({
		queryKey: [CRYPTO_WALLETS],
		queryFn: () => CryptoWalletService.getUserWallets(),
        select: (data) => data.user_wallets,
	});
};

export const useAddCryptoWallet = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (currency: string) => CryptoWalletService.addWallet(currency),
		onSuccess: (_, currency) => {
			toast.success(`Wallet for ${currency} added`);
			queryClient.invalidateQueries({ queryKey: ["crypto-wallets"] });
		},
		onError: () => {
			toast.error("Failed to add wallet");
		},
	});
};

