import { useMutation, useQuery } from "@tanstack/react-query";
// import { notify, ResponseStatus } from "@/utils/notifications";

import { SwapService } from "@/services/swap";
import { CreateSwapPayload, ISwapPriceData } from "@/types/swaps";

export const SWAP = "swap";

export const useCreateSwap = () => {
	return useMutation({
		mutationFn: (payload: CreateSwapPayload) =>
			SwapService.createSwap(payload),
	});
};

// export const useSwappingPrice = () => {
// 	return useQuery({
// 		queryKey: [SWAP, "price"],
// 		queryFn: () => SwapService.getSwappingPrice(),
// 	});
// };

// export const useSwapsForCurrency = (payload: GetSwapsForCurrencyPayload) => {
// 	return useQuery({
// 		queryKey: [SWAP, "currency", payload.currency],
// 		queryFn: () => SwapService.getSwapsForCurrency(payload),
// 	});
// };

// export const useGetSwapRates = (price_id: string) => {
// 	return useQuery({
// 		queryKey: [],
// 		queryFn: () => SwapService.getSwappingRate(price_id),
// 	});
// };

export const useGetSwapRates = () => {
	return useMutation({
		mutationFn: (price_id: string) => SwapService.getSwappingRate(price_id),
	});
};

export function useGetSwapPrice(
	fromCurrency: string,
	toCurrency: string,
	conversionType: string,
) {
	const isEnabled = !!fromCurrency && !!toCurrency && !!conversionType;
	return useQuery({
		enabled: isEnabled,
		queryKey: [fromCurrency, toCurrency, conversionType],
		queryFn: () =>
			SwapService.getSwappingPrice({
				from_currency: fromCurrency,
				to_currency: toCurrency,
				method: conversionType,
				amount: "1",
			}),
		select: (data) => data?.swap_price_data as ISwapPriceData,
	});
}
