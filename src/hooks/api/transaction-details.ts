import { useState, useEffect } from "react";
import { APP_CONFIG } from "@/config/index";

export interface TransactionDetail {
	account_name: string;
	currency: string;
	transaction_id: string;
	amount: string;
	fee: string;
	status: string;
	type: string;
	method: string;
	details: {
		type: string;
		details: Array<{
			description: string;
			timestamp: string | null;
		}>;
	};
	user_note: string | null;
	analytics: any;
	sender: any;
	beneficiary?: any;
	date: string;
}

export function useTransactionDetail(transactionId: string | null) {
	const [transaction, setTransaction] = useState<TransactionDetail | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (!transactionId) {
			setTransaction(null);
			return;
		}

		const fetchTransactionDetail = async () => {
			setLoading(true);
			setError(null);

			try {
				const token = localStorage.getItem("auth_token");

				if (!token) {
					throw new Error("User not authenticated");
				}

				const response = await fetch(
					`${APP_CONFIG.API_URLS.CURRENCY}/fiat-banking/get-transaction-by-id?transaction_id=${transactionId}`,
					{
						headers: {
							Authorization: `Bearer ${token}`,
							"Content-Type": "application/json",
						},
					}
				);

				if (!response.ok) {
					throw new Error(`Failed to fetch transaction details: ${response.status}`);
				}

				const data = await response.json();
				
				const transactionData = data.data || data;
				setTransaction(transactionData);
			} catch (err: any) {
				console.error("Transaction detail fetch error:", err);
				setError(err.message || "Failed to fetch transaction details");
			} finally {
				setLoading(false);
			}
		};

		fetchTransactionDetail();
	}, [transactionId]);

	return {
		transaction,
		loading,
		error,
	};
}