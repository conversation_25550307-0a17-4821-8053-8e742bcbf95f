import { useState, useEffect } from "react";
import { useFiatUserWallets } from "@/hooks/api/fiat-wallets";
import { APP_CONFIG } from "@/config/index";

export interface Transaction {
	user_id:string;
    id: number;
    transaction_id: string;
    type: string;
    time: string;
    date: string;
    account: string;
    amount: string;
    status: "Successful" | "Failed" | "Processing" | "Pending";
}

export interface TransactionWithTimestamp extends Transaction {
    timestamp: number;
}

const formatStatus = (apiStatus: string): Transaction["status"] => {
    switch (apiStatus.toUpperCase()) {
        case "CREATED":
            return "Pending";
        case "SUCCESSFUL":
        case "COMPLETED":
            return "Successful";
        case "FAILED":
            return "Failed";
        case "PROCESSING":
            return "Processing";
        default:
            return "Pending";
    }
};

export function useFiatTransactions() {
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const {
        data: wallets,
        isLoading: walletsLoading,
        isError,
        error: walletsError,
    } = useFiatUserWallets();

    useEffect(() => {
        if (!wallets || wallets.length === 0) return;

        const fetchTransactions = async () => {
            setLoading(true);
            setError(null);

            try {
                const token = localStorage.getItem("auth_token");
                if (!token) {
                    throw new Error("User not authenticated");
                }

                const allTransactions: TransactionWithTimestamp[] = [];

                for (const wallet of wallets) {
                    const response = await fetch(
                        `${APP_CONFIG.API_URLS.CURRENCY}/fiat-banking/get-user-fiat-wallet-transactions?account_id=${wallet.account_id}`,
                        {
                            headers: {
                                Authorization: `Bearer ${token}`,
                                "Content-Type": "application/json",
                            },
                        },
                    );

                    if (!response.ok) {
                        console.warn(
                            `Failed to fetch transactions for account ${wallet.account_id}`,
                        );
                        continue;
                    }

                    const data = await response.json();
                    const transactionsArray = data.data || [];

                    const formatted: TransactionWithTimestamp[] =
                        transactionsArray.map((item: any, index: number) => {
                            const fullDate = new Date(item.date);
                            return {
                                id: Date.now() + index + Math.random(),
                                transaction_id: item.transaction_id,
                                type: item.method || "Unknown",
                                time: fullDate.toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                }),
                                date: fullDate.toLocaleDateString(),
                                account: item.account_name || "Unknown Account",
                                amount: `${item.currency || "NGN"} ${Number(
                                    item.amount,
                                ).toLocaleString()}`,
                                status: formatStatus(item.status),
                                timestamp: fullDate.getTime(),
                            };
                        });

                    allTransactions.push(...formatted);
                }

                allTransactions.sort((a, b) => b.timestamp - a.timestamp);

                const cleaned: Transaction[] = allTransactions.map(
                    ({ timestamp, ...rest }) => rest,
                );

                setTransactions(cleaned);
            } catch (err: any) {
                console.error("Transaction fetch error:", err);
                setError(err.message || "Failed to fetch transactions.");
            } finally {
                setLoading(false);
            }
        };

        fetchTransactions();
    }, [wallets]);

    return {
        transactions,
        loading: loading || walletsLoading,
        error: error || (isError ? (walletsError as Error)?.message : null),
    };
}