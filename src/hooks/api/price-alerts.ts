// import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
// import { notify, ResponseStatus } from "@/utils/notifications";
// import { PriceAlertsService } from "@/services/price-alerts";

export const PRICE_ALERTS = "price-alerts";


// export const useSetPriceAlert = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: SetPriceAlertPayload) =>
// 			PriceAlertsService.setPriceAlert(payload),
// 		onSuccess: () => {
// 			notify("Price alert set successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [PRICE_ALERTS] });
// 		},
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to set price alert";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };


// export const useEditPriceAlertStatus = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: EditPriceAlertStatusPayload) =>
// 			PriceAlertsService.editPriceAlertStatus(payload),
// 		onSuccess: () => {
// 			notify("Price alert updated successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [PRICE_ALERTS] });
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to update price alert";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };


// export const useDeletePriceAlert = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: DeletePriceAlertPayload) =>
// 			PriceAlertsService.deletePriceAlert(payload),
// 		onSuccess: () => {
// 			notify("Price alert deleted successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [PRICE_ALERTS] });
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to delete price alert";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };


// export const usePriceAlertCoins = () => {
// 	return useQuery({
// 		queryKey: [PRICE_ALERTS, "coins"],
// 		queryFn: () => PriceAlertsService.getPriceAlertCoins(),
// 	});
// };


// export const usePriceAlertList = () => {
// 	return useQuery({
// 		queryKey: [PRICE_ALERTS, "list"],
// 		queryFn: () => PriceAlertsService.getPriceAlertList(),
// 	});
// };
