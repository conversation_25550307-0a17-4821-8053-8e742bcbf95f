import { useMutation, useQuery } from "@tanstack/react-query";
import { UserService } from "@/services/user";
import useUserStore from "@/store/user-store";
import { IClypUser, IUser } from "@/types/user";
import { useQueryClient } from "@tanstack/react-query";
import { ResponseStatus } from "@/config/enums";
import { notify } from "@/utils/notify";
export const USER = "user";

export const useChatHistory = () => {
	return useQuery({
		queryKey: [USER, "chat-history"],
		queryFn: () => UserService.getChatHistory(),
	});
};

export const useFullUserData = () => {
	const { setUser, setKycDone, setBvnDone, setKycLevels } =
		useUserStore.getState();

	return useQuery({
		queryKey: [USER, "full-data"],
		queryFn: async () => {
			const data = await UserService.getFullUserData();
			const { kycDone, bvnDone, kyc_level_completed } = data;
			const user: IUser = data?.user;

			setUser(user);
			
			setKycDone(kycDone);
			setBvnDone(bvnDone);
			setKycLevels(kyc_level_completed);
			return data;
		},
	});
};

export function useRefreshUserData() {
	const { setUser, setKycDone, setBvnDone, setKycLevels } =
		useUserStore.getState();
	// const { setProModeEnabled, setEmailNotificationsEnabled, setPriceAlertNotificationsEnabled } =
	// 	useSettingsStore.getState()

	return useMutation({
		mutationFn: () => UserService.getFullUserData(),
		onSuccess: (res) => {
			const { kycDone, bvnDone, kyc_level_completed } = res;
			const user: IUser = res?.user;
			//
			setUser(user);
			setKycDone(kycDone);
			setBvnDone(bvnDone);
			setKycLevels(kyc_level_completed);
			console.log("User data refreshed:", user);
			// setProModeEnabled(user?.proMode)
			// setEmailNotificationsEnabled(user?.emailNotifSetting)
			// setPriceAlertNotificationsEnabled(user?.priceAlertSettings)
		},
		onError: (e) => {
			throw e;
		},
	});
}

// export const useAccountInsightsOverview = () => {
// 	return useQuery({
// 		queryKey: [USER, "account-insights"],
// 		queryFn: () => UserService.getAccountInsightsOverview(),
// 	});
// };

// export const useNews = () => {
// 	return useQuery({
// 		queryKey: [USER, "news"],
// 		queryFn: () => UserService.getNews(),
// 	});
// };

// export const useAvailableCountries = () => {
// 	return useQuery({
// 		queryKey: [USER, "countries"],
// 		queryFn: () => UserService.getAvailableCountries(),
// 	});
// };

// export const useStates = (countryId: string) => {
// 	return useQuery({
// 		queryKey: [USER, "states", countryId],
// 		queryFn: () => UserService.getStates(countryId),
// 		enabled: !!countryId,
// 	});
// };

// export const useCitiesInState = (stateId: string) => {
// 	return useQuery({
// 		queryKey: [USER, "cities", stateId],
// 		queryFn: () => UserService.getCitiesInState(stateId),
// 		enabled: !!stateId,
// 	});
// };

// export const useAllUserCourses = () => {
// 	return useQuery({
// 		queryKey: [USER, "courses"],
// 		queryFn: () => UserService.getAllUserCourses(),
// 	});
// };

// export const useAllUserTopics = () => {
// 	return useQuery({
// 		queryKey: [USER, "topics"],
// 		queryFn: () => UserService.getAllUserTopics(),
// 	});
// };

// export const useReferralData = () => {
// 	return useQuery({
// 		queryKey: [USER, "referral"],
// 		queryFn: () => UserService.getReferralData(),
// 	});
// };

export const useAvailableProfileImages = () => {
	return useQuery({
		queryKey: [USER, "profile-images"],
		queryFn: () => UserService.getAvailableProfileImages(),
	});
};

// export const useUserNotifications = () => {
// 	return useQuery({
// 		queryKey: [USER, "notifications"],
// 		queryFn: () => UserService.getUserNotifications(),
// 	});
// };

// export const useUnlinkAccount = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: () => UserService.unlinkAccount(),
// 		onSuccess: () => {
// 			notify("Account unlinked successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [USER] });
// 		},
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to unlink account";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useMarkNotificationAsRead = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (notificationId: string) =>
// 			UserService.markNotificationAsRead(notificationId),
// 		onSuccess: () => {
// 			queryClient.invalidateQueries({
// 				queryKey: [USER, "notifications"],
// 			});
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to mark notification as read";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

export const useVerifyPin = () => {
	return useMutation({
		mutationFn: (pin: string) => UserService.verifyPin(pin),
	});
};

export const useGetUserCustomerByClypId = (identifier: string) => {
	return useQuery({
		queryKey: ["user-by-clyp-id", identifier],
		retry: 1,
		queryFn: () => UserService.getUserCustomerByClypId(identifier),
		enabled: !!identifier,
		select: (data) => data.user as IClypUser,
	});
};

export const useSendChat = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (message: string) => UserService.sendChat(message),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [USER, "chat-history"] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to send message";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};

export const useSetPin = () => {
	// const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (pin: string) => UserService.setPin(pin),
		// onSuccess: () => {
		// 	notify("PIN set successfully!", ResponseStatus.SUCCESS);
		// 	queryClient.invalidateQueries({ queryKey: [USER] });
		// },
	});
};

// export const useChangePin = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: ChangePinPayload) =>
// 			UserService.changePin(payload),
// 		onSuccess: () => {
// 			notify("PIN changed successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [USER] });
// 		},
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to change PIN";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useToggleProMode = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: ProModePayload) =>
// 			UserService.toggleProMode(payload),
// 		onSuccess: () => {
// 			notify("Pro mode updated successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [USER] });
// 		},
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to update Pro mode";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useValidateBusiness = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: BusinessValidationPayload) =>
// 			UserService.validateBusiness(payload),
// 		onSuccess: () => {
// 			notify("Business validated successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [USER] });
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to validate business";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useAddUserAddress = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: AddressPayload) =>
// 			UserService.addUserAddress(payload),
// 		onSuccess: () => {
// 			notify("Address added successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [USER] });
// 		},
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to add address";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useUpdateUsername = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: UsernamePayload) =>
// 			UserService.updateUsername(payload),
// 		onSuccess: () => {
// 			notify("Username updated successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [USER] });
// 		},
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to update username";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useUploadProfileImage = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (file: File) => UserService.uploadProfileImage(file),
// 		onSuccess: () => {
// 			notify(
// 				"Profile image updated successfully!",
// 				ResponseStatus.SUCCESS,
// 			);
// 			queryClient.invalidateQueries({ queryKey: [USER] });
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to upload profile image";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

export const useUploadProfileImageAvatar = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (picture: string) => UserService.uploadProfileImageAvatar(picture),
		onSuccess: () => {
			notify(
				"Profile Avatar updated successfully!",
				ResponseStatus.SUCCESS,
			);
			queryClient.invalidateQueries({ queryKey: [USER] });
		},
		onError: (error) => {
			const errorMessage =
				error?.message || "Failed to upload profile image";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};

export const useRequestEmailChange = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (newEmail: string) => UserService.requestEmailChange(newEmail),
		onSuccess: () => {
			notify(
				"Email change request sent successfully!",
				ResponseStatus.SUCCESS
			);
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to request email change";
			notify(errorMessage, ResponseStatus.ERROR);
		}
	});
};

export const useConfirmEmailChange = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: { authToken: string; newEmail: string }) =>
			UserService.confirmEmailChange(payload),
		onSuccess: () => {
			notify("Email changed successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [USER] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to confirm email change";
			notify(errorMessage, ResponseStatus.ERROR);
		}
	});
};

// export const useSubmitLoanRequest = () => {
// 	return useMutation({
// 		mutationFn: (payload: LoanRequestPayload) =>
// 			UserService.submitLoanRequest(payload),
// 		onSuccess: () => {
// 			notify(
// 				"Loan request submitted successfully!",
// 				ResponseStatus.SUCCESS,
// 			);
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to submit loan request";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useAddReferee = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: RefereePayload) =>
// 			UserService.addReferee(payload),
// 		onSuccess: () => {
// 			notify("Referee added successfully!", ResponseStatus.SUCCESS);
// 			queryClient.invalidateQueries({ queryKey: [USER, "referral"] });
// 		},
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to add referee";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useSubmitCode = () => {
// 	return useMutation({
// 		mutationFn: (payload: CodeSubmissionPayload) =>
// 			UserService.submitCode(payload),
// 		onError: (error) => {
// 			const errorMessage = error?.message || "Failed to submit code";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };

// export const useUpdateNotificationStatus = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: NotificationStatusPayload) =>
// 			UserService.updateNotificationStatus(payload),
// 		onSuccess: () => {
// 			queryClient.invalidateQueries({
// 				queryKey: [USER, "notifications"],
// 			});
// 		},
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to update notification status";
// 			notify(errorMessage, ResponseStatus.ERROR);
// 		},
// 	});
// };
