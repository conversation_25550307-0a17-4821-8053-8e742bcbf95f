import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { notify } from "@/utils/notify";
import { ResponseStatus } from "@/config/enums";
import { CryptoSavingsService } from "@/services/crypto-savings";
import { 
    CryptosavingsGoalPayload, 
    FundSavingsPayload, // Re-added FundSavingsPayload import
    WithdrawCryptoSavingsPayload 
} from "@/types/crypto-savings";
import { AutoSavePayload } from "@/types/fiat-savings"; // Import AutoSavePayload

export const CRYPTO_SAVINGS = "crypto-savings";

export const useCreateCryptoSavingGoal = () => {

	
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: CryptosavingsGoalPayload) =>
			CryptoSavingsService.createSavingGoal(payload),
		onSuccess: () => {
			notify("Saving goal created successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CRYPTO_SAVINGS] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to create saving goal";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};

export const useGetCryptoSavingsGoals = () => {
	// const queryClient = useQueryClient();
	return useQuery({
		queryKey: [CRYPTO_SAVINGS],
		queryFn: async () =>  CryptoSavingsService.getSavingsGoals(),
		staleTime: 1000 * 60 * 5,
	});
};

export const useFundSavingsManual = ()=>{
	const queryClient = useQueryClient();
	return useMutation({
mutationFn: (payload: FundSavingsPayload) =>
			CryptoSavingsService.fundSavingsManual(payload),
		onSuccess:()=>{
			queryClient.invalidateQueries({
				queryKey:[CRYPTO_SAVINGS]
			})
			notify("Savings funded successfully",ResponseStatus.SUCCESS)
		}
	})
}

export const useFundCryptoSavingsManual = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (payload: FundSavingsPayload) => CryptoSavingsService.fundSavingsManual(payload),
		onSuccess: () => {
			notify("Savings funded successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CRYPTO_SAVINGS] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to fund savings";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};

export const useFundCryptoSavingsAuto = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (payload: AutoSavePayload) => 
		 CryptoSavingsService.fundSavingsAuto(payload),
		onSuccess: () => {
			notify("Auto-funding setup successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CRYPTO_SAVINGS] });
		},
		onError: (error) => {
			const errorMessage = (error as Error)?.message || "Failed to setup auto-funding";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};

export const useWithdrawCryptoSavings = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (payload: WithdrawCryptoSavingsPayload) => CryptoSavingsService.withdrawSavings(payload),
		onSuccess: () => {
			notify("Withdrawal successful!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CRYPTO_SAVINGS] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to withdraw savings";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};
