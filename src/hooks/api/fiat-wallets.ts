import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { FiatWalletService } from "@/services/fiat-wallets";

export const FIAT_WALLETS = "fiat-wallets";

export const useFiatUserWallets = () => {
	return useQuery({
		queryKey: [FIAT_WALLETS],
		queryFn: () => FiatWalletService.getUserWallets(),
        select: (data) => data.wallets,

	});
};


// export const useAddFiatWallet = () => {
// 	const queryClient = useQueryClient();

// 	return useMutation({
// 		mutationFn: (payload: AddWalletPayload) =>
// 			FiatWalletService.addWallet(payload),
// 		onSuccess: () => {
			
// 		},
// 	});
// };


// export const useFiatAvailableCurrencies = () => {
// 	return useQuery({
// 		queryKey: [FIAT_WALLETS, "available-currencies"],
// 		queryFn: () => FiatWalletService.getAvailableCurrencies(),
// 	});
// };

export const useFiatAvailableCurrencyCodes = () => {
	return useQuery({
		queryKey: [FIAT_WALLETS, "available-currencies"],
		queryFn: () => FiatWalletService.getAvailableCurrencies(),
		// select: (data) => data.currencies.map((c: any) => c.currency),
	});
	
};

export const useAddFiatWallet = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (currency: string) => FiatWalletService.addWallet(currency),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [FIAT_WALLETS] });
		},
	});
};