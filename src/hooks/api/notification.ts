import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";
import { ResponseStatus } from "@/config/enums";
import { notify } from "@/utils/notify";
import { NotificationService } from "@/services/notification";
import { NotificationSettings } from "@/types/notification";
export const USER = "user";


export const useNotificationSettings = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: NotificationSettings) =>
			NotificationService.notificationSettings(payload),
		onSuccess: () => {
			notify("Notification Updated!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [USER] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to update notification";
			notify(errorMessage, ResponseStatus.ERROR);
		}
	});
};