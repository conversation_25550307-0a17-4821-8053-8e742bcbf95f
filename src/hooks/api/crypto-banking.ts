import {  useMutation, useQuery } from "@tanstack/react-query";
import { CryptoBankingService } from "@/services/crypto-banking";
import { AvailableCurrenciesResponse, ICryptoDepositRequest, ICryptoUniversalNetworkData, ICryptoWithdrawalRequest } from "@/types/crypto-banking";
import { ICryptoWallet } from "@/types/crypto-wallets";

export const CRYPTO_BANKING = "crypto-banking";


export const useMakeCryptoWithdrawal = () => {
	// const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: ICryptoWithdrawalRequest) =>
			CryptoBankingService.send(payload),
		// onSuccess: () => {
			
		// 	queryClient.invalidateQueries({
		// 		queryKey: [CRYPTO_BANKING, "transactions"],
		// 	});
		// },
	});
};


export const useCreateCryptoDeposit = () => {
	return useMutation({
		mutationFn: (payload: ICryptoDepositRequest) =>
			CryptoBankingService.createDynamicDeposit(payload),
		
	});
};


export const useGetCoinWithdrawalMethod = (
	coin: string,
) => {
	return useQuery({
		queryKey: [CRYPTO_BANKING, "withdrawal-methods", coin],
		queryFn: () =>
			CryptoBankingService.getCurrencyWithdrawalMethods(coin),
        select: (data) => data?.options,
        enabled: !!coin
	});
};


// export const useUniversalWithdrawalNetworks = (
// 	payload: UniversalNetworksPayload,
// ) => {
// 	return useQuery({
// 		queryKey: [CRYPTO_BANKING, "withdrawal-networks", payload.currency],
// 		queryFn: () =>
// 			CryptoBankingService.getUniversalWithdrawalNetworks(payload),
// 	});
// };


export const useGetCryptoUniversalDepositMethods = (
	coin: ICryptoWallet["currency"],
) => {
	return useQuery({
		queryKey: [CRYPTO_BANKING, "universal-deposit", coin],
		enabled: !!coin,
		queryFn: () =>
			CryptoBankingService.getUniversalDepositNetworks(coin),
		select: (data) => data?.options as ICryptoUniversalNetworkData[],
	});
};


// export const useCoinNetworks = () => {
// 	return useQuery({
// 		queryKey: [CRYPTO_BANKING, "coin-networks"],
// 		queryFn: () => CryptoBankingService.getCoinNetworks(),
// 	});
// };


export const useGetReceiveCryptoMethods = (currency: string) => {
	return useQuery({
		queryKey: [CRYPTO_BANKING, "deposit-methods", currency],
		queryFn: () => CryptoBankingService.getCurrencyDepositMethods(currency),
        select: (data) => data.options,
        enabled: !!currency,
	});
};


// export const useValidateWalletAddress = () => {
// 	return useMutation({
// 		mutationFn: (payload: ValidateAddressPayload) =>
// 			CryptoBankingService.validateWalletAddress(payload),
// 		onError: (error) => {
// 			const errorMessage =
// 				error?.message || "Failed to validate wallet address";
			
// 		},
// 	});
// };


// export const useCryptoTransactions = (payload: TransactionsListPayload) => {
// 	return useQuery({
// 		queryKey: [CRYPTO_BANKING, "transactions", payload],
// 		queryFn: () => CryptoBankingService.getTransactions(payload),
// 	});
// };


// export const useCryptoTransaction = (payload: TransactionDetailsPayload) => {
// 	return useQuery({
// 		queryKey: [CRYPTO_BANKING, "transaction", payload.transactionId],
// 		queryFn: () => CryptoBankingService.getTransaction(payload),
// 	});
// };


// export const useBeneficiariesList = () => {
// 	return useQuery({
// 		queryKey: [CRYPTO_BANKING, "beneficiaries"],
// 		queryFn: () => CryptoBankingService.getBeneficiariesList(),
// 	});
// };


// export const useFiatBeneficiary = (payload: FiatBeneficiaryPayload) => {
// 	return useQuery({
// 		queryKey: [CRYPTO_BANKING, "fiat-beneficiary", payload.beneficiaryId],
// 		queryFn: () => CryptoBankingService.getFiatBeneficiary(payload),
// 	});
// };


export const useAvailableCurrencies = () => {
  return useQuery({
    queryKey: [CRYPTO_BANKING, "available-currencies"],
    queryFn: () => CryptoBankingService.getAvailableCurrencies(),
    select: (data: AvailableCurrenciesResponse) =>
      data.available_currencies.map(c => c.currency),
  });
};
