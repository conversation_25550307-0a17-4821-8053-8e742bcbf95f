import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { notify} from "@/utils/notify"; 
import { ResponseStatus } from "@/config/enums";

import { CardsService } from "@/services/card";
import { CreateCardPayload,FundCardPayload,WithdrawCardPayload,FreezeUnfreezeCardPayload, ICard } from "@/types/cards";

export const CARDS = "cards";


export const useCreateCard = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: CreateCardPayload) =>
			CardsService.createCard(payload),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [CARDS] });
		},
	});
};


export const useFundCard = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: FundCardPayload) =>
			CardsService.fundCard(payload),
		onSuccess: () => {
			notify("Card funded successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CARDS] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to fund card";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};


export const useWithdrawFromCard = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: WithdrawCardPayload) =>
			CardsService.withdrawFromCard(payload),
		onSuccess: () => {
			notify("Withdrawal successful!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CARDS] });
		},
		onError: (error) => {
			const errorMessage =
				error?.message || "Failed to withdraw from card";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};


export const useTerminateCard = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (CardID: string) =>
			CardsService.terminateCard(CardID),
		onSuccess: () => {
			notify("Card terminated successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CARDS] });
		},
		onError: (error) => {
			const errorMessage = error?.message || "Failed to terminate card";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};


export const useFreezeOrUnfreezeCard = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (payload: FreezeUnfreezeCardPayload) =>
			CardsService.freezeOrUnfreezeCard(payload),
		onSuccess: () => {
			notify("Card status updated successfully!", ResponseStatus.SUCCESS);
			queryClient.invalidateQueries({ queryKey: [CARDS] });
		},
		onError: (error) => {
			const errorMessage =
				error?.message || "Failed to update card status";
			notify(errorMessage, ResponseStatus.ERROR);
		},
	});
};


export const useGetUserCards = () => {
	return useQuery({
        retry: 2,
		queryKey: [CARDS],
		queryFn: () => CardsService.getUserCardsList(),
        select: (data) => data?.data as ICard[],
	});
};


// export const useCardDetails = (payload: CardDetailsPayload) => {
// 	return useQuery({
// 		queryKey: [CARDS, payload],
// 		queryFn: () => CardsService.getCardDetails(payload),
// 	});
// };



export const useGetCardTransactions = (cardID:string)=>{
	return useQuery({
		retry:2,
		queryKey:[CARDS],
		queryFn:()=>CardsService.getCardTransactions(cardID)
	})
}
 