// import {useMutation,useQuery,useQueryClient} from "@tanstack/react-query";
// import {notify} from "@/utils/notify";
// import { SavingsService} from "@/services/savings"
// import {useNavigate} from "react-router-dom";
// import { ResponseStatus } from "@/config/enums";
 
// export const SAVINGS_KEYS ={
//     all:['savings'] as const,
//     goals:()=>["savings","goals"] as const,
//     goal:(id:string)=>["savings","goals",id] as const,
// };

// export const useCreateSavingsGoal = ()=>{
//     const queryClient = useQueryClient();
//     const navigate = useNavigate();

//     return useMutation({
//         mutationFn:SavingsService.createSavingsGoal,
//         onSuccess:(response)=>{
//             queryClient.invalidateQueries({queryKey:SAVINGS_KEYS.goals()});
//             console.error(response.data)
//             navigate(`/savings/success/${response.data.id}`);
//         },
//         onError:(error:any)=>{
//             notify(error.message || "Failed to create savings goal", ResponseStatus.ERROR);
//         }
//     })
// };
// export const useSavingGoal = (goalId:string)=>{
//     return useQuery({
//         queryKey:SAVINGS_KEYS.goal(goalId),
//         queryFn:()=>SavingsService.getSavingGoal(goalId),
//         enabled:!!goalId,

//         })
 
// }