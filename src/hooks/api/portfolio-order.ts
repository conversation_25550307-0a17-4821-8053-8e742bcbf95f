import { useState, useEffect } from "react";
import { ICryptoWallet } from "@/types/crypto-wallets";

// 🔧 Custom hook for persistent portfolio order
export const usePortfolioOrder = (cryptoAssets: ICryptoWallet[], userId: string) => {
  const [items, setItems] = useState<string[]>([]);
  const STORAGE_KEY = `portfolio_order_${userId}`;

  // Load saved order from localStorage on mount
  useEffect(() => {
    const savedOrder = localStorage.getItem(STORAGE_KEY);
    const cryptoIds = cryptoAssets.map(c => c.id);
    
    if (savedOrder && cryptoIds.length > 0) {
      try {
        const parsedOrder = JSON.parse(savedOrder);
        // Filter out any IDs that no longer exist in the crypto assets
        const validOrder = parsedOrder.filter((id: string) => cryptoIds.includes(id));
        // Add any new IDs that aren't in the saved order
        const newIds = cryptoIds.filter(id => !parsedOrder.includes(id));
        const finalOrder = [...validOrder, ...newIds];
        setItems(finalOrder);
      } catch (error) {
        console.error('Error parsing saved portfolio order:', error);
        setItems(cryptoIds);
      }
    } else if (cryptoIds.length > 0) {
      setItems(cryptoIds);
    }
  }, [cryptoAssets, STORAGE_KEY]);

  // Save order to localStorage whenever it changes
  const updateOrder = (newOrder: string[]) => {
    setItems(newOrder);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newOrder));
  };

  return { items, updateOrder };
};