// // import { useState, useEffect } from "react";
// // import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
// // import { APP_CONFIG } from "@/config/index";
// // import { ICryptoWallet } from "@/types/crypto-wallets";

// // export interface CryptoTransaction {
// //     	user_id: string;
// // 	transaction_id: string;
// //     id: number;
// //     type: string;
// //     time: string;
// //     date: string;
// //     account: string;
// //     amount: string;
// //     status: "Successful" | "Failed" | "Processing" | "Pending";
// // }

// // export interface TransactionWithTimestamp extends CryptoTransaction {
// //     timestamp: number;
// // }

// // const formatStatus = (apiStatus: string): CryptoTransaction["status"] => {
// //     switch (apiStatus.toUpperCase()) {
// //         case "CREATED":
// //             return "Pending";
// //         case "SUCCESSFUL":
// //         case "COMPLETED":
// //             return "Successful";
// //         case "FAILED":
// //             return "Failed";
// //         case "PROCESSING":
// //             return "Processing";
// //         default:
// //             return "Pending";
// //     }
// // };

// // export function useCryptoTransactions() {
// //     const [transactions, setTransactions] = useState<CryptoTransaction[]>([]);
// //     const [loading, setLoading] = useState(true);
// //     const [error, setError] = useState<string | null>(null);

// //     const {
// //         data: user_wallets,
// //         isLoading: user_walletsLoading,
// //         isError,
// //         error: user_walletsError,
// //     } = useCryptoUserWallets();

// //     useEffect(() => {
// //         if (!user_wallets || user_wallets.length === 0) return;

// //         const fetchTransactions = async () => {
// //             setLoading(true);
// //             setError(null);

// //             try {
// //                 const token = localStorage.getItem("auth_token");
// //                 if (!token) throw new Error("User not authenticated");

// //                 const allTransactions: TransactionWithTimestamp[] = [];

// //                 const uniqueWallets = new Map<string, ICryptoWallet>();
// //                 user_wallets.forEach(wallet => {
// //                     // Key ensures one fetch per (user_id + currency)
// //                     const key = `${wallet.user_id}-${wallet.currency}`;
// //                     if (!uniqueWallets.has(key)) {
// //                         uniqueWallets.set(key, wallet);
// //                     }
// //                 });

// //                 for (const [, wallet] of uniqueWallets) {
// //                     const url = new URL(`${APP_CONFIG.API_URLS.CURRENCY}/crypto-banking/get-transactions`);
// //                     url.searchParams.append("user_id", wallet.user_id);
// //                     url.searchParams.append("currency", wallet.currency);
// //                     url.searchParams.append("limit", "10");
// //                     url.searchParams.append("page", "1");
// //                     url.searchParams.append("skip", "0");
// //                     url.searchParams.append("sort", "DESC");
// //                     url.searchParams.append("startDate", "2025-01-01");
// //                     url.searchParams.append("endDate", "2025-06-01");

// //                     const response = await fetch(url.toString(), {
// //                         headers: {
// //                             Authorization: `Bearer ${token}`,
// //                             "Content-Type": "application/json",
// //                         },
// //                     });

// //                     if (!response.ok) {
// //                         console.warn(`Failed to fetch transactions for ${wallet.user_id} (${wallet.currency})`);
// //                         continue;
// //                     }

// //                     const data = await response.json();
// //                     const transactionsArray = data.data || [];

// //                     const formatted: TransactionWithTimestamp[] = transactionsArray.map((item: any, index: number) => {
// //                         const fullDate = new Date(item.date);

// //                         return {
// //                             id: Date.now() + index + Math.random(),
// //                             type: item.method || "Unknown",
// //                             time: fullDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
// //                             date: fullDate.toLocaleDateString(),
// //                             account: item.account_name || "Unknown Account",
// //                             amount: `${item.currency || "NGN"} ${Number(item.amount).toLocaleString()}`,
// //                             status: formatStatus(item.status),
// //                             timestamp: fullDate.getTime(),
// //                         };
// //                     });

// //                     allTransactions.push(...formatted);
// //                 }

// //                 allTransactions.sort((a, b) => b.timestamp - a.timestamp);
// //                 const cleaned: CryptoTransaction[] = allTransactions.map(({ timestamp, ...rest }) => rest);

// //                 setTransactions(cleaned);
// //             } catch (err: any) {
// //                 console.error("Transaction fetch error:", err);
// //                 setError(err.message || "Failed to fetch transactions.");
// //             } finally {
// //                 setLoading(false);
// //             }
// //         };

// //         fetchTransactions();
// //     }, [user_wallets]);

// //     return {
// //         transactions,
// //         loading: loading || user_walletsLoading,
// //         error: error || (isError ? (user_walletsError as Error)?.message : null),
// //     };
// // }


// import { useState, useEffect } from "react";
// import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
// import { APP_CONFIG } from "@/config/index";
// import { ICryptoWallet } from "@/types/crypto-wallets";

// export interface CryptoTransaction {
//     user_id: string;
//     transaction_id: string;
//     id: number;
//     type: string;
//     time: string;
//     date: string;
//     account: string;
//     amount: string;
//     status: "Successful" | "Failed" | "Processing" | "Pending";
// }

// export interface TransactionWithTimestamp extends CryptoTransaction {
//     timestamp: number;
// }

// const formatStatus = (apiStatus: string): CryptoTransaction["status"] => {
//     switch (apiStatus.toUpperCase()) {
//         case "CREATED":
//             return "Pending";
//         case "SUCCESSFUL":
//         case "COMPLETED":
//             return "Successful";
//         case "FAILED":
//             return "Failed";
//         case "PROCESSING":
//             return "Processing";
//         default:
//             return "Pending";
//     }
// };

// // Modified hook to accept optional currency filter
// export function useCryptoTransactions(filterCurrency?: string) {
//     const [transactions, setTransactions] = useState<CryptoTransaction[]>([]);
//     const [loading, setLoading] = useState(true);
//     const [error, setError] = useState<string | null>(null);

//     const {
//         data: user_wallets,
//         isLoading: user_walletsLoading,
//         isError,
//         error: user_walletsError,
//     } = useCryptoUserWallets();

//     useEffect(() => {
//         if (!user_wallets || user_wallets.length === 0) return;

//         const fetchTransactions = async () => {
//             setLoading(true);
//             setError(null);

//             try {
//                 const token = localStorage.getItem("auth_token");
//                 if (!token) throw new Error("User not authenticated");

//                 const allTransactions: TransactionWithTimestamp[] = [];

//                 // Filter wallets by currency if provided
//                 const walletsToFetch = filterCurrency 
//                     ? user_wallets.filter(wallet => 
//                         wallet.currency.toLowerCase() === filterCurrency.toLowerCase()
//                       )
//                     : user_wallets;

//                 const uniqueWallets = new Map<string, ICryptoWallet>();
//                 walletsToFetch.forEach(wallet => {
//                     // Key ensures one fetch per (user_id + currency)
//                     const key = `${wallet.user_id}-${wallet.currency}`;
//                     if (!uniqueWallets.has(key)) {
//                         uniqueWallets.set(key, wallet);
//                     }
//                 });

//                 for (const [, wallet] of uniqueWallets) {
//                     const url = new URL(`${APP_CONFIG.API_URLS.CURRENCY}/crypto-banking/get-transactions`);
//                     url.searchParams.append("user_id", wallet.user_id);
//                     url.searchParams.append("currency", wallet.currency);
//                     url.searchParams.append("limit", "10");
//                     url.searchParams.append("page", "1");
//                     url.searchParams.append("skip", "0");
//                     url.searchParams.append("sort", "DESC");
//                     url.searchParams.append("startDate", "2025-01-01");
//                     url.searchParams.append("endDate", "2025-06-01");

//                     const response = await fetch(url.toString(), {
//                         headers: {
//                             Authorization: `Bearer ${token}`,
//                             "Content-Type": "application/json",
//                         },
//                     });

//                     if (!response.ok) {
//                         console.warn(`Failed to fetch transactions for ${wallet.user_id} (${wallet.currency})`);
//                         continue;
//                     }

//                     const data = await response.json();
//                     const transactionsArray = data.data || [];

//                     const formatted: TransactionWithTimestamp[] = transactionsArray.map((item: any, index: number) => {
//                         const fullDate = new Date(item.date);

//                         return {
//                             id: Date.now() + index + Math.random(),
//                             type: item.method || "Unknown",
//                             time: fullDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
//                             date: fullDate.toLocaleDateString(),
//                             account: item.account_name || "Unknown Account",
//                             amount: `${item.currency || "NGN"} ${Number(item.amount).toLocaleString()}`,
//                             status: formatStatus(item.status),
//                             timestamp: fullDate.getTime(),
//                             user_id: wallet.user_id,
//                             transaction_id: item.transaction_id || `tx_${Date.now()}_${index}`,
//                         };
//                     });

//                     allTransactions.push(...formatted);
//                 }

//                 allTransactions.sort((a, b) => b.timestamp - a.timestamp);
//                 const cleaned: CryptoTransaction[] = allTransactions.map(({ timestamp, ...rest }) => rest);

//                 setTransactions(cleaned);
//             } catch (err: any) {
//                 console.error("Transaction fetch error:", err);
//                 setError(err.message || "Failed to fetch transactions.");
//             } finally {
//                 setLoading(false);
//             }
//         };

//         fetchTransactions();
//     }, [user_wallets, filterCurrency]); // Added filterCurrency to dependency array

//     return {
//         transactions,
//         loading: loading || user_walletsLoading,
//         error: error || (isError ? (user_walletsError as Error)?.message : null),
//     };
// }

import { useState, useEffect, useRef } from "react";
import { useCryptoUserWallets } from "@/hooks/api/crypto-wallets";
import { APP_CONFIG } from "@/config/index";
import { ICryptoWallet } from "@/types/crypto-wallets";

export interface CryptoTransaction {
    user_id: string;
    transaction_id: string;
    id: number;
    type: string;
    time: string;
    date: string;
    account: string;
    amount: string;
    status: "Successful" | "Failed" | "Processing" | "Pending";
}

export interface TransactionWithTimestamp extends CryptoTransaction {
    timestamp: number;
}

const formatStatus = (apiStatus: string): CryptoTransaction["status"] => {
    switch (apiStatus.toUpperCase()) {
        case "CREATED":
            return "Pending";
        case "SUCCESSFUL":
        case "COMPLETED":
            return "Successful";
        case "FAILED":
            return "Failed";
        case "PROCESSING":
            return "Processing";
        default:
            return "Pending";
    }
};

export function useCryptoTransactions(filterCurrency?: string) {
    const [transactions, setTransactions] = useState<CryptoTransaction[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    
    // SOLUTION 1: Better request deduplication
    const requestInProgress = useRef(false);
    const requestId = useRef(0);

    const {
        data: user_wallets,
        isLoading: user_walletsLoading,
        isError,
        error: user_walletsError,
    } = useCryptoUserWallets();

    useEffect(() => {
        if (!user_wallets || user_wallets.length === 0) {
            setLoading(false);
            return;
        }

        // SOLUTION 2: Increment request ID to handle race conditions
        const currentRequestId = ++requestId.current;

        if (requestInProgress.current) {
            // console.log('Request already in progress, skipping...');
            return;
        }

        let isCancelled = false;

        const fetchTransactions = async () => {
            if (isCancelled || requestInProgress.current) return;
            
            requestInProgress.current = true;
            setLoading(true);
            setError(null);

            try {
                const token = localStorage.getItem("auth_token");
                if (!token) throw new Error("User not authenticated");

                const allTransactions: TransactionWithTimestamp[] = [];
                
                // SOLUTION 3: Track successful vs failed requests
                let successfulRequests = 0;
                let totalRequests = 0;

                // Filter wallets by currency if provided
                const walletsToFetch = filterCurrency 
                    ? user_wallets.filter(wallet => 
                        wallet.currency.toLowerCase() === filterCurrency.toLowerCase()
                      )
                    : user_wallets;

                // Get unique wallets to avoid duplicate API calls
                const uniqueWallets = new Map<string, ICryptoWallet>();
                walletsToFetch.forEach(wallet => {
                    const key = `${wallet.user_id}-${wallet.currency}`;
                    if (!uniqueWallets.has(key)) {
                        uniqueWallets.set(key, wallet);
                    }
                });

                totalRequests = uniqueWallets.size;

                for (const [, wallet] of uniqueWallets) {
                    if (isCancelled || requestId.current !== currentRequestId) break;
                    
                    try {
                        // Build URL with proper parameters
                        const baseUrl = `${APP_CONFIG.API_URLS.CURRENCY}/crypto-banking/get-transactions`;
                        const url = new URL(baseUrl);
                        
                        // Validate required parameters
                        if (!wallet.user_id || !wallet.currency) {
                            console.warn('Missing required wallet data:', wallet);
                            continue;
                        }

                        url.searchParams.append("user_id", wallet.user_id.toString());
                        url.searchParams.append("currency", wallet.currency.toString().toUpperCase());
                        
                        // Use YYYY-MM-DD format consistently
                        const endDate = new Date();
                        const startDate = new Date();
                        startDate.setMonth(startDate.getMonth() - 1);
                        
                        const formatDate = (date: Date) => {
                            return date.toISOString().split('T')[0];
                        };
                        
                        url.searchParams.append("startDate", formatDate(startDate));
                        url.searchParams.append("endDate", formatDate(endDate));
                        url.searchParams.append("limit", "20");
                        url.searchParams.append("page", "1");

                        // console.log('Fetching transactions from:', url.toString());

                        const response = await fetch(url.toString(), {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                        });

                        if (!response.ok) {
                            let errorMessage = `HTTP ${response.status}`;
                            try {
                                const errorData = await response.json();
                                errorMessage = errorData.message || errorData.error || errorMessage;
                                console.error(`API Error for ${wallet.currency}:`, errorData);
                            } catch {
                                const errorText = await response.text();
                                console.error(`API Error for ${wallet.currency}:`, errorText);
                                errorMessage = errorText;
                            }
                            
                            console.warn(`Failed to fetch transactions for ${wallet.user_id} (${wallet.currency}):`, {
                                status: response.status,
                                error: errorMessage
                            });
                            
                            // SOLUTION 4: Don't continue, just skip this wallet but preserve others
                            continue;
                        }

                        const data = await response.json();
                        // console.log(`API Response for ${wallet.currency}:`, data);
                        
                        // Handle different response structures
                        const transactionsArray = data.transactions || data.data || data.result || [];
                        
                        if (!Array.isArray(transactionsArray)) {
                            console.warn('Invalid transactions data format for', wallet.currency, ':', data);
                            continue;
                        }

                        successfulRequests++;

                        if (transactionsArray.length === 0) {
                            // console.log(`No transactions found for ${wallet.currency}`);
                            continue;
                        }

                        const formatted: TransactionWithTimestamp[] = transactionsArray.map((item: any, index: number) => {
                            let fullDate: Date;
                            const dateValue = item.date || item.created_at || item.timestamp || item.transaction_date;
                            
                            if (dateValue) {
                                fullDate = new Date(dateValue);
                                if (isNaN(fullDate.getTime())) {
                                    fullDate = new Date();
                                }
                            } else {
                                fullDate = new Date();
                            }

                            return {
                                id: Date.now() + index + Math.random(),
                                type: item.method || item.type || item.transaction_type || "Transfer",
                                time: fullDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
                                date: fullDate.toLocaleDateString(),
                                account: item.account_name || item.account || item.wallet_address || "Crypto Wallet",
                                amount: `${item.currency || wallet.currency || "NGN"} ${Number(item.amount || 0).toLocaleString()}`,
                                status: formatStatus(item.status || "PENDING"),
                                timestamp: fullDate.getTime(),
                                user_id: wallet.user_id,
                                transaction_id: item.transaction_id || item.id || item.hash || `tx_${Date.now()}_${index}`,
                            };
                        });

                        allTransactions.push(...formatted);
                        // console.log(`Added ${formatted.length} transactions for ${wallet.currency}`);

                    } catch (walletError: any) {
                        console.error(`Error processing wallet ${wallet.user_id} (${wallet.currency}):`, walletError);
                        continue;
                    }
                }

                // SOLUTION 5: Only update state if this is still the current request
                if (isCancelled || requestId.current !== currentRequestId) return;

                // Sort by timestamp (newest first)
                allTransactions.sort((a, b) => b.timestamp - a.timestamp);
                
                // Remove timestamp property for final result
                const cleaned: CryptoTransaction[] = allTransactions.map(({ timestamp, ...rest }) => rest);

                // console.log(`Total transactions fetched: ${cleaned.length} (${successfulRequests}/${totalRequests} wallets succeeded)`);
                
                // SOLUTION 6: Always set transactions, even if empty (but log why)
                if (cleaned.length === 0 && successfulRequests === 0) {
                    console.warn('No transactions found - all API requests failed');
                    setError('Failed to fetch transactions from all wallets');
                } else if (cleaned.length === 0 && successfulRequests > 0) {
                    // console.log('No transactions found - API requests succeeded but returned empty results');
                }
                
                setTransactions(cleaned);
                
            } catch (err: any) {
                console.error("Transaction fetch error:", err);
                if (!isCancelled && requestId.current === currentRequestId) {
                    setError(err.message || "Failed to fetch transactions.");
                }
            } finally {
                requestInProgress.current = false;
                if (!isCancelled && requestId.current === currentRequestId) {
                    setLoading(false);
                }
            }
        };

        fetchTransactions();

        return () => {
            isCancelled = true;
            requestInProgress.current = false;
        };
    }, [user_wallets, filterCurrency]);

    return {
        transactions,
        loading: loading || user_walletsLoading,
        error: error || (isError ? (user_walletsError as Error)?.message : null),
    };
}