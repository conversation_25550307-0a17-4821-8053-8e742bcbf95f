import { useMutation } from "@tanstack/react-query";
import AuthService from "@/services/auth";
import {
	ActivateUserPayload,
	ChangePasswordPayload,
	ConfirmCodePayload,
	CreateUserPayload,
	ForgetPasswordPayload,
	LoginPayload,
	ResendOTPPayload,
	RetrievePasswordEmailPayload,
} from "@/types/auth";

export const AUTH = "auth";
export const USER = "user";

export const useRegisterUser = () => {
	return useMutation({
		mutationFn: (createUserPayload: CreateUserPayload) =>
			AuthService.registerUser(createUserPayload),
	});
};

export const useActivateUser = () => {
	return useMutation({
		mutationFn: (payload: ActivateUserPayload) =>
			AuthService.activateUser(payload),
	});
};

export const useResendOTP = () => {
	return useMutation({
		mutationFn: (payload: ResendOTPPayload) =>
			AuthService.resendOTP(payload),
	});
};

export const useRetrievePasswordEmail = () => {
	return useMutation({
		mutationFn: (payload: RetrievePasswordEmailPayload) =>
			AuthService.retrievePasswordEmail(payload),
	});
};

export const useForgetPassword = () => {
	return useMutation({
		mutationFn: (payload: ForgetPasswordPayload) =>
			AuthService.forgetPassword(payload),
	});
};

export const useConfirmCode = () => {
	return useMutation({
		mutationFn: (payload: ConfirmCodePayload) =>
			AuthService.confirmCodeForForgotPassword(payload),
	});
};

export const useLogin = () => {
	return useMutation({
		mutationFn: (payload: LoginPayload) => AuthService.login(payload),
	});
};

export const useChangePassword = () => {
	return useMutation({
		mutationFn: (payload: ChangePasswordPayload) =>
			AuthService.changePassword(payload),
	});
};

// export const useGoogleSSO = () => {
// 	return useMutation({
// 		mutationFn: (token: string) => AuthService.googleSSO(token),

// 	});
// };

// export const useAppleSSO = () => {
// 	return useMutation({
// 		mutationFn: (token: string) => AuthService.appleSSO(token),

// 	});
// };
