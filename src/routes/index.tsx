import ComingSoon from "@/components/coming-soon";
import ProtectedDashboardLayout from "@/components/protected-dashboard-route";

import AuthLayout from "@/layouts/auth-layout";
import GlobalLayout from "@/layouts/global-layout";

import ConfirmEmailPage from "@/pages/auth/confirm-email";
import CreateNewPasswordPage from "@/pages/auth/create-new-password";
import ForgotPasswordPage from "@/pages/auth/forgot-password";
import LoginPage from "@/pages/auth/login";
import RegisterPage from "@/pages/auth/register";
import VerifyEmailPage from "@/pages/auth/verify-email";
import DrawerPage from "@/pages/dashbaord/drawer";
import ClyphubPage from "@/pages/dashbaord/clyphub";
import Home from "@/pages/dashbaord/home";
import NotFoundPage from "@/pages/not-found";
import { createBrowserRouter } from "react-router";

import Lifestyle from "@/pages/dashbaord/actions/lifestyle/lifestyle";
import Airtime from "@/pages/dashbaord/actions/lifestyle/airtime";
import AutoSaveDetails from "@/pages/savings/auto-save/auto-save-details";
import MyPortfolio from "@/shared/home/<USER>";
import AutoSave from "@/pages/savings/auto-save";
import Loans from "@/pages/dashbaord/actions/loans";
import RequestCryptoLoan from "@/pages/dashbaord/actions/request-crypto-loan";
import ReportsPage from "@/pages/dashbaord/reports/reports-page";

import FiatTransactions from "@/shared/home/<USER>";
import TransactionDetail from "@/shared/home/<USER>";
import Data from "@/pages/dashbaord/actions/lifestyle/data";
import Electricity from "@/pages/dashbaord/actions/lifestyle/electricity";
import CryptoTransactions from "@/shared/home/<USER>";

import SafeLock from "@/pages/savings/safe-lock";
import { SafeLockDetails } from "@/pages/savings/safe-lock/safe-lock-details";
import OnboardingFlow from "@/pages/onboarding/index.tsx";
import ChatbotPage from "@/pages/chatbot";
import Notifications from "@/pages/notifications";

import ProfileLanding from "@/pages/dashbaord/profile/profile-landing"
import { EditProfile } from "@/pages/dashbaord/profile/views/edit-profile/edit-profile";
import NotificationSettings from "@/pages/dashbaord/profile/views/notifications";
import { ChangeTransactionPin } from "@/pages/dashbaord/profile/views/security/change-transaction-pin";
import { Security } from "@/pages/dashbaord/profile/views/security/security";
import { Support } from "@/pages/dashbaord/profile/views/support/support";
import Faq from "@/pages/dashbaord/profile/views/support/faq";
import { ReportIssue } from "@/pages/dashbaord/profile/views/support/report-issue";
import { SubArticles } from "@/pages/dashbaord/profile/views/support/articles/sub-articles";
import { ChangeName } from "@/pages/dashbaord/profile/views/support/change-name";
import { AccountSettings } from "@/pages/dashbaord/profile/views/account-settings/acccount-settings";
import { AccountStatement } from "@/pages/dashbaord/profile/views/account-settings/statement-of-account";
import { BeneficiaryList } from "@/pages/dashbaord/profile/views/account-settings/beneficiary-list";
import { AccountLimit } from "@/pages/dashbaord/profile/views/account-settings/account-limit";
import { InviteFriend } from "@/pages/dashbaord/profile/views/invite-friend";
import { PrivacyPolicy } from "@/pages/dashbaord/profile/views/privacy-policy";
import { ArticleDetail } from "@/pages/dashbaord/profile/views/support/articles/ArticleDetail";
import { Articles } from "@/pages/dashbaord/profile/views/support/articles/articles";
import { TermsAndConditions } from "@/pages/dashbaord/profile/views/terms-and-conditions";

const router = createBrowserRouter([
	{
		element: <GlobalLayout />,
		children: [
			{
				path: "/",
				element: <ProtectedDashboardLayout />,
				children: [
					{ index: true, element: <Home /> },

					{ path: "cards", element: <ComingSoon /> },
					{ path: "portfolio", element: <MyPortfolio userId={""} /> },
					{ path: "reports", element: <ReportsPage /> },
					{ path: "clyphub", element: <ClyphubPage /> },
					{ path: "lifestyle", element: <Lifestyle /> },
					{ path: "airtime", element: <Airtime /> },
					{ path: "electricity", element: <Electricity /> },
					{ path: "data", element: <Data /> },
					{ path: "loans", element: <Loans /> },
					{
						path: "request-crypto-loan",
						element: <RequestCryptoLoan />,
					},
					{ path: "notifications", element: <Notifications /> },
					{ path: "clypnews", element: <ComingSoon /> },

					{
						element: <ProfileLanding />,
						children: [
							{ path: "profile", element: <EditProfile /> },
							{ path: "account-settings", element: <AccountSettings /> },
							{ path: "account-settings/account-statement", element: <AccountStatement /> },
							{ path: "account-settings/account-limit", element: <AccountLimit /> },
							{ path: "account-settings/beneficiary-list", element: <BeneficiaryList /> },
							
							{ path: "security-settings", element: <Security /> },
							{ path: "security-settings/change-transaction-pin", element: <ChangeTransactionPin /> },

							{ path: "support", element: <Support /> },
							{ path: "support/faq", element: <Faq /> },
							{ path: "support/report-issue", element: <ReportIssue /> },

							{ path: "support/articles", element: <Articles /> },
							{ path: "support/articles/sub-articles", element: <SubArticles /> },
							{ path: "support/articles/sub-articles/:slug", element: <ArticleDetail /> },

							{ path: "support/change-account-name", element: <ChangeName /> },

							{ path: "notifications-settings", element: <NotificationSettings /> },
							{ path: "invite", element: <InviteFriend /> },
							{ path: "privacy-policy", element: <PrivacyPolicy /> },
							{ path: "terms-and-conditions", element: <TermsAndConditions /> },
						]
					},

					{ path: "drawer", element: <DrawerPage /> },
					{ path: "auto-save", element: <AutoSave /> },
					{ path: "details/:id", element: <AutoSaveDetails /> },
					{ path: "safe-lock", element: <SafeLock /> },
					{ path: "safe-details/:id", element: <SafeLockDetails /> },
					{
						path: "/chatbot",
						element: <ChatbotPage />,
					},
					{ path: "fiat-transactions", element: <FiatTransactions /> },
					{ path: "crypto-transactions", element: <CryptoTransactions /> },
					{ path: "transaction/:transactionId", element: <TransactionDetail transactionId={""} /> },


				],
			},
			{
				element: <AuthLayout />,
				children: [
					{ path: "login", element: <LoginPage /> },
					{ path: "register", element: <RegisterPage /> },
					{
						path: "forgot-password",
						element: <ForgotPasswordPage />,
					},
					{
						path: "confirm-email",
						element: <ConfirmEmailPage />,
					},
					{
						path: "email-verification",
						element: <VerifyEmailPage />,
					},
					{
						path: "create-new-password",
						element: <CreateNewPasswordPage />,
					},
				],
			},
			{
				path: "*",
				element: <NotFoundPage />,
			},
			{ path: "onboarding", element: <OnboardingFlow /> },
		],
	},
]);

export default router;
