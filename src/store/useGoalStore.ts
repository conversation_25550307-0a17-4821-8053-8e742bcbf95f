import { create } from "zustand";
import { SavingsGoal } from "@/types/fiat-savings"; // Removed SavingsDetails import
import { FiatSavingsService } from "@/services/fiat-savings";
import { CryptoSavingsService } from "@/services/crypto-savings";

type FundSavingsGoalStep = "amount" | "pin" | "success" | "receipt";

interface GoalState {
	createdGoal: SavingsGoal | null;
	setCreatedGoal: (goal: SavingsGoal) => void;
	clearGoal: () => void;
}
interface AllGoalsState {
	goals: SavingsGoal[]; 
	fiatGoals: SavingsGoal[]; 
	cryptoGoals: SavingsGoal[]; 
	isLoading: boolean;
	error: string | null;
	setGoals: (goals: SavingsGoal[]) => void; 
	fetchFiatGoals: () => Promise<void>;
	fetchCryptoGoals: () => Promise<void>;
	fetchAllGoals: () => Promise<void>; 
	clearGoals: () => void;
}

export const useAllGoalsStore = create<AllGoalsState>((set) => ({
	goals: [],
	fiatGoals: [],
	cryptoGoals: [],
	isLoading: false,
	error: null,
	setGoals: (goals: SavingsGoal[]) => set({ goals: goals, isLoading: false, error: null }),
	fetchFiatGoals: async () => {
		set({ isLoading: true, error: null });
		try {
			const fiatData = await FiatSavingsService.getSavingGoals();
			const validatedFiatData: SavingsGoal[] = Array.isArray(fiatData) ? fiatData : [];
			set((state) => ({
				fiatGoals: validatedFiatData,
				goals: [...validatedFiatData, ...state.cryptoGoals],
				isLoading: false,
			}));
		} catch (error) {
			set({ error: (error as Error).message || "Failed to fetch fiat goals", isLoading: false });
		}
	},
	fetchCryptoGoals: async () => {
		set({ isLoading: true, error: null });
		try {
			const cryptoData = await CryptoSavingsService.getSavingsGoals();
			const validatedCryptoData: SavingsGoal[] = Array.isArray(cryptoData) ? cryptoData : [];
			set((state) => ({
				cryptoGoals: validatedCryptoData,
				goals: [...state.fiatGoals, ...validatedCryptoData],
				isLoading: false,
			}));
		} catch (error) {
			set({ error: (error as Error).message || "Failed to fetch crypto goals", isLoading: false });
		}
	},
	fetchAllGoals: async () => {
		set({ isLoading: true, error: null });
		try {
			const [fiatServiceData, cryptoServiceData] = await Promise.all([
				FiatSavingsService.getSavingGoals(),
				CryptoSavingsService.getSavingsGoals(),
			]);
			const fiatGoalsArray: SavingsGoal[] = Array.isArray(fiatServiceData) ? fiatServiceData : [];
			const cryptoGoalArray: SavingsGoal[] = Array.isArray(cryptoServiceData) ? cryptoServiceData : [];

			set({
				fiatGoals: fiatGoalsArray,
				cryptoGoals: cryptoGoalArray,
				goals: [...fiatGoalsArray, ...cryptoGoalArray],
				isLoading: false,
			});
		} catch (error) {
			set({ error: (error as Error).message || "Failed to fetch goals", isLoading: false });
		}
	},
	clearGoals: () => set({ goals: [], fiatGoals: [], cryptoGoals: [], error: null, isLoading: false }),
}));

interface FundSavingsGoalState {
	currentStep: FundSavingsGoalStep;
	amount: number | null;
	fundingSource: "fiat" | "crypto" | null;
	selectedAccount: string | null;
	setStep: (step: FundSavingsGoalStep) => void;
	setAmount: (amount: number) => void;
	setFundingSource: (source: "fiat" | "crypto") => void;
	setSelectedAccount: (account: string) => void;
	reset: () => void;
}

interface AutoSaveGoalState {
	user_id: string;
	preferredTime: string;
	savePreference: string;
	setUserId:(userId:string)=>void
	setPreferredTime: (time: string) => void;
	setSavePreference: (preference: string) => void;
	reset: ()=>void;
}
export const useAutoSaveStore = create<AutoSaveGoalState>((set) => ({
    user_id: '', 
    preferredTime: '',
    savePreference: '',
		setUserId: (userId) => set({ user_id: userId }),
		setPreferredTime: (time) => set({ preferredTime: time }),
		setSavePreference: (preference) => set({ savePreference: preference }),
		reset: ()=> set({user_id: '', preferredTime: '', savePreference: ''})
}));

export const useFundSavingsGoalStore = create<FundSavingsGoalState>((set) => ({
	currentStep: "amount",
	amount: null,
	fundingSource: null,
	selectedAccount: null,
	setStep: (step) => set({ currentStep: step }),
	setAmount: (amount) => set({ amount }),
	setFundingSource: (source) => set({ fundingSource: source }),
	setSelectedAccount: (account) => set({ selectedAccount: account }),
	reset: () =>
		set({
			currentStep: "amount",
			amount: null,
			fundingSource: null,
			selectedAccount: null,
		}),
}));

export const useSavingsStore = create<GoalState>((set) => ({
	createdGoal: null,
	setCreatedGoal: (goal) => set({ createdGoal: goal }),
	clearGoal: () => set({ createdGoal: null }),
}));
