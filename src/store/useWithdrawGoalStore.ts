import { create } from "zustand";

type WithdrawSavingsGoalStep = "amount" | "pin" | "success" | "receipt";

interface WithdrawSavingsGoalState {
  currentStep: WithdrawSavingsGoalStep;
  amount: number | null;
  fundingSource: "fiat" | "crypto" | null;
  selectedAccount: string | null;
  setStep: (step: WithdrawSavingsGoalStep) => void;
  setAmount: (amount: number) => void;
  setFundingSource: (source: "fiat" | "crypto") => void;
  setSelectedAccount: (account: string) => void;
  reset: () => void;
}

export const useWithdrawSavingsGoalStore = create<WithdrawSavingsGoalState>((set) => ({
  currentStep: "amount",
  amount: null,
  fundingSource: null,
  selectedAccount: null,
  setStep: (step) => set({ currentStep: step }),
  setAmount: (amount) => set({ amount }),
  setFundingSource: (source) => set({ fundingSource: source }),
  setSelectedAccount: (account) => set({ selectedAccount: account }),
  reset: () => set({ 
    currentStep: "amount", 
    amount: null, 
    fundingSource: null, 
    selectedAccount: null 
  }),
}));
