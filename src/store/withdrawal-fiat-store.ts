import { create } from "zustand";
import { IFiatWithdrawalMethod } from "@/types/fiat-banking";
import { IFiatWallet } from "@/types/fiat-wallets";
import { IOption } from "@/types/general";

export type WithdrawalStep =
	| "select"
	| "internalTransfer"
	| "bankWithdrawal"
	| "previewWithdrawal"
	| "pin"
	| "success"
	| "receipt";

interface BaseWithdrawalPayload {
	coin: IFiatWallet;
	method: IFiatWithdrawalMethod;
	amount: string;
	note: string;
	feeInfo: { fee: number; amount: number };
}

interface InternalTransferPayload extends BaseWithdrawalPayload {
	type: "internal";
	identifier: string;
}

interface BankWithdrawalPayload extends BaseWithdrawalPayload {
	type: "bank";
	accountName: string;
	bankName: string;
	bankCode: string;
	accountNumber: string
}

type WithdrawalPayload = InternalTransferPayload | BankWithdrawalPayload;

type WithdrawFiatPayload = {
	payload: WithdrawalPayload;
};

interface WithdrawFiatState {
	currentStep: WithdrawalStep;
	selectedCoin: IFiatWallet | null;
	previousStep: WithdrawalStep | null;
	selectedWithdrawMethod: IOption<IFiatWithdrawalMethod> | null;
	withdrawFiatPayload: WithdrawFiatPayload | null;

	
	setCurrentStep: (step: WithdrawalStep, trackPrevious?: boolean) => void;
	setSelectedWithdrawMethod: (method: IOption<IFiatWithdrawalMethod>) => void;
	setSelectedCoin: (coin: IFiatWallet) => void;
	setWithdrawFiatPayload: (data: WithdrawFiatPayload) => void;
	reset: () => void;
}

const initialState = {
	currentStep: "select" as WithdrawalStep,
	previousStep: null as WithdrawalStep | null,
	selectedCoin: null,
	selectedWithdrawMethod: null,
	selectedNetwork: null,
	amount: "",
	withdrawFiatPayload: null,
};

const useWithdrawalFiatStore = create<WithdrawFiatState>((set) => ({
	...initialState,

	setCurrentStep: (step, trackPrevious = false) =>
		set((state) => ({
			currentStep: step,
			previousStep: trackPrevious
				? state.currentStep
				: state.previousStep,
		})),
	setSelectedWithdrawMethod: (method) =>
		set({ selectedWithdrawMethod: method }),
	setSelectedCoin: (coin) => set({ selectedCoin: coin }),
	setWithdrawFiatPayload: (data) => set({ withdrawFiatPayload: data }),
	reset: () => set(initialState),
}));

export default useWithdrawalFiatStore;
