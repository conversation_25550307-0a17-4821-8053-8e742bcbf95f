import { create } from "zustand";
import { IFiatWallet } from "@/types/fiat-wallets";
import { ISwapPriceData } from "@/types/swaps";


export type ConvertStep = "select" | "preview" | "pin" | "success" | "receipt";


export interface ConvertFiatTransaction {
	from: IFiatWallet;
	to: IFiatWallet;
	amount: number;
	method: string;
	price_data: ISwapPriceData;
}


interface ConvertFiatState {
	currentStep: ConvertStep;
	transaction: ConvertFiatTransaction | null;
	setCurrentStep: (step: ConvertStep) => void;
	setTransaction: (transaction: ConvertFiatTransaction) => void;
	reset: () => void;
}


const useConvertFiatTransactionStore = create<ConvertFiatState>((set) => ({
	currentStep: "select",
	transaction: null,

	setCurrentStep: (step) => set({ currentStep: step }),

	setTransaction: (transaction) => set({ transaction }),

	reset: () =>
		set({
			currentStep: "select",
			transaction: null,
		}),
}));

export default useConvertFiatTransactionStore;
