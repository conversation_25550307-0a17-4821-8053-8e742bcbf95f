import { ICryptoWithdrawalMethod } from "@/types/crypto-banking";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { IClypUser } from "@/types/user";
import { create } from "zustand";

export type SendCryptoStep =
	| "select"
	| "preview"
	| "pin"
	| "success"
	| "receipt";

interface SendCryptoState {
	currentStep: SendCryptoStep;
	transaction: ISendCryptoTransaction | null;
	isLoading: boolean;
	error: string | null;
}
interface IFeeInfo {
	fee: number;
	amount: number;
	
}

export interface ISendCryptoTransaction {
	from: ICryptoWallet;
	method: ICryptoWithdrawalMethod;
	to_address: string;
	amount: string;
	description: string;
	network: string;
	feeInfo: IFeeInfo;
	userData?: IClypUser;
    type? : "internal";
	note?: string;
}

interface SendCryptoActions {
	setCurrentStep: (step: SendCryptoStep) => void;
	setTransaction: (transaction: ISendCryptoTransaction) => void;
	updateTransaction: (updates: Partial<ISendCryptoTransaction>) => void;
	setIsLoading: (isLoading: boolean) => void;
	setError: (error: string | null) => void;
	reset: () => void;
}

const initialState: SendCryptoState = {
	currentStep: "select",
	transaction: null,
	isLoading: false,
	error: null,
};

const useSendCryptoStore = create<SendCryptoState & SendCryptoActions>(
	(set) => ({
		...initialState,

		setCurrentStep: (step) => set({ currentStep: step }),

		setTransaction: (transaction) => set({ transaction }),

		updateTransaction: (updates) =>
			set((state) => ({
				transaction: state.transaction
					? { ...state.transaction, ...updates }
					: null,
			})),

		setIsLoading: (isLoading) => set({ isLoading }),

		setError: (error) => set({ error }),

		reset: () => set(initialState),
	}),
);

export default useSendCryptoStore;
