import { IOption } from "@/types/general";
import { create } from "zustand";

export type SafeLockSteps = "duration" | "form" | "preview" | "success";


export interface SafeLockFormData {
	title: string;
	startDate: string;
	endDate: string;
	currency: string;
	targetAmount: number;
	wallet_id: string;
	funding_source_type: string;
}

interface SafeLockState {
	currentStep: SafeLockSteps;
	previousStep: SafeLockSteps | null;
	selectedDuration: IOption<string> | null;
	formData: SafeLockFormData | null;

	setCurrentStep: (step: SafeLockSteps, trackPrevious?: boolean) => void;
	setSelectedDuration: (duration: IOption<string>) => void;
	setFormData: (data: SafeLockFormData) => void;
	reset: () => void;
}

const initialState = {
	currentStep: "duration" as SafeLockSteps,
	previousStep: null as SafeLockSteps | null,
	selectedDuration: null,
	formData: null,
};

const useSafeLockStore = create<SafeLockState>((set) => ({
	...initialState,

	setCurrentStep: (step, trackPrevious = false) =>
		set((state) => ({
			currentStep: step,
			previousStep: trackPrevious
				? state.currentStep
				: state.previousStep,
		})),

	setSelectedDuration: (duration) => set({ selectedDuration: duration }),

	setFormData: (data) => set({ formData: data }),

	reset: () => set(initialState),
}));

export default useSafeLockStore;
