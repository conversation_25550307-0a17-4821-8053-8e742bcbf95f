import { create } from "zustand";

export type Theme = "light" | "dark" | "system";

interface ThemeState {
	theme: Theme;
	resolvedTheme: "light" | "dark";
	setTheme: (theme: Theme) => void;
	toggleTheme: () => void;
}

// Helper function to get system theme preference
const getSystemTheme = (): "light" | "dark" => {
	if (typeof window === "undefined") return "light";
	return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
};

// Helper function to resolve theme based on current setting
const resolveTheme = (theme: Theme): "light" | "dark" => {
	if (theme === "system") return getSystemTheme();
	return theme;
};

// Initialize theme from localStorage
const getInitialTheme = (): Theme => {
	if (typeof window === "undefined") return "system";
	const savedTheme = localStorage.getItem("clyp-theme");
	if (savedTheme && ["light", "dark", "system"].includes(savedTheme)) {
		return savedTheme as Theme;
	}
	return "system";
};

const useThemeStore = create<ThemeState>((set, get) => ({
	theme: getInitialTheme(),
	resolvedTheme: resolveTheme(getInitialTheme()),

	setTheme: (theme: Theme) => {
		const resolvedTheme = resolveTheme(theme);
		set({ theme, resolvedTheme });
		
		// Save to localStorage
		if (typeof window !== "undefined") {
			localStorage.setItem("clyp-theme", theme);
			
			// Apply theme to document
			const root = window.document.documentElement;
			root.classList.remove("light", "dark");
			root.classList.add(resolvedTheme);
		}
	},

	toggleTheme: () => {
		const { theme } = get();
		if (theme === "system") {
			// If system, toggle to opposite of system preference
			const systemTheme = getSystemTheme();
			const newTheme = systemTheme === "light" ? "dark" : "light";
			get().setTheme(newTheme);
		} else {
			// Toggle between light and dark
			const newTheme = theme === "light" ? "dark" : "light";
			get().setTheme(newTheme);
		}
	},
}));

export default useThemeStore;
