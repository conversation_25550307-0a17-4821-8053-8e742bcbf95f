import { create } from 'zustand';
import { ICryptoWallet } from '@/types/crypto-wallets';

interface CryptoSelectionState {
  selectedCrypto: ICryptoWallet | null;
  setSelectedCrypto: (crypto: ICryptoWallet | null) => void;
}

const useCryptoSelectionStore = create<CryptoSelectionState>((set) => ({
  selectedCrypto: null,
  setSelectedCrypto: (crypto) => set({ selectedCrypto: crypto }),
}));

export default useCryptoSelectionStore;