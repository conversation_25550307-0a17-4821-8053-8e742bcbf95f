import { create } from "zustand";
import { IFiatDepositMethod } from "@/types/fiat-banking";
import { IFiatWallet } from "@/types/fiat-wallets";
import { IOption } from "@/types/general";

export type DepositStep =
	| "select"
	| "paymentLink"
	| "temporaryAccount"
	| "previewDeposit"
	| "temporaryAccountDetails"
	| "processingDeposit"
	| "success";


interface BaseDepositPayload {
	coin: IFiatWallet;
	method: IFiatDepositMethod;
	amount: string;
	feeInfo: { fee: number; amount: number };
}


interface PaymentLinkPayload extends BaseDepositPayload {
	type: "paymentLink";
	
}


interface TemporaryAccountPayload extends BaseDepositPayload {
	type: "temporaryAccount";
	
}


type DepositPayload = PaymentLinkPayload | TemporaryAccountPayload;


type DepositFiatPayload = {
	payload: DepositPayload;
};

interface DepositFiatState {
	currentStep: DepositStep;
	previousStep: DepositStep | null;
	selectedCoin: IFiatWallet | null;
	selectedDepositMethod: IOption<IFiatDepositMethod> | null;
	depositFiatPayload: DepositFiatPayload | null;
	transactionReference: string | null;
    paymentLink: string;
	temporaryAccountDetails: {
		accountNumber: string;
		accountName: string;
		bankName: string;
		amount: string;
	} | null;

	
	setCurrentStep: (step: DepositStep, trackPrevious?: boolean) => void;
	setSelectedDepositMethod: (method: IOption<IFiatDepositMethod>) => void;
	setSelectedCoin: (coin: IFiatWallet) => void;
	setDepositFiatPayload: (data: DepositFiatPayload) => void;
	setTransactionReference: (reference: string) => void;
    setPaymentLink: (link: string) => void;
	setTemporaryAccountDetails: (details: {
		accountNumber: string;
		accountName: string;
		bankName: string;
		amount: string;
        expiresAt: string;
	}) => void;
	reset: () => void;
}

const initialState = {
	currentStep: "select" as DepositStep,
	previousStep: null as DepositStep | null,
	selectedCoin: null,
	selectedDepositMethod: null,
	depositFiatPayload: null,
	transactionReference: null,
	temporaryAccountDetails: null,
    paymentLink: "",
};

const useDepositFiatStore = create<DepositFiatState>((set) => ({
	...initialState,

	setCurrentStep: (step, trackPrevious = false) =>
		set((state) => ({
			currentStep: step,
			
			previousStep: trackPrevious
				? state.currentStep
				: state.previousStep,
		})),
	setSelectedDepositMethod: (method) =>
		set({ selectedDepositMethod: method }),
	setSelectedCoin: (coin) => set({ selectedCoin: coin }),
	setDepositFiatPayload: (data) => set({ depositFiatPayload: data }),
	setTransactionReference: (reference) =>
		set({ transactionReference: reference }),
	setTemporaryAccountDetails: (details) =>
		set({ temporaryAccountDetails: details }),
    setPaymentLink: (link) => set({ paymentLink: link }),
	reset: () => set(initialState),
}));

export default useDepositFiatStore;
