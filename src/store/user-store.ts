import { IUser, KY<PERSON>eve<PERSON> } from "@/types/user";
import { create } from "zustand";

interface WalletBalances {
	fiat: number;
	crypto: {
		BTC: number;
		ETH: number;
	};
}

interface UserState {
	user: IUser | null;
	bvnDone: boolean;
	kycDone: boolean;
	kyc_level_completed: KYCLevels | null;
	wallet: {
		balances: WalletBalances;
	};
	setUser: (userData: IUser | null) => void;
	setKycDone: (value: boolean) => void;
	setBvnDone: (value: boolean) => void;
	setKycLevels: (levels: KYCLevels | null) => void;
}

const useUserStore = create<UserState>((set) => ({
	user: null,
	bvnDone: false,
	kycDone: false,
	kyc_level_completed: null,
	wallet: {
		balances: {
			fiat: 0.0,
			crypto: {
				BTC: 0.0,
				ETH: 0.0,
			},
		},
	},
	setUser: (userData) => set({ user: userData }),
	setKycDone: (done) => set({ kycDone: done }),
	setBvnDone: (done) => set({ bvnDone: done }),
	setKycLevels: (levels) => set({ kyc_level_completed: levels }),


	// setUser: (userData) => set((state) => ({ user: userData })),
	// setKycDone: (done) => set((state) => ({ kycDone: done })),
	// setBvnDone: (done) => set((state) => ({ bvnDone: done })),
	// setKycLevels: (levels) => set((state) => ({ kyc_level_completed: levels })),
}));

export default useUserStore;



// export const refreshUserData = async () => {}


// serService.getFullUserData()