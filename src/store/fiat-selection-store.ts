import { create } from 'zustand';

interface FiatWallet {
	id: string;
	number: string;
	name: string;
	currency_name: string;
	image: string;
	currency: string;
	ledger_balance: number;
	available_balance: number;
	is_default: boolean;
	is_virtual: boolean;
	is_partner_managed: boolean;
	is_wallet: boolean;
	bank: string;
	bank_name: string;
	bank_code: string;
	bank_id: string;
	can_deposit: boolean;
	can_withdraw: boolean;
	account_id: string;
	currency_id: string;
	user_id: string;
	meta: string;
	createdAt: string;
	updatedAt: string;
}

interface FiatSelectionStore {
  selectedFiat: FiatWallet | null;
  setSelectedFiat: (fiat: FiatWallet | null) => void;
  clearSelectedFiat: () => void;
}

const useFiatSelectionStore = create<FiatSelectionStore>((set) => ({
  selectedFiat: null,
  setSelectedFiat: (fiat) => set({ selectedFiat: fiat }),
  clearSelectedFiat: () => set({ selectedFiat: null }),
}));

export default useFiatSelectionStore;