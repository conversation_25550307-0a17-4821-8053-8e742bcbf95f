import { ICryptoWallet } from "@/types/crypto-wallets";
import { IFiatWallet } from "@/types/fiat-wallets";
import { ISwapPriceData } from "@/types/swaps";
import { create } from "zustand";


export type SellStep =
	| "select" 
	| "preview" 
	| "pin" 
	| "success" 
	| "receipt"; 


export interface TransactionPayload {
	from: ICryptoWallet;
	to: IFiatWallet;
	amount: number | string;
	method: string;
	price_data: ISwapPriceData;
}

interface SellCryptoState {
	
	currentStep: SellStep;

	
	transaction: TransactionPayload | null;

	
	setCurrentStep: (step: SellStep) => void;
	setTransaction: (transaction: TransactionPayload) => void;

	
	reset: () => void;
}


const useBuyCryptoTransactionStore = create<SellCryptoState>((set) => ({
	
	currentStep: "select",
	transaction: null,

	
	setCurrentStep: (step) => set({ currentStep: step }),
	setTransaction: (transaction) => set({ transaction }),

	
	reset: () =>
		set({
			currentStep: "select",
			transaction: null,
		}),
}));

export default useBuyCryptoTransactionStore;
