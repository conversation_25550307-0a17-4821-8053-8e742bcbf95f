import { ICryptoWallet } from "@/types/crypto-wallets";
import { IFiatWallet } from "@/types/fiat-wallets";
import { ISwapPriceData } from "@/types/swaps";
import { create } from "zustand";


export type PurchaseStep =
	| "select" 
	| "preview" 
	| "pin" 
	| "success" 
	| "receipt"; 


export interface TransactionPayload {
	from: IFiatWallet;
	to: ICryptoWallet;
	amount: number | string;
	method: string;
	price_data: ISwapPriceData;
}

interface BuyCryptoState {
	
	currentStep: PurchaseStep;

	
	transaction: TransactionPayload | null;

	
	setCurrentStep: (step: PurchaseStep) => void;
	setTransaction: (transaction: TransactionPayload) => void;

	
	reset: () => void;
}


const useBuyCryptoTransactionStore = create<BuyCryptoState>((set) => ({
	
	currentStep: "select",
	transaction: null,

	
	setCurrentStep: (step) => set({ currentStep: step }),
	setTransaction: (transaction) => set({ transaction }),

	
	reset: () =>
		set({
			currentStep: "select",
			transaction: null,
		}),
}));

export default useBuyCryptoTransactionStore;
