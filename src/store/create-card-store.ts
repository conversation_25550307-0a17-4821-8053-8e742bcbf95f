import { IFiatWallet } from "@/types/fiat-wallets";
import { create } from "zustand";

export type CreateCardStep =
	| "cardType"
	| "accountType"
	| "createCard"
	| "success";

export interface CardDetails {
	name: string;
	currency: string;
	limit: string;
}

type CardColor = "white" | "black" | "yellow";

interface cardColor {
	currentColor: CardColor;
	setColor: (color: CardColor) => void;
}
export const useCardColor = create<cardColor>((set) => ({
	currentColor: "white",
	setColor: (color) => set({ currentColor: color }),
}));

interface CreateCardState {
	currentStep: CreateCardStep;
	selectedCoin: IFiatWallet | null;

	cardData: {
		currency: string;
		name: string;
		limit: string;
	} | null;

	setCurrentStep: (step: CreateCardStep) => void;
	setSelectedCoin: (coin: IFiatWallet) => void;
	setCardDetails: (details: CardDetails) => void;
	reset: () => void;
}

const initialState = {
	currentStep: "cardType" as CreateCardStep,
	selectedCoin: null,
	cardData: null,
};

const useCreateCardStore = create<CreateCardState>((set) => ({
	...initialState,

	setCurrentStep: (step) => set({ currentStep: step }),

	setSelectedCoin: (coin) =>
		set((state) => ({
			selectedCoin: coin,
			cardData: {
				...state.cardData,
				currency: coin.currency,
				name: "",
				limit: "",
			},
		})),

	setCardDetails: (details) =>
		set((state) => ({
			cardData: {
				...state.cardData,
				...details,
			},
		})),

	reset: () => set(initialState),
}));

export default useCreateCardStore;
