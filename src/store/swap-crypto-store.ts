import { create } from "zustand";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { ISwapPriceData } from "@/types/swaps";


export type SwapStep =
	| "type"
	| "select"
	| "preview"
	| "pin"
	| "success"
	| "receipt";


export interface ISwapTransaction {
	from: ICryptoWallet;
	to: ICryptoWallet;
	amount: number;
	method: string;
	price_data: ISwapPriceData;
	txId?: string;
	timestamp?: number;
}


interface SwapCryptoState {
	currentStep: SwapStep;
	transaction: ISwapTransaction | null;
	txId?: string;
	setCurrentStep: (step: SwapStep) => void;
	setTransaction: (transaction: ISwapTransaction) => void;
	setTxId: (txId: string) => void;
	reset: () => void;
}


const useSwapCryptoTransactionStore = create<SwapCryptoState>((set) => ({
	
	currentStep: "type",
	transaction: null,
	txId: undefined,

	
	setCurrentStep: (step: SwapStep) => set({ currentStep: step }),
	setTransaction: (transaction: ISwapTransaction) => set({ transaction }),
	setTxId: (txId: string) => set({ txId }),

	
	reset: () =>
		set({
			currentStep: "type",
			transaction: null,
			txId: undefined,
		}),
}));

export default useSwapCryptoTransactionStore;
