import { create } from "zustand";
import { ICryptoWallet } from "@/types/crypto-wallets";
import { IOption } from "@/types/general";
import { ICryptoDepositData, ICryptoDepositMethod, ICryptoUniversalNetworkData, ICryptoWithdrawalMethod } from "@/types/crypto-banking";

export type ReceiveStep =
	| "select"
	| "staticDeposit"
	| "universalDepositForm"
	| "universalDepositDetails";

interface DisplayAccountData {
	data: ICryptoDepositData;
	payload: {
		coin: ICryptoWallet;
		withdrawalMethod: ICryptoWithdrawalMethod;
		depositMethod: IOption<ICryptoDepositMethod>;
		network: IOption<ICryptoUniversalNetworkData>;
		amount: string;
		feeInfo: {
			fee: number;
			amount: number;
		};
	};
}

interface ReceiveCryptoState {
	currentStep: ReceiveStep;
	selectedCoin: ICryptoWallet | null;
	selectedDepositMethod: IOption<ICryptoDepositMethod> | null;
	displayAccountsData: DisplayAccountData | null;

	
	setCurrentStep: (step: ReceiveStep) => void;
	setSelectedDepositMethod: (method: IOption<ICryptoDepositMethod>) => void;
	setSelectedCoin: (coin: ICryptoWallet) => void;
	setDisplayAccountsData: (data: DisplayAccountData) => void;
	reset: () => void;
}

const initialState = {
	currentStep: "select" as ReceiveStep,
	selectedCoin: null,
	selectedDepositMethod: null,
	selectedNetwork: null,
	amount: "",
    displayAccountsData: null,
};

const useReceiveCryptoStore = create<ReceiveCryptoState>((set) => ({
	...initialState,

	setCurrentStep: (step) => set({ currentStep: step }),
	setSelectedDepositMethod: (method) =>
		set({ selectedDepositMethod: method }),
	setSelectedCoin: (coin) => set({ selectedCoin: coin }),
	setDisplayAccountsData: (data) => set({ displayAccountsData: data }),
	reset: () => set(initialState),
}));

export default useReceiveCryptoStore;
