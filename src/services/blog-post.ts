// import { API_URL } from "@/config/constants";
// import { ApiResponse, BlogPostResponse } from "@/types";

export const BlogService = {
	// fetchBlogPosts: async (): Promise<ApiResponse<BlogPostResponse>> => {
	// 	try {
	// 		const response = await fetch(
	// 			`${API_URL}/user-gateway/get-news?type=BLOG`,
	// 		);
	// 		if (!response.ok) {
	// 			throw new Error("Failed to fetch posts");
	// 		}

	// 		const data = await response.json();
	// 		return data;
	// 	} catch (error) {
	// 		console.error(error);
	// 		if (error instanceof Error) {
	// 			throw new Error(error.message);
	// 		} else {
	// 			throw new Error("An unknown error occurred");
	// 		}
	// 	}
	// },
};
