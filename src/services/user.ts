import { apiRoutes } from "@/config/api-routes";
import { userGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import { FullUserDataResponse } from "@/types/user";
import { ApiResponse } from "@/types/auth";

export const UserService = {
	getChatHistory: async () => {
		try {
			const response = await userGateway.get(apiRoutes.user.chat_history);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	getFullUserData: async (): Promise<FullUserDataResponse> => {
		try {
			const response = await userGateway.get<FullUserDataResponse>(
				apiRoutes.user.full_user_data,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	getUserCustomerByClypId: async (identifier: string): Promise<any> => {
		try {
			const response = await userGateway.get<any>(
				apiRoutes.user.get_user_by_cutomeries(identifier),
			);

			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// getAccountInsightsOverview: async () => {
	// 	try {
	// 		const response = await userGateway.get(
	// 			apiRoutes.user.account_insights_overview,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// unlinkAccount: async (id: string) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.unlink_account,
	// 			{ mono_account_id: id },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// getNews: async () => {
	// 	try {
	// 		const response = await userGateway.get(apiRoutes.user.get_news);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// getAvailableCountries: async () => {
	// 	try {
	// 		const response = await userGateway.get(
	// 			apiRoutes.user.available_countries,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// getStates: async (country: string) => {
	// 	try {
	// 		const response = await userGateway.get(apiRoutes.user.get_states, {
	// 			params: { country },
	// 		});
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// getCitiesInState: async (country: string, state: string) => {
	// 	try {
	// 		const response = await userGateway.get(
	// 			apiRoutes.user.cities_in_states,
	// 			{
	// 				params: { country, state },
	// 			},
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// getAllUserCourses: async (params: any = {}) => {
	// 	try {
	// 		const response = await userGateway.get(
	// 			apiRoutes.user.get_all_course_user,
	// 			{
	// 				params,
	// 			},
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// getAllUserTopics: async () => {
	// 	try {
	// 		const response = await userGateway.get(
	// 			apiRoutes.user.get_all_topics_user,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// getReferralData: async () => {
	// 	try {
	// 		const response = await userGateway.get(
	// 			apiRoutes.user.get_referral_data,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	getAvailableProfileImages: async () => {
		try {
			const response = await userGateway.get(
				apiRoutes.user.get_all_available_profile_image,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	uploadProfileImageAvatar: async (picture: string) => {
		try {
			const response = await userGateway.post(
				apiRoutes.user.upload_profile_image,
				{ picture },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	getUserNotifications: async () => {
		try {
			const response = await userGateway.get(
				apiRoutes.user.get_user_notification,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	markNotificationAsRead: async (notificationId: string) => {
		try {
			const response = await userGateway.post(
				apiRoutes.user.mark_notification_read,
				{ notificationId },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	verifyPin: async (pin: string): Promise<ApiResponse> => {
		try {
			const response = await userGateway.post<ApiResponse>(
				apiRoutes.user.verify_pin,
				{ pin },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	sendChat: async (message: string) => {
		try {
			const response = await userGateway.post(apiRoutes.user.send_chat, {
				message,
			});
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	setPin: async (pin: string): Promise<ApiResponse> => {
		try {
			const response = await userGateway.post<ApiResponse>(
				apiRoutes.user.set_pin,
				{ pin },
			);

			return response.data;
		} catch (error: unknown) {
			console.error("Error setting pin:", error);

			return handleError(error);
		}
	},
	// changePin: async (payload: ChangePinPayload) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.change_pin,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// toggleProMode: async (proMode: boolean) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.activate_deactivate_pro_mode,
	// 			proMode,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// validateBusiness: async (payload: BusinessValidationPayload) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.validate_business,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// addUserAddress: async (payload: AddressPayload) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.add_user_address,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// updateUsername: async (username: string) => {
	// 	try {
	// 		const response = await userGateway.put(
	// 			apiRoutes.user.update_username,
	// 			{ username },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// uploadProfileImage: async (file: File) => {
	// 	try {
	// 		const formData = new FormData();
	// 		formData.append("image", file);
	// 		const response = await userFormDataGateway.post(
	// 			apiRoutes.user.upload_profile_image,
	// 			formData,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	requestEmailChange: async (newEmail: string): Promise<ApiResponse> => {
		try {
			const response = await userGateway.post(
				apiRoutes.user.change_email_request,
				{ newEmail }
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	confirmEmailChange: async (payload: { authToken: string; newEmail: string }): Promise<ApiResponse> => {
		try {
			const response = await userGateway.post(
				apiRoutes.user.confirm_email_change_request,
				{ authToken: payload.authToken, newEmail: payload.newEmail }
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
	// submitLoanRequest: async (payload: LoanRequestPayload) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.submit_loan_request,
	// 			{ data: payload },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// addReferee: async (payload: RefereePayload) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.add_referee,
	// 			{ data: payload },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// submitCode: async (payload: CodeSubmissionPayload) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.submit_code,
	// 			{ mono_code: payload.code },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
	// updateNotificationStatus: async (payload: NotificationStatusPayload) => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.user.read_unread_notification,
	// 			{
	// 				notification_id: payload.id,
	// 				status: payload.status.toUpperCase(),
	// 			},
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
};
