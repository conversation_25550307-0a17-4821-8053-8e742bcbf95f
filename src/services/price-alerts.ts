// import { apiRoutes } from "@/config/api-routes";
// import { currencyGateway } from "@/config/axios";
// import { handleError } from "@/utils/error-handler";
// import {
// 	SetPriceAlertPayload,
// 	SetPriceAlertResponse,
// 	EditPriceAlertStatusPayload,
// 	EditPriceAlertStatusResponse,
// 	DeletePriceAlertPayload,
// 	DeletePriceAlertResponse,
// 	PriceAlertCoinsResponse,
// 	PriceAlertListResponse,
// } from "@/types/price-alerts";

export const PriceAlertsService = {
	// setPriceAlert: async (
	// 	payload: SetPriceAlertPayload,
	// ): Promise<SetPriceAlertResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.price_alerts.set_price_alert,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// editPriceAlertStatus: async (
	// 	payload: EditPriceAlertStatusPayload,
	// ): Promise<EditPriceAlertStatusResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.price_alerts.edit_price_alert_status,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// deletePriceAlert: async (
	// 	payload: DeletePriceAlertPayload,
	// ): Promise<DeletePriceAlertResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.price_alerts.delete_price_alert,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getPriceAlertCoins: async (): Promise<PriceAlertCoinsResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.price_alerts.price_alerts_coins,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getPriceAlertList: async (): Promise<PriceAlertListResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.price_alerts.get_price_alert_list,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
};
