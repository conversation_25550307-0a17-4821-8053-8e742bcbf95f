import { APP_CONFIG } from "@/config";
import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import {
	ValidateCustomerBillPayload,
	ValidateCustomerBillResponse,
	PurchaseBillPayload,
	PurchaseBillResponse,
	PurchaseAirtimePayload,
	PurchaseAirtimeResponse,
} from "@/types/bills";
import { handleError } from "@/utils/error-handler";

export const BillsService = {
	// getAllBillerCategory: async (
	// 	code: string,
	// ): Promise<BillCategoryResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.bills.get_all_biller_category,
	// 			{ code },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getBillsFromCategory: async (
	// 	biller_type: string,
	// ): Promise<BillsFromCategoryResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.bills.get_bill_from_category,
	// 			{ biller_type },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	validateCustomerBill: async (
		payload: ValidateCustomerBillPayload,
	): Promise<ValidateCustomerBillResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.bills.validate_customer_bill,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	purchaseBill: async (
		payload: PurchaseBillPayload,
	): Promise<PurchaseBillResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.bills.purchase_bill,
				{ data: payload },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	async purchaseAirtime(payload: PurchaseAirtimePayload): Promise<PurchaseAirtimeResponse> {
        const token = localStorage.getItem("auth_token");
        
        const response = await fetch(
            `${APP_CONFIG.API_URLS.CURRENCY}/bills/purchase-bill`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify(payload),
            }
        );

        if (!response.ok) {
            throw new Error("Failed to purchase airtime");
        }

        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || "Purchase failed");
        }

        return result;
    }


};


