import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	// AddCryptoWalletPayload,
	AddCryptoWalletResponse,
	// CreateCryptoWalletPayload,
	// CreateCryptoWalletResponse,
	CryptoWalletResponse,
} from "@/types/crypto-wallets";

export const CryptoWalletService = {
	addWallet: async (currency: string): Promise<AddCryptoWalletResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.crypto_wallets.add_wallet,
				{ currency },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// createWallet: async (
	// 	payload: CreateCryptoWalletPayload,
	// ): Promise<CreateCryptoWalletResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.crypto_wallets.create_wallet,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getUserWallets: async (): Promise<CryptoWalletResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.crypto_wallets.get_user_wallets,
	// 		);

	//

	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getUserWallets: async (): Promise<{ message: string; user_wallets: ICryptoWallet[] }> => {
	getUserWallets: async (): Promise<CryptoWalletResponse> => {
		try {
			const response = await currencyGateway.get<CryptoWalletResponse>(
				apiRoutes.currency.crypto_wallets.get_user_wallets,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
