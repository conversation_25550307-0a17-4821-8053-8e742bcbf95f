import { apiRoutes } from "@/config/api-routes";
import { userGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import { ApiResponse } from "@/types/auth";
import { NotificationSettings } from "@/types/notification";

export const NotificationService = {
	notificationSettings: async (payload: NotificationSettings) => {
		try {
			const response = await userGateway.post(
				apiRoutes.notification.notification_settings, payload
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
