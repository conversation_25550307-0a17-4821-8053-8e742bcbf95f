// import { apiRoutes }  from "@/config/api-routes";
// import {currencyGateway} from "@/config/axios";
// import {handleError} from "@/utils/error-handler";
// import {CreateSavingsGoalPayload,CreateSavingsGoalResponse,SavingsGoal} from  "@/types/savings";
// import { get } from "http";

// export const SavingsService = {
//     createSavingsGoal:async(payload:CreateSavingsGoalPayload):Promise<CreateSavingsGoalResponse>=>{
//         try{
//             const response = await currencyGateway.post(
//                 apiRoutes.currency.fiat_savings.create_saving_goal,
//                 payload
//             );
//             return response.data;

//         }catch (error:unknown){
//             return handleError(error);

//         }

//     },
//     getSavingGoal:async(goalId:string):Promise<SavingsGoal>=>{
//         try{
//             const response = await currencyGateway.get(
//                 `${apiRoutes.currency.fiat_savings.get_saving_goals}/${goalId}`
                
//             );
//             return response.data;
//         }
//         catch(error:unknown){
//             return handleError(error);
//         }
//     }

// }
