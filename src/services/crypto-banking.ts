import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	// DynamicDepositPayload,
	// DynamicDepositResponse,
	// WithdrawalMethodsPayload,
	WithdrawalMethodsResponse,
	// UniversalNetworksPayload,
	// UniversalNetworksResponse,
	// CoinNetworksResponse,
	// DepositMethodsPayload,
	DepositMethodsResponse,
	ICryptoWithdrawalRequest,
	ICryptoWithdrawalResponse,
	ICryptoUniversalDepositMethodsResponse,
	ICryptoDepositRequest,
	ICryptoDepositResponse,
	// ValidateAddressPayload,
	// ValidateAddressResponse,
	// TransactionsListPayload,
	// TransactionsListResponse,
	// TransactionDetailsPayload,
	// TransactionDetailsResponse,
	// BeneficiariesListResponse,
	// FiatBeneficiaryPayload,
	// FiatBeneficiaryResponse,
	AvailableCurrenciesResponse,
} from "@/types/crypto-banking";
import { ICryptoWallet } from "@/types/crypto-wallets";

export const CryptoBankingService = {
	send: async (payload: ICryptoWithdrawalRequest): Promise<ICryptoWithdrawalResponse> => {
		try {
			const response = await currencyGateway.post<ICryptoWithdrawalResponse>(
				apiRoutes.currency.crypto_banking.send,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	createDynamicDeposit: async (
		payload: ICryptoDepositRequest,
	): Promise<ICryptoDepositResponse> => {
		try {
			const response = await currencyGateway.post<ICryptoDepositResponse>(
				apiRoutes.currency.crypto_banking.create_dynamic_deposit,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	getCurrencyWithdrawalMethods: async (
		coin: string,
	): Promise<WithdrawalMethodsResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.crypto_banking.get_currency_withdrawal_methods(coin),
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// getUniversalWithdrawalNetworks: async (
	// 	payload: UniversalNetworksPayload,
	// ): Promise<UniversalNetworksResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.crypto_banking.get_currency_universal_withdrawal_networks(
	// 				payload.currency,
	// 			),
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	getUniversalDepositNetworks: async (
		coin: ICryptoWallet["currency"],
	): Promise<ICryptoUniversalDepositMethodsResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.crypto_banking.get_currency_universal_deposit_networks(
					coin,
				),
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// getCoinNetworks: async (coin: string): Promise<CoinNetworksResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.crypto_banking.get_coin_networks,
	// 			{ params: { currency: coin?.toUpperCase() } },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	getCurrencyDepositMethods: async (
		currency: string,
	): Promise<DepositMethodsResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.crypto_banking.get_currency_deposit_methods,
				{ params: { currency: currency?.toUpperCase() } },
			);            
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// validateWalletAddress: async (
	// 	payload: ValidateAddressPayload,
	// ): Promise<ValidateAddressResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.crypto_banking.validate_wallet_address,
	// 			{ params: payload },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getTransactions: async (
	// 	payload: TransactionsListPayload,
	// ): Promise<TransactionsListResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.crypto_banking.get_transactions,
	// 			{ params: payload },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getTransaction: async (
	// 	payload: TransactionDetailsPayload,
	// ): Promise<TransactionDetailsResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.crypto_banking.get_transaction,
	// 			{ params: { transactionId: payload.transactionId } },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getBeneficiariesList: async (): Promise<BeneficiariesListResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.crypto_banking.get_beneficiaries_list,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// getFiatBeneficiary: async (): Promise<FiatBeneficiaryResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.crypto_banking.get_fiat_beneficiary,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	getAvailableCurrencies: async (): Promise<AvailableCurrenciesResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.crypto_banking.get_available_currencies_user,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
