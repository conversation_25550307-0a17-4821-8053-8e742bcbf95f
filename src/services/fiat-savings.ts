import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	GetSavingGoalsResponse,
	CreateFiatSavingGoalPayload,
	CreateFiatSavingGoalResponse,
	FundFiatSavingsResponse,
	FundFiatSavingsPayload,
	WithdrawFiatSavingsPayload,
	WithdrawFiatSavingsResponse,
} from "@/types/fiat-savings";



export const FiatSavingsService = {
	getSavingGoals: async (): Promise<GetSavingGoalsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_savings.get_saving_goals,
			);
			console.log(response.data,"abeg")
			return response.data.savingsGoals;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	createSavingGoal: async (
		payload: CreateFiatSavingGoalPayload,
	): Promise<CreateFiatSavingGoalResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_savings.create_saving_goal,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	fundSavingsManual: async (
		payload: FundFiatSavingsPayload,
	): Promise<FundFiatSavingsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_savings.fund_savings_manual,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	fundSavingsAuto: async (
		payload: FundFiatSavingsPayload,
	): Promise<FundFiatSavingsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_savings.fund_savings_auto,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	withdrawSavings: async (
		payload: WithdrawFiatSavingsPayload,
	): Promise<WithdrawFiatSavingsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_savings.withdraw_savings_funds,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
