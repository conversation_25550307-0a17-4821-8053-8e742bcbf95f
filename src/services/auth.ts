// import { apiRoutes } from "@/config/api-routes";
// import { userGateway } from "@/config/axios";
import {
	ActivateUserPayload,
	ApiResponse,
	ChangePasswordPayload,
	ChangePasswordResponse,
	ConfirmCodePayload,
	CreateUserPayload,
	ForgetPasswordPayload,
	ForgetPasswordResponse,
	LoginPayload,
	LoginResponse,
	RegisterUserResponse,
	ResendOTPPayload,
	RetrievePasswordEmailPayload,
	SetPinPayload,
	SetPinResponse,
} from "@/types/auth";
import { handleError } from "@/utils/error-handler";

import axios, { AxiosResponse } from "axios";

const API_BASE_URL = "https://clyp-user-staging-c8d72db35571.herokuapp.com";

const AuthService = {
	registerUser: async (
		createUserPayload: CreateUserPayload,
	): Promise<RegisterUserResponse> => {
		try {
			const response: AxiosResponse<RegisterUserResponse> =
				await axios.post(
					`${API_BASE_URL}/user-gateway/user/register`,
					createUserPayload,
				);
			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},
	activateUser: async (
		payload: ActivateUserPayload,
	): Promise<ApiResponse> => {
		try {
			const response: AxiosResponse<ApiResponse> = await axios.post(
				`${API_BASE_URL}/user-gateway/user/activate-user`,
				payload,
			);
			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},
	resendOTP: async (payload: ResendOTPPayload): Promise<ApiResponse> => {
		try {
			const response: AxiosResponse<ApiResponse> = await axios.post(
				`${API_BASE_URL}/user-gateway/user/resend-auth-code`,
				payload,
			);
			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},
	retrievePasswordEmail: async (
		payload: RetrievePasswordEmailPayload,
	): Promise<ApiResponse> => {
		try {
			const response: AxiosResponse<ApiResponse> = await axios.post(
				`${API_BASE_URL}/user-gateway/user/retrive-password-email`,
				payload,
			);
			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},

	forgetPassword: async (
		payload: ForgetPasswordPayload,
	): Promise<ForgetPasswordResponse> => {
		try {
			const response: AxiosResponse<ForgetPasswordResponse> =
				await axios.post(
					`${API_BASE_URL}/user-gateway/user/forget-password`,
					payload,
				);
			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},

	login: async (payload: LoginPayload): Promise<LoginResponse> => {
		try {
			const response: AxiosResponse<LoginResponse> = await axios.post(
				`${API_BASE_URL}/user-gateway/user/login`,
				payload,
			);

			// if (response.data.token) {
			// 	localStorage.setItem("auth_token", response.data.token);
			// }

			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},

	changePassword: async (
		payload: ChangePasswordPayload,
	): Promise<ChangePasswordResponse> => {
		try {
			const response: AxiosResponse<ChangePasswordResponse> =
				await axios.post(
					`${API_BASE_URL}/user-gateway/user/change-password`,
					payload,
				);
			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},

	setPin: async (payload: SetPinPayload): Promise<SetPinResponse> => {
		try {
			const response: AxiosResponse<SetPinResponse> = await axios.post(
				`${API_BASE_URL}/user-gateway/user/set-pin`,
				payload,
			);
			return response.data;
		} catch (error) {
			return handleError(error);
		}
	},

	async confirmCodeForForgotPassword(
		payload: ConfirmCodePayload,
	): Promise<ApiResponse> {
		try {
			const response: AxiosResponse<ApiResponse> = await axios.post(
				`${API_BASE_URL}/user-gateway/user/confirm-code-forgot-password`,
				payload,
			);			
			return response.data;
		} catch (error) {			
			return handleError(error);
		}
	},

	// googleSSO: async (token: string): Promise<LoginResponse> => {
	// 	try {
	// 		const response = await userGateway.post(apiRoutes.auth.sso.google, {
	// 			token,
	// 		});

	// 		if (response.data.token) {
	// 			localStorage.setItem(ADMIN_ACCESS_TOKEN, response.data.token);
	// 		}

	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// appleSSO: async (token: string): Promise<LoginResponse> => {
	// 	try {
	// 		const response = await userGateway.post(apiRoutes.auth.sso.apple, {
	// 			token,
	// 		});

	// 		if (response.data.token) {
	// 			localStorage.setItem(ADMIN_ACCESS_TOKEN, response.data.token);
	// 		}

	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	/**
	 * Logs out the current user by removing the auth token
	 */
	logout: () => {
		localStorage.removeItem("auth_token");
	},

	// forgetPassword: async (
	// 	payload: ForgetPasswordPayload,
	// ): Promise<ForgetPasswordResponse> => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.auth.forget_password,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// confirmForgotPasswordCode: async (
	// 	payload: ConfirmCodePayload,
	// ): Promise<void> => {
	// 	try {
	// 		await userGateway.post(
	// 			apiRoutes.auth.confirm_code_forget_password,
	// 			payload,
	// 		);
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// // Account Activation
	// activateUser: async (payload: ActivateUserPayload): Promise<void> => {
	// 	try {
	// 		await userGateway.post(apiRoutes.auth.activate_user, payload);
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// retrievePasswordEmail: async (
	// 	payload: RetrievePasswordEmailPayload,
	// ): Promise<void> => {
	// 	try {
	// 		await userGateway.post(
	// 			apiRoutes.auth.retrieve_password_email,
	// 			payload,
	// 		);
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// resendAuthCode: async (payload: ResendAuthCodePayload): Promise<void> => {
	// 	try {
	// 		await userGateway.post(apiRoutes.auth.resend_auth_code, payload);
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// // Core Authentication
	// login: async (payload: LoginPayload): Promise<LoginResponse> => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.auth.login,
	// 			payload,
	// 		);

	// 		if (response.data.token) {
	// 			localStorage.setItem("auth_token", response.data.token);
	// 		}

	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// register: async (payload: RegisterPayload): Promise<RegisterResponse> => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.auth.register,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// // KYC Verification
	// submitKycValidation: async (
	// 	payload: KycValidationPayload,
	// ): Promise<KycResponse> => {
	// 	try {
	// 		const response = await userGateway.post(
	// 			apiRoutes.auth.kyc_validation,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	// uploadKycImages: async (
	// 	payload: KycImageVerificationPayload,
	// ): Promise<KycResponse> => {
	// 	try {
	// 		const formData = new FormData();
	// 		Object.entries(payload).forEach(([key, value]) => {
	// 			if (value instanceof File) {
	// 				formData.append(key, value);
	// 			} else if (value !== undefined) {
	// 				formData.append(key, String(value));
	// 			}
	// 		});

	// 		const response = await userFormDataGateway.post(
	// 			apiRoutes.auth.kyc_image_verification,
	// 			formData,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },
};

export default AuthService;
