import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	CreateSwapPayload,
	CreateSwapResponse,
	GetSwappingPriceResponse,
	SwappingPricePayload,
	// GetSwapsForCurrencyResponse,
	GetSwappingRateResponse,
} from "@/types/swaps";

export const SwapService = {
	createSwap: async (
		payload: CreateSwapPayload,
	): Promise<CreateSwapResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.swaps.create_swap,
				payload,
			);
			return response.data;
		} catch (error) {
			  console.error("Swap creation failed:",error.response?.data);
			return handleError(error);
		}
	},

	getSwappingPrice: async (
		payload: SwappingPricePayload,
	): Promise<GetSwappingPriceResponse> => {
		try {
			const response = await currencyGateway.get<GetSwappingPriceResponse>(
				apiRoutes.currency.swaps.get_swapping_price,
				{
					params: {
						...payload,
						amount: payload.amount || "1",
					},
				},
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// getSwapsForCurrency: async (
	// 	currency: string,
	// ): Promise<GetSwapsForCurrencyResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.swaps.get_swaps_for_currency(currency),
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	getSwappingRate: async (
		price_id: string,
	): Promise<GetSwappingRateResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.swaps.get_swapping_rate(price_id),
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
