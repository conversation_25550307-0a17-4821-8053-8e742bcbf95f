import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import {
	CreateCardPayload,
	CreateCardResponse,
	FundCardPayload,
	WithdrawCardPayload,
	FreezeUnfreezeCardPayload,
	CardListResponse,
	CardDetailsResponse,
	CardTransactionsResponse,
} from "@/types/cards";
import { ApiResponse } from "@/types/auth";
import { handleError } from "@/utils/error-handler";

export const CardsService = {
	createCard: async (
		payload: CreateCardPayload,
	): Promise<CreateCardResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.cards.create_card,
				{ data: payload },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	fundCard: async (payload: FundCardPayload): Promise<ApiResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.cards.fund_card,
				{ data: payload },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	withdrawFromCard: async (
		payload: WithdrawCardPayload,
	): Promise<ApiResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.cards.withdraw_card,
				 payload ,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	terminateCard: async (id: string): Promise<ApiResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.cards.terminate_card,
				{ params: { card_id: id } },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	freezeOrUnfreezeCard: async (
		payload: FreezeUnfreezeCardPayload,
	): Promise<ApiResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.cards.freeze_unfreeze_card,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	getUserCardsList: async (): Promise<CardListResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.cards.get_user_cards_list,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	getCardDetails: async (id: string): Promise<CardDetailsResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.cards.get_user_cards_details,
				{ params: { card_id: id } },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	getCardTransactions: async (
		id: string,
	): Promise<CardTransactionsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.cards.get_card_transactions,
				{ card_id: id },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
