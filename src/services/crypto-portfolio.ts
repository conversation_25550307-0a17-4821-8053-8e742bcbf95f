import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	MarketDataPayload,
	MarketDataResponse,
	MarketChartPayload,
	MarketChartResponse,
	CoinDataPayload,
	CoinDataResponse,
} from "@/types/crypto-portfolio";

export const CryptoPortfolioService = {
	fetchCoinsMarketData: async (
		payload: MarketDataPayload,
	): Promise<MarketDataResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.crypto_portfolio.fetch_coins_market_data(
					payload.currency,
				),
				{ params: { page: payload.page, size: payload.size } },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	fetchCoinsMarketChart: async (
		payload: MarketChartPayload,
	): Promise<MarketChartResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.crypto_portfolio.fetch_coins_market_chart,
				{ params: payload },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	fetchCoinData: async (
		payload: CoinDataPayload,
	): Promise<CoinDataResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.crypto_portfolio.fetch_coin_data(
					encodeURIComponent(payload.coin_id),
				),
				{ params: { currency: payload.currency } },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
