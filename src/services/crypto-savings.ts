import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	CryptosavingsGoalPayload,
	GetSavingGoalsResponse,
	FundSavingsPayload,
	FundSavingsResponse,
	WithdrawCryptoSavingsPayload,
	WithdrawSavingsResponse
	// SavingsGoal,
	// FundSavings
	
} from "@/types/crypto-savings";
import { AutoSavePayload } from "@/types/fiat-savings";

export const CryptoSavingsService = {
	getSavingsGoals: async (): Promise<GetSavingGoalsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.crypto_savings.get_saving_goals,
			);
			console.log('response', response.data);
			// Safely access the nested savingsGoals array
			return response.data?.data?.savingsGoals || []; 
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	
	createSavingGoal: async (
		payload: CryptosavingsGoalPayload,
	): Promise<any> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.crypto_savings.create_saving_goal,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	fundSavingsManual:  async (
		payload: FundSavingsPayload,
	): Promise<FundSavingsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.crypto_savings.fund_savings_manual,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	fundSavingsAuto: async (
		payload: AutoSavePayload,
	): Promise<FundSavingsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.crypto_savings.fund_savings_auto,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	withdrawSavings: async (
		payload: WithdrawCryptoSavingsPayload,
	): Promise<WithdrawSavingsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.crypto_savings.withdraw_savings_funds,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
