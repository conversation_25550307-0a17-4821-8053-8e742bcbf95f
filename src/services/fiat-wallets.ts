import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	FiatWalletResponse,
	AddWalletResponse,
	AvailableCurrenciesResponse,
} from "@/types/fiat-wallets";

export const FiatWalletService = {
	getUserWallets: async (): Promise<FiatWalletResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_wallets.get_user_wallets,
			);

			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	addWallet: async (currency: string): Promise<AddWalletResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_wallets.add_wallet,
				{ currency },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	getAvailableCurrencies: async (): Promise<AvailableCurrenciesResponse> => {
		try {
			const response = await currencyGateway.get(
				apiRoutes.currency.fiat_wallets.get_available_currencies,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
