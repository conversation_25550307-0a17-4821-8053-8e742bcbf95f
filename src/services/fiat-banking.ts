import { apiRoutes } from "@/config/api-routes";
import { currencyGateway } from "@/config/axios";
import { handleError } from "@/utils/error-handler";
import {
	ResolveAccountPayload,
	ResolveAccountResponse,
	BankListResponse,
	// 	CreateDepositMethodPayload,
	// 	CreateDepositMethodResponse,
		CreateDepositPayload,
		CreateDepositResponse,
		CreateWithdrawalPayload,
		CreateWithdrawalResponse,
		GetDepositMethodResponse,
	// 	GetTransactionPayload,
	// 	GetTransactionResponse,
	FiatWithdrawalMethodsResponse,
} from "@/types/fiat-banking";

export const FiatBankingService = {
	resolveAccount: async (
		payload: ResolveAccountPayload,
	): Promise<ResolveAccountResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_banking.resolve_account,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	getBankList: async (method: string): Promise<BankListResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_banking.get_bank_list,
				{ transaction_method: method },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// createDepositMethod: async (
	// 	payload: CreateDepositMethodPayload,
	// ): Promise<CreateDepositMethodResponse> => {
	// 	try {
	// 		const response = await currencyGateway.post(
	// 			apiRoutes.currency.fiat_banking.create_account_deposit_method,
	// 			payload,
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	createDeposit: async (
		payload: CreateDepositPayload,
	): Promise<CreateDepositResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_banking.create_deposit,
				payload,
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	createWithdrawal: async (
		payload: Partial<CreateWithdrawalPayload>,
	): Promise<CreateWithdrawalResponse> => {
		try {
			//    console.log("Sending withdrawal payload:", JSON.stringify(payload, null, 2));
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_banking.create_withdrawal,
				payload,
			);
			console.log("Successful response:", response.data);
			return response.data;
		} catch (error: unknown) {
	// 		    console.error("Withdrawal API error:", error);
    // console.error("Error response data:", error.response?.data);
    // console.error("Error status:", error.response?.status);
			return handleError(error);
		}
	},

	getDepositMethod: async (
		account_id: string,
	): Promise<GetDepositMethodResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_banking.get_deposit_method,
				{ account_id },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},

	// getTransactionById: async (id: string): Promise<GetTransactionResponse> => {
	// 	try {
	// 		const response = await currencyGateway.get(
	// 			apiRoutes.currency.fiat_banking.get_transaction_by_id,
	// 			{ params: { transaction_id: id } },
	// 		);
	// 		return response.data;
	// 	} catch (error: unknown) {
	// 		return handleError(error);
	// 	}
	// },

	getWithdrawFiatMethods: async (
		currency: string,
	): Promise<FiatWithdrawalMethodsResponse> => {
		try {
			const response = await currencyGateway.post(
				apiRoutes.currency.fiat_banking.get_withdrawal_method,
				{ currency },
			);
			return response.data;
		} catch (error: unknown) {
			return handleError(error);
		}
	},
};
