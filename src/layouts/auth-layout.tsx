import { Icon } from "@/components/icons";
import Container from "@/layouts/section-container";
import { Link, Outlet } from "react-router-dom";

export default function AuthLayout() {
	return (
		<div className="auth-layout min-h-screen flex flex-col">
			<header className="sticky top-0 z-50 bg-white backdrop-blur-md border border-b-gray-200">
				<Container className="bg-white">
					<nav className="flex items-center justify-between py-4">
						<Link to="/" className="flex items-center">
							<Icon
								name="clyppay_logo"
								className="w-22 sm:w-48 h-auto"
							/>
						</Link>
					</nav>
				</Container>
			</header>
			<main className="flex-1 flex items-center justify-center bg-white">
				<Outlet />
			</main>
			<footer className="py-4 text-center text-sm bg-white text-gray-500">
				<div className="container bg-white mx-auto">
					&copy; {new Date().getFullYear()} Clyp Inc. All rights
					reserved.
				</div>
			</footer>
		</div>
	);
}
