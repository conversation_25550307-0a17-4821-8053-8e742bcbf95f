import { Navbar } from "@/components/navbar";
import { Sidebar } from "@/components/sidebar";
import { useState, useEffect } from "react";
import { Outlet } from "react-router-dom";
import { useFullUserData } from "@/hooks/api/user";

export default function DashboardLayout() {
	const [sidebarOpen, setSidebarOpen] = useState(false);
	const { isLoading, error } = useFullUserData();

	useEffect(() => {
		const handleResize = () => {
			if (window.innerWidth < 768 && sidebarOpen) {
				setSidebarOpen(false);
			}
		};

		window.addEventListener("resize", handleResize);
		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, [sidebarOpen]);

	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-screen bg-background">
				<div className="flex flex-col items-center space-y-6 max-w-md mx-auto px-4">
					<img
						src="/favicon-512.png"
						alt="Clyppay Logo"
						className="size-20 object-contain animate-pulse"
					/>
					<div className="text-center space-y-2">
						<p className="text-foreground text-2xl font-bold">
							Your Blockchain Passport
						</p>
						<p className="text-muted-foreground text-sm">
							Loading your dashboard...
						</p>
					</div>

					{/* Progress Bar */}
					<div className="w-full max-w-xs">
						<div className="w-full bg-muted rounded-full h-2 overflow-hidden">
							<div className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full animate-[loading_2s_ease-in-out_infinite] bg-[length:200%_100%]"></div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Log error but continue rendering dashboard
	if (error) {
		console.error("Failed to load user data:", error);
	}

	return (
		<div className="flex h-screen flex-col overflow-hidden bg-background text-foreground dark:bg-background">
			{/* Navbar - Fixed at top */}
			<Navbar onMenuClick={() => setSidebarOpen(!sidebarOpen)} />

			<div className="flex flex-1 overflow-hidden">
				{/* Sidebar - Fixed on left */}
				<Sidebar
					isOpen={sidebarOpen}
					onClose={() => setSidebarOpen(false)}
				/>
				<main className="flex-1 overflow-y-auto bg-background dark:bg-background">
					<div className="px-4 sm:px-6 md:px-0 py-8 md:py-14">
						<Outlet />
					</div>
				</main>
			</div>
		</div>
	);
}
