import cn from "@/utils/class-names";
import { ReactNode } from "react";

interface ContainerProps {
  children: ReactNode;
  className?: string;
  sectionClassName?: string;
}

const Container = ({ children, className, sectionClassName }: ContainerProps) => {
  return (
    <section className={cn("mx-auto max-w-screen-xl w-[95%]", sectionClassName)}>
    {/* <section className="mx-auto max-w-[1920px] w-[90%]"> */}
      <div className={cn("", className)}>{children}</div>
    </section>
  );
};

export default Container;
