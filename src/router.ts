// Generouted, changes to this file will be overridden
/* eslint-disable */

import { components, hooks, utils } from '@generouted/react-router/client'

export type Path =
  | `/auth/confirm-email`
  | `/auth/create-new-password`
  | `/auth/forgot-password`
  | `/auth/login`
  | `/auth/register`
  | `/auth/reset-password`
  | `/auth/verify-email`
  | `/chatbot`
  | `/dashbaord/actions/cryptoLoan`
  | `/dashbaord/actions/fiatLoan`
  | `/dashbaord/actions/lifestyle/airtime`
  | `/dashbaord/actions/lifestyle/data`
  | `/dashbaord/actions/lifestyle/electricity`
  | `/dashbaord/actions/lifestyle/lifestyle`
  | `/dashbaord/actions/loans`
  | `/dashbaord/actions/request-crypto-loan`
  | `/dashbaord/clyp-news`
  | `/dashbaord/clyphub`
  | `/dashbaord/clyphub-actions`
  | `/dashbaord/drawer`
  | `/dashbaord/home`
  | `/dashbaord/profile/page`
  | `/dashbaord/reports/coin-details-drawer`
  | `/dashbaord/reports/coin-statistics`
  | `/dashbaord/reports/reports-page`
  | `/dashbaord/reports/transaction-logs`
  | `/dashbaord/reports/web3`
  | `/not-found`
  | `/notifications`
  | `/onboarding`
  | `/savings/auto-save`
  | `/savings/auto-save/auto-save-card`
  | `/savings/auto-save/auto-save-details`
  | `/savings/auto-save/auto-save-form`
  | `/savings/safe-lock`
  | `/savings/safe-lock/duration`
  | `/savings/safe-lock/safe-lock-card`
  | `/savings/safe-lock/safe-lock-dashboard`
  | `/savings/safe-lock/safe-lock-details`
  | `/savings/safe-lock/safe-lock-flow`
  | `/savings/safe-lock/safe-lock-form`
  | `/savings/safe-lock/safe-lock-preview`
  | `/savings/safe-lock/safe-lock-success`
  | `/savings/safe-lock/withdraw-safe-lock-drawer`
  | `/savings/success`

export type Params = {
  
}

export type ModalPath = never

export const { Link, Navigate } = components<Path, Params>()
export const { useModals, useNavigate, useParams } = hooks<Path, Params, ModalPath>()
export const { redirect } = utils<Path, Params>()
