{"name": "clyppay-webapp-new", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview1": "vite preview", "type-check": "tsc --noEmit", "preview": "npm run type-check && vite build && vite preview", "serve-heroku": "vite --host 0.0.0.0 --port $PORT preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^4.1.3", "@hugeicons/react": "^1.0.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@tailwindcss/vite": "^4.0.7", "@tanstack/react-query": "^5.67.2", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.4", "framer-motion": "^12.4.7", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-datepicker": "^8.3.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-ga4": "^2.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-idle-timer": "^5.7.2", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.2.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.7", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-router-dom": "^5.3.3", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}